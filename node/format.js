/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const fs = require('fs');
const path = require('path');
const adCodeMap = require('./source/csvJson.json');
const jsonData_330702 = require('./target/330702_full.json');
const jsonData_330703 = require('./target/330703_full.json');
const jsonData_330723 = require('./target/330723_full.json');
const jsonData_330726 = require('./target/330726_full.json');
const jsonData_330727 = require('./target/330727_full.json');
const jsonData_330781 = require('./target/330781_full.json');
const jsonData_330782 = require('./target/330782_full.json');
const jsonData_330783 = require('./target/330783_full.json');
const jsonData_330784 = require('./target/330784_full.json');

/**
 * 对乡镇地图数据填充必要字段
 *  */

const formatFun = (adcode, jsonData) => {
  const codeMap = adCodeMap[adcode];

  jsonData.features.forEach(val => {
    // 层级必填
    val.properties.level = 'town';
    // 自身adcode必填
    val.properties.adcode =
      codeMap
        .map(item => {
          const key = Object.keys(item)[0];
          const value = Object.values(item)[0];
          if (key.indexOf(val.properties.zldwmc) >= 0) {
            return value;
          }
        })
        .filter(v => v)[0] * 1;
    // 父级adcode必填
    val.properties.parent = { adcode };
    val.properties.name = val.properties.zldwmc;
  });

  fs.writeFile(
    path.join(__dirname, `/format/${adcode}_full.json`),
    JSON.stringify(jsonData),
    { encoding: 'utf-8' },
    _err => {
      if (_err) {
        console.log('file_err', _err);
      }
    },
  );
};

[
  { data: jsonData_330702, adcode: 330702 },
  { data: jsonData_330703, adcode: 330703 },
  { data: jsonData_330723, adcode: 330723 },
  { data: jsonData_330726, adcode: 330726 },
  { data: jsonData_330727, adcode: 330727 },
  { data: jsonData_330781, adcode: 330781 },
  { data: jsonData_330782, adcode: 330782 },
  { data: jsonData_330783, adcode: 330783 },
  { data: jsonData_330784, adcode: 330784 },
].forEach(({ data, adcode }) => {
  formatFun(adcode, data);
});
