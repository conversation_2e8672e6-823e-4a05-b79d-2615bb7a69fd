/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const fs = require('fs');
const path = require('path');
const adCodeMap = require('./source/csvJson.json');
// const jsonData = require('./target/330702_full.json');
// const jsonData = require('./target/330703_full.json');
// const jsonData = require('./target/330723_full.json');
// const jsonData = require('./target/330726_full.json');
// const jsonData = require('./target/330727_full.json');
// const jsonData = require('./target/330781_full.json');
// const jsonData = require('./target/330782_full.json');
// const jsonData = require('./target/330783_full.json');
const jsonData = require('./target/330784_full.json');

/**
 * 对乡镇地图数据填充必要字段
 *  */

const formatFun = adcode => {
  const codeMap = adCodeMap[adcode];

  jsonData.features.forEach(val => {
    // 层级必填
    val.properties.level = 'town';
    // 自身adcode必填
    val.properties.adcode = codeMap
      .map(item => {
        const key = Object.keys(item)[0];
        const value = Object.values(item)[0];
        if (key.indexOf(val.properties.zldwmc) >= 0) {
          return value;
        }
      })
      .filter(v => v)[0];
    // 父级adcode必填
    val.properties.parent = { adcode };
  });

  fs.writeFile(
    path.join(__dirname, `/format/${adcode}_full.json`),
    JSON.stringify(jsonData),
    { encoding: 'utf-8' },
    _err => {
      if (_err) {
        console.log('file_err', _err);
      }
    },
  );
};

formatFun(330784);
