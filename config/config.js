/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

require('kbh5-deploy/bin/patch.require');

/* eslint-disable */

import os from 'os';
import defaultSettings from '../src/defaultSettings';
import pageRoutes from './router.config';
import routerAutomatic from './router.automatic.config'; // 自动化分拣路由

const { MODE_ENV, DEBUG_DEV } = process.env;

const publicPath =
  process.env.NODE_ENV === 'production' ? '//cdn-static.kuaidihelp.com/kuaidihelp_city_web/' : '/';

// 路由配置
const routesMap = {
  AUTOMATIC: routerAutomatic,
};
const routes = routesMap[MODE_ENV] || pageRoutes;

const plugins = [
  [
    'umi-plugin-react',
    {
      title: '快递末端企业专用管理系统',
      antd: true,
      dva: {
        hmr: true,
      },
      locale: {
        enable: true, // default false
        default: 'zh-CN', // default zh-CN
        baseNavigator: true, // default true, when it is true, will use `navigator.language` overwrite default
      },
      dynamicImport: {
        loadingComponent: './components/PageLoading/index',
      },
      ...(!process.env.TEST && os.platform() === 'darwin'
        ? {
            dll: {
              include: ['dva', 'dva/router', 'dva/saga', 'dva/fetch'],
              exclude: [
                '@babel/runtime',
                'kbh5-deploy',
                'antd-pro-merge-less',
                'antd-theme-webpack-plugin',
                '@ant-design/pro-components',
              ],
            },
          }
        : {}),
    },
  ],
];

export default {
  define: {
    APP_TYPE: process.env.APP_TYPE || '',
    'process.env.MODE_ENV': MODE_ENV,
    'process.env.DEBUG_DEV': DEBUG_DEV,
  },
  plugins,
  externals: {
    '@kb/poster': 'poster',
    '@kb/brands': 'brands',
    '@antv/data-set': 'DataSet',
    '@kb/citys': 'address',
  },
  ignoreMomentLocale: true,
  routes,
  theme: {
    'primary-color': defaultSettings.primaryColor,
    'font-size-base': '14px',
    'badge-font-size': '12px',
    'btn-font-size-lg': '@font-size-base',
    'menu-dark-bg': '#00182E',
    'menu-dark-submenu-bg': '#000B14',
    'layout-sider-background': '#00182E',
    'layout-body-background': '#f0f2f5',
  },
  lessLoaderOptions: {
    javascriptEnabled: true,
  },
  // 关闭分包
  // disableDynamicImport: true,
  // 分包引用路径设置
  publicPath,
  disableRedirectHoist: true,
  cssnano: {
    mergeRules: false,
  },
  hash: true,
  targets: {
    ie: 10,
  },
};

/* eslint-enable */
