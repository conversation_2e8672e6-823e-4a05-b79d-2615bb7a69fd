/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

module.exports = [
  {
    path: '/m',
    component: '../layouts/m',
    hideInMenu: true,
    routes: [
      {
        path: '/m',
        redirect: '/m/home',
      },
      {
        name: '首页',
        path: '/m/home',
        isInTab: true,
        navigator: false,
        component: './m/home',
        icon: 'home',
      },
      {
        name: '我的',
        path: '/m/user',
        isInTab: true,
        navigator: false,
        component: './m/user',
        icon: 'user',
      },
      {
        name: '暂无权限',
        path: '/m/no-permission',
        navigator: false,
        component: './m/no-permission',
      },
      {
        name: '共配充值',
        path: '/m/recharge',
        component: './m/recharge',
      },
      {
        name: '充值记录',
        path: '/m/recharge/record',
        component: './m/recharge/record',
      },
      {
        name: '收银台',
        path: '/m/recharge/cashier',
        component: './m/recharge/cashier',
      },
      {
        name: '跳转提示',
        path: '/m/recharge/redirect',
        component: './m/recharge/redirect',
      },
      {
        name: '支付结果',
        path: '/m/recharge/result',
        component: './m/recharge/result',
      },
      {
        name: '告警处理',
        path: '/m/error-handle',
        component: './m/error-handle',
        navigator: false,
      },
      {
        name: '测试',
        path: '/m/test',
        component: './m/test',
      },
    ],
  },
  {
    path: '/league',
    component: '../layouts/StatiscLayout',
    routes: [
      {
        name: '数据统计',
        path: '/league/statistics',
        component: './Statistics',
      },
      {
        name: '数据统计',
        path: '/league/chart',
        component: './Statistics/RealPage',
      },
      {
        name: '数据统计',
        path: '/league/chart-yz',
        component: './Statistics/yzPage',
      },
      {
        component: '404',
      },
    ],
  },
  {
    path: '/dashboard',
    component: '../layouts/BigDataLayout',
    routes: [
      {
        path: '/dashboard/:id/:platform?',
        name: '数据大屏',
        component: './Post/BigScreen',
      },
    ],
  },
  {
    path: '/video',
    component: '../layouts/BigDataLayout',
    routes: [
      {
        path: '/video/:id/:platform?',
        name: '监控视频',
        component: './Post/BigScreen/Video',
      },
    ],
  },
  {
    path: '/user',
    component: '../layouts/UserLayout',
    routes: [
      {
        name: '登录',
        path: '/user/login',
        component: './User/Login',
      },
      {
        name: '注册',
        path: '/user/register',
        component: './User/Register',
      },
      {
        name: '忘记密码',
        path: '/user/forget',
        component: './User/Register',
      },
      {
        name: '数据统计',
        path: '/user/league/statistics',
        component: './Statistics',
      },
      {
        component: '404',
      },
    ],
  },
  {
    path: '/help',
    component: '../layouts/ArticleLayout',
    routes: [
      {
        name: '服务协议',
        path: '/help/service-pro',
        component: './Help/ServicePro',
      },
      {
        name: '快宝共配软件服务协议',
        path: '/help/GpAgreement',
        component: './Help/GpAgreement',
      },
      {
        name: '云存款拍照功能开通及续费协议',
        path: '/help/CloudStorage',
        component: './Help/CloudStorage',
      },
      {
        name: '快宝云打印',
        path: '/help/KbPintBox',
        component: './Help/KbPintBox',
      },
      {
        name: '注册服务条款',
        path: '/help/register',
        component: './Help/Register',
      },
      {
        name: '圆通行者验证',
        path: '/help/verify',
        component: './Help/Verify',
      },
      {
        component: '404',
      },
    ],
  },
  {
    path: '/',
    component: '../layouts/BasicLayout',
    routes: [
      { path: '/', redirect: '/system/info' },
      {
        name: '快宝驿站',
        icon: 'bank',
        path: '/post',
        authority: ['post', ['yjy', 'cctjs', 'khy']],
        // 目前只用到[0],根据info中的role判断权限authority
        routes: [
          {
            name: '超级搜索',
            path: '/post/advanced',
            component: './Post/Advanced',
            authority: ['post/advanced'],
          },
          {
            name: '站点管理',
            name_yz: '驿站管理',
            path: '/post/area',
            component: './Post/Area',
          },
          {
            hideInMenu: true,
            path: '/post/area/postDetail',
            name: '详情',
            component: './Post/PostDetail',
          },
          {
            name: '库存记录',
            path: '/post/inventoryManagement',
            component: './Post/InventoryManagement',
          },
          {
            name: '重传失败单号',
            path: '/post/retransmit',
            component: './Post/Retransmit',
            authority: ['post/retransmit'],
          },
          {
            name: '数据统计',
            path: '/post/list',
            component: './Post/List',
          },
          {
            name: '服务费与保管费',
            path: '/post/dispat',
            component: './Post/Dispat',
            userInfoAccess: ['company'],
            authority: ['post/dispat'],
          },
          {
            name: '快递费管理',
            path: '/post/express',
            component: './Post/Express',
            hideByKey: ['cctjs', 'khy'],
            authority: ['post/express'],
          },
          {
            name: '投诉与黑名单',
            path: '/post/complantBlackList',
            component: './Post/ComplantBlackList',
            authority: ['post/complantBlackList'],
          },
          {
            name: '数据导出',
            path: '/post/export',
            component: './Post/CreateReports',
            authority: ['post/export'],
          },
          {
            name: '设置和管理',
            path: '/post/setup',
            component: './Post/Setup/index',
            hideByKey: ['yjy', 'khy'],
            userInfoAccess: ['quickLoginType_post_cctjs'],
            // 中邮 村村通只有代入库菜单，无代入库权限不显示菜单
            authority: ['post/setup'],
          },
          // 仅供圆通 企微快捷登录
          // Tower 任务: 0416 圆通定制新零售增加代入库设置页面 ( https://tower.im/teams/258300/todos/113267 )
          {
            name: '代入库',
            path: '/post/mixInStorage',
            component: './Post/mixInStorage/index',
            hideByKey: ['yjy', 'khy', 'cctjs', 'post', 'sy', 'td', 'qhd'],
            authority: ['mixInStorage'],
          },
          {
            name: '驿站大屏',
            path: '/post/bigData',
            component: './Post/YzScreenManagement',
            userInfoAccess: ['company', 'branchLevel_post_0,1,2,3'],
            authority: ['post/bigData'],
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '快递柜',
        path: '/Cabinet',
        icon: 'bank',
        authority: ['Cabinet'],
        routes: [
          {
            name: '下属快递柜',
            path: '/Cabinet/manage',
            component: './Cabinet/manage',
            id: 'kdg_sub',
          },
          {
            name: '数据统计',
            path: '/Cabinet/statistics',
            component: './Cabinet/statistics',
            id: 'kdg_stat',
          },
          {
            name: '收费设置',
            path: '/Cabinet/fee-setting',
            component: './Cabinet/fee-setting',
            id: 'kdg_fee',
          },
          // {
          //   name: '重传失败单号',
          //   path: '/Cabinet/fail-order',
          //   component: './Cabinet/fail-order',
          //   id: 'kdg_retransmit',
          // },
          {
            name: '快递柜分期付款',
            path: '/Cabinet/payment',
            component: './Cabinet/payment',
            authority: ['post/kdg-fee'],
            id: 'kdg_installment',
          },
          {
            path: '/Cabinet',
            redirect: '/Cabinet/manage',
          },
          {
            component: '404',
          },
        ],
      },

      {
        name: '快宝共配',
        icon: 'tool',
        path: '/Allocation',
        authority: ['allocation'],
        hideByKey: ['td', 'qhd'],
        userInfoAccess: ['branchLevel_post_0,1,2,3'],
        component: '../layouts/GpAuthLayout',
        breadcrumbElement: 'span',
        routes: [
          {
            name: '巴枪扫描配置',
            authority: ['allocation/postOption', ['post', 'yjy', 'cctjs', 'khy']],
            path: '/Allocation/PostOption',
            component: './Allocation/PostOption',
            id: 1,
          },
          {
            name: '快件信息下发',
            authority: ['allocation/fastMsg', ['post', 'yjy', 'cctjs', 'khy']],
            path: '/Allocation/fastMsg',
            component: './Fast/FastMsg',
            userInfoAccess: ['company'],
            id: 2,
          },
          {
            name: '批量补件',
            path: '/Allocation/patch',
            component: './Allocation/BatchPatch',
            id: 3,
          },
          {
            name: '共配数据总览',
            path: '/Allocation/OverView',
            component: './Allocation/OverView',
            id: 4,
          },
          {
            name: '巴枪扫描记录',
            path: '/Allocation/GunScanRecord',
            component: './Allocation/GunScanRecord',
            id: 5,
          },
          {
            name: '数据统计',
            authority: ['allocation/statistics', ['post', 'yjy', 'cctjs', 'khy']],
            path: '/Allocation/DataStatistics',
            component: './Allocation/DataStatistics',
            hideByKey: ['yjy'],
            userInfoAccess: ['company'],
            id: 6,
          },
          {
            name: '派费直发',
            path: '/Allocation/Dispat',
            component: './Allocation/Dispat',
            hideByKey: ['post', 'yjy'],
            userInfoAccess: ['company'],
            id: 7,
          },
          {
            name: '数据监控',
            path: '/Allocation/DataMonitor',
            component: './Allocation/DataMonitor',
            hideByKey: ['post', 'yjy'],
            id: 8,
          },
          {
            name: '业务员看板',
            path: '/Allocation/OperatorKanban',
            component: './Allocation/OperatorKanban',
            userInfoAccess: ['company'],
            id: 9,
          },
          {
            name: '自定义扫描',
            authority: ['allocation/customScan', ['post', 'yjy', 'cctjs', 'khy']],
            path: '/Allocation/CustomScan',
            component: './Allocation/CustomScan',
            userInfoAccess: ['company'],
            id: 10,
          },
          {
            name: '集包记录',
            path: '/Allocation/BundleRecord',
            component: './Allocation/BundleRecord',
            hideByKey: ['yz', 'post', 'yjy', 'sy'],
            id: 11,
          },
          {
            name: '交付集包统计',
            path: '/Allocation/BundleDeliverRecord',
            component: './Allocation/BundleDeliverRecord',
            hideByKey: ['yz', 'post', 'yjy', 'sy'],
            id: 12,
          },
          {
            name: '共配费用明细',
            path: '/Allocation/costDetail',
            component: './Allocation/costDetail',
            userInfoAccess: ['branchLevel_post_3'],
            hideByKey: ['yz', 'yjy', 'td', 'cctjs', 'khy', 'qhd', 'sy'],
            id: 13,
          },
          {
            name: '底单云存储',
            path: '/Allocation/cloud_storage',
            component: './Allocation/cloud_storage',
            hideByKey: ['post', 'yjy', 'sy'],
            userInfoAccess: ['company'],
            id: 14,
          },
          {
            name: '设置',
            path: '/Allocation/setting',
            component: './Allocation/setting',
            hideByKey: ['yjy'],
            userInfoAccess: ['company', 'branchLevel_post_3'],
            id: 15,
          },
          {
            name: '共配权限管理',
            authority: ['allocation/authSet'],
            path: '/Allocation/AuthorityManagement',
            component: './Allocation/AuthorityManagement',
            hideByKey: ['yz', 'sy'],
            userInfoAccess: ['company'],
            id: 16,
          },
          {
            name: '共配加速器',
            path: '/Allocation/PrintKey',
            component: './Allocation/PrintKey',
            // hideByKey: ['sy'],
            id: 17,
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '自动化',
        icon: 'setting',
        authority: ['automatic'],
        hideByKey: ['td', 'cctjs', 'khy', 'qhd'],
        path: '/automatic',
        routes: [
          {
            name: '分拣线配置',
            path: '/automatic/sortLineConfiguration',
            component: './Automatic/SortLineConfiguration',
            id: 18,
          },
          {
            name: '格口配置',
            path: '/automatic/gridConfiguration',
            component: './Automatic/GridConfiguration',
            id: 19,
          },
          {
            name: '地址方案',
            path: '/automatic/AddressSort',
            component: './Automatic/AddressSort',
            id: 20,
          },
          {
            name: '进港分拣',
            path: '/automatic/arrivalSorting',
            component: './Automatic/ArrivalSorting',
            id: 21,
          },
          {
            name: '格口统计',
            path: '/automatic/gridStatistics',
            component: './Automatic/GridStatistics',
            id: 22,
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '快宝同城',
        icon: 'global',
        path: '/business',
        authority: ['business'],
        hideByKey: ['td', 'cctjs', 'khy', 'qhd', 'sy'],
        routes: [
          {
            name: '单号设置',
            path: '/business/orderNumSet',
            component: './Business/OrderNumSet',
          },
          {
            name: '同城订单',
            path: '/business/order',
            component: './Business/Order',
          },
          {
            name: '扫描记录统计',
            path: '/business/scanning',
            component: './Business/Scanning',
          },
          {
            name: '扫描记录查询',
            path: '/business/query',
            component: './Business/Query',
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '即时配送',
        icon: 'coffee',
        path: '/delivery',
        authority: ['delivery'],
        hideByKey: ['td', 'cctjs', 'khy', 'qhd', 'sy'],
        routes: [
          {
            name: '配送员设置',
            path: '/delivery/setting',
            component: './Delivery/Setting',
          },
          {
            name: '配送费计价',
            path: '/delivery/valuation',
            component: './Delivery/Valuation',
          },
          {
            name: '配送范围',
            path: '/delivery/scope',
            component: './Delivery/Scope',
          },
          {
            name: '订单调度',
            path: '/delivery/allot',
            component: './Delivery/Allot',
          },
          {
            name: '历史订单',
            path: '/delivery/history',
            component: './Delivery/History',
          },
          {
            name: '订单数据',
            path: '/delivery/order',
            component: './Delivery/Order',
          },
          {
            name: '配送费结算',
            path: '/delivery/cost',
            component: './Delivery/Cost',
          },
        ],
      },
      {
        name: '寄件管理',
        icon: 'file-protect',
        path: '/order',
        authority: ['orders', ['yjy', 'cctjs', 'khy']],
        hideByKey: ['cctjs', 'khy', 'sy'],
        routes: [
          {
            name: '订单概览',
            path: '/order/worktop',
            component: './Order/Worktop',
          },
          {
            name: '订单明细',
            path: '/order/orderDetails',
            component: './Order/OrderDetails',
          },
          {
            name: '代收货款管理',
            path: '/order/payment',
            component: './Order/Payment',
          },
          {
            name: '单号源设置',
            path: '/order/setter',
            component: './Order/Setter',
          },
          {
            name: '单号使用记录',
            path: '/order/record',
            component: './Order/Record',
          },
          {
            name: '报价单设置',
            path: '/order/finance',
            component: './Order/Finance',
          },
          {
            name: '导出对账单',
            path: '/order/export',
            component: './Order/ExportOrder',
          },
          {
            name: '新建报价单',
            path: '/order/finance/finance_add',
            component: './Order/Finance/detail/addNew',
            hideInMenu: true,
          },
          {
            name: '编辑报价单',
            path: '/order/finance/finance_edit',
            component: './Order/Finance/detail/editDetail',
            hideInMenu: true,
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '微信管理',
        icon: '#wechat',
        authority: ['platform', ['yjy', 'cctjs', 'khy']],
        path: '/platform',
        hideByKey: ['sy'],
        routes: [
          {
            name: '数据中心',
            path: '/platform/datacenter',
            component: './Platform/DataCenter',
          },
          {
            name: '群发推送',
            path: '/platform/masspush',
            component: './Platform/MassPush',
          },
          {
            name: '二维码',
            path: '/platform/qrcode',
            component: './Platform/QrCode',
          },
          {
            name: '设置',
            path: '/platform/setting',
            component: './Platform/Setting',
          },
        ],
      },
      {
        name: '工单系统',
        icon: 'desktop',
        path: '/workOrder',
        authority: ['workOrder'],
        component: './WorkOrder/WorkOrder',
        hideByKey: ['td', 'cctjs', 'khy', 'qhd', 'sy', 'post'],
      },
      {
        name: '报表下载',
        icon: 'download',
        authority: ['downloadReport', ['cctjs', 'khy']],
        path: '/downloadReport',
        component: './Post/Report',
      },
      {
        name: '网站导航',
        icon: 'desktop',
        path: '/Guide',
        component: './Guide',
        hideInMenu: true,
      },
      {
        name: '数据开放平台',
        icon: 'cloud-server',
        path: 'https://open.tongdiyiyou.com',
        target: '_blank',
        authority: ['fund'],
        hideByKey: ['yz', 'post', 'yjy', 'cctjs', 'khy', 'qhd', 'sy'],
      },
      {
        name: '快宝云店',
        icon: 'tag',
        path: 'https://mall.tongdiyiyou.com',
        target: '_blank',
        authority: ['fund'],
        hideByKey: ['yz', 'post', 'yjy', 'cctjs', 'khy', 'qhd', 'sy'],
      },
      {
        name: '智能硬件',
        icon: 'printer',
        path: '/help/KbPintBox',
        target: '_blank',
        authority: ['fund'],
        hideByKey: ['yz', 'post', 'yjy', 'cctjs', 'khy', 'qhd', 'sy'],
      },
      {
        name: '资金账户',
        icon: 'wallet',
        path: '/finance/account',
        authority: ['finance'],
        component: '../layouts/PermissionLayout',
        routes: [
          {
            component: './Finance/Account',
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '财务管理',
        icon: 'pay-circle',
        path: 'https://cityfinance.kuaidihelp.com',
        target: '_blank',
        authority: ['fund'],
        hideByKey: ['td', 'cctjs', 'khy', 'qhd', 'sy'],
      },
      {
        name: '无人车',
        icon: 'car',
        path: '/truck',
        authority: ['truck'],
        hideByKey: ['post', 'yjy', 'td', 'cctjs', 'khy', 'qhd', 'sy'],
        routes: [
          {
            name: '停靠点',
            path: '/truck/station',
            component: './Truck/Station',
          },
          {
            name: '车辆信息',
            path: '/truck/info',
            component: './Truck/Info',
          },
          {
            name: '设置',
            path: '/truck/setting',
            component: './Truck/Setting',
          },
        ],
      },
      {
        name: '基础数据',
        icon: 'database',
        path: '/system',
        authority: ['system', ['yjy', 'cctjs', 'khy']],
        routes: [
          {
            name: '片区',
            path: './business/station',
            component: './Business/Station',
            hideByKey: ['post'],
            userInfoAccess: ['company'],
            id: 23,
          },
          {
            name: '网点信息',
            path: './business/outlets',
            component: './Business/Outlets',
            id: 24,
          },
          {
            name: '业务员',
            path: './business/operator',
            component: './Business/Operator',
            id: 25,
          },
          {
            name: '公司信息',
            path: '/system/info',
            component: './System/Info',
            userInfoAccess: ['company'],
            id: 26,
          },
          {
            name: '客户管理',
            path: './business/client',
            component: './Business/Client',
            hideByKey: ['yjy', 'cctjs', 'khy', 'sy'],
            userInfoAccess: ['company'],
            id: 27,
          },
          {
            name: '车辆信息',
            path: './Business/Car',
            component: './Business/Car',
            hideByKey: ['yz', 'post', 'yjy', 'td', 'qhd', 'sy'],
            userInfoAccess: ['company'],
            id: 28,
          },
          {
            name: '公交时刻表',
            path: './business/busSchedule',
            component: './Business/BusSchedule',
            hideByKey: ['yz', 'post', 'yjy', 'td', 'qhd', 'sy'],
            userInfoAccess: ['company'],
            id: 29,
          },
          {
            name: '员工信息卡',
            path: './business/staff',
            component: './Business/Staff',
            hideByKey: ['yz', 'post', 'yjy', 'td', 'qhd', 'sy'],
            id: 30,
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '平台设置',
        icon: 'setting',
        path: '/set',
        routes: [
          {
            name: '安全设置',
            path: '/set/safe',
            component: './System/ModifyPassword',
            authority: ['set/safe'],
          },
          {
            name: '账号管理',
            path: '/set/accounts',
            component: './Set/Accounts',
            authority: ['account'],
          },
          {
            component: '404',
          },
        ],
      },
      {
        name: '新闻中心',
        icon: 'profile',
        path: '/news',
        authority: ['system', ['yjy', 'cctjs', 'khy']],
        hideInMenu: true,
        routes: [
          {
            component: './News/News',
          },
          {
            component: '404',
          },
        ],
      },
      {
        component: '404',
      },
    ],
  },
];
