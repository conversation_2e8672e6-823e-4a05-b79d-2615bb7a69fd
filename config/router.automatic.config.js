/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// import moduleName from 'module'
module.exports = [
  {
    path: "/",
    component: "../layouts/BasicLayout",
    routes: [
      { path: "/", redirect: "/automatic/arrivalSorting" },
      {
        name: "自动化",
        icon: "setting",
        path: "/automatic",
        routes: [
          {
            name: "进港分拣",
            path: "/automatic/arrivalSorting",
            component: "./Automatic/ArrivalSorting",
          },
          {
            name: "格口统计",
            path: "/automatic/gridStatistics",
            component: "./Automatic/GridStatistics",
          },
          {
            component: "404",
          },
        ],
      },
    ],
  },
];
