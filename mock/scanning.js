/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  getScanningList,
  getSingleList,
  getWorkList
} from "./scanningList.js";


const proxy ={
  //扫描记录统计
  "POST /v1/LocalCity/QueryScanRecord/getRecordTj": getScanningList,
  //扫描记录查询单号
  "POST /v1/LocalCity/QueryScanRecord/getRecordByWaybillNo": getSingleList,
  //扫描记录查询业务员
  "POST /v1/LocalCity/QueryScanRecord/getRecordByCn": getWorkList,
}

export default proxy;
