/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  getYzList,
  getExportTask,
  getReport,
  regeneration,
  getReportListType,
  getReportType,
} from './reportList';

const proxy = {
  // 驿站列表
  'POST /Api/YZ/CourierStation/getSubDaksList': getYzList,
  // 生成报表
  'POST /Api/YZ/StockManage/createExportTask': getExportTask,
  // 区域排名，生成报表
  'POST /Api/YZ/ExcelTask/createRankExportTask': getExportTask,
  // 导出报表
  'POST /Api/YZ/StockManage/exportTaskList': getReport,
  // 数据统计导出报表
  'POST /Api/YZ/ExcelTask/create': getReport,
  // 数据统计，重新生成报表
  'POST /Api/YZ/StockManage/retryExport': regeneration,
  // 数据统计，获取报表下载类型
  'POST /v1/ReportDownload/getReportOptions': getReportListType,
  // 数据统计，获取报表下载分类
  'POST /v1/ReportDownload/getClassifyOptions': getReportType,
};

export default proxy;
