/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Request, Response } from 'express';
import { responseSuccess } from './utils';
import Mock from 'mockjs';
import { mockResponse } from './_utils';
const { mock } = Mock;

export default {
  'POST /Api/CabinetCredit/cabinetCreditList': (req: Request, res: Response) => {
    res.send({
      "code": 0,
      "msg": "成功",
      "data": {
          "count": "1",
          "list": [
              {
                  "id": "33",
                  "contract_number": "21241231004",
                  "kb_id": "377177545",
                  "kb_type": "yz",
                  "cm_id": "6666753",
                  "cabinet_id": "211",
                  "cabinet_t_kb_id": "8139",
                  "cabinet_t_kb_type": "city",
                  "total_money": "2.00",
                  "first_paid_money": "1.00",
                  "credit_money": "1.00",
                  "paid_money": "0.00",
                  "surplus_credit_money": "1.00",
                  "deduct_date": "0000-00-00",
                  "deduct_frequency": "day",
                  "deduct_standard": "deliver_number",
                  "deduct_number": "0",
                  "single_deduct_money": "0.10",
                  "start_date": "2025-01-01",
                  "end_date": "",
                  "operator": "郝君君",
                  "status": "0",
                  "create_time": "2024-12-31 15:58:38",
                  "update_time": "2024-12-31 15:58:38",
                  "share_id": "5",
                  "share_t_kb_id": "8139",
                  "share_t_kb_type": "city",
                  "share_type": "share_fix_amount",
                  "share_ratio": "0.01",
                  "share_money": "100",
                  "share_status": "0",
                  "share_start_date": "2025-01-01",
                  "share_end_date": "2025-01-09",
                  "cabinet_payee": "city",
                  "share_payee": "city",
                  "city_name": "新零售加盟商测试dxx",
                  "cabinet_name": "test编号测试一下",
                  "inn_phone": "19521405164",
                  "inn_name": "dxx小号测",
                  "deduct_time": "每日凌晨2点",
                  "once_pay_amount": "￥0.10*投件量",
                  payer_role: "加盟商"
              }
          ]
      }
  })
  return
    return mockResponse(
      {
        id: '@id',
        contract_number: /\d{10}/,
        kb_id: /^1[3-9]\d{9}$/,
        kb_type: 'yz',
        'cabinet_id|1': [1, 2, 3, 4, 5],
        total_money: '100.00',
        first_paid_money: '2.00',
        credit_money: '98.00',
        paid_money: '0.00',
        surplus_credit_money: '98.00',
        single_deduct_money: '50.00',
        start_date: '@now("yyyy-MM-dd")',
        end_date: '@now("yyyy-MM-dd")',
        operator: '@cname',
        status: '0',
        create_time: '@now()',
        update_time: '@now()',
        cabinet_name: '@cname',
        inn_phone: /1\d{10}/,
        inn_name: '@cname',
      },
      {
        total: 30,
      },
    )(req, res);
  },
  'POST /Api/CabinetCredit/creditHisPayList': (req: Request, res: Response) => {
    return mockResponse(
      {
        kb_id: '67892029',
        kb_type: 'yz',
        inner_trade_no: '',
        outer_trade_no: '',
        money: '5.00',
        pay_method: 'balance',
        status: '1',
        surplus_credit_money: '8.00',
        date: '2024-11-22',
        create_time: '2024-11-22 18:20:57',
      },
      {
        total: 30,
      },
    )(req, res);
  },
  'POST /Api/CabinetCredit/cabinetSharePayList': (req: Request, res: Response) => {
    return mockResponse(
      {
        kb_id: '67892029',
        kb_type: 'yz',
        inner_trade_no: '',
        outer_trade_no: '',
        money: '5.00',
        pay_method: 'balance',
        status: '2',
        surplus_credit_money: '8.00',
        date: '2024-11-22',
        create_time: '2024-11-22 18:20:57',
      },
      {
        total: 30,
      },
    )(req, res);
  },

};
