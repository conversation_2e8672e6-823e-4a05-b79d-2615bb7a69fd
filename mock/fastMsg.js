/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  "POST /Api/Waybill/specialList": (req, res) => {
    res.send({
      msg: "成功",
      code: 0,
      data: {
      	list: [
	      	{
	      		"id":"5",
            "shop_id":"148",
            "waybill_no":"34542423",
            "type":"1",
            "amount":"43.00",
            "create_at":"2020-02-27 22:09:12",
            "type_str":"到付件"
	      	},
	      	{
	      		"id":"3",
            "shop_id":"148",
            "waybill_no":"4563456345634",
            "type":"0",
            "amount":"4.00",
            "create_at":"2020-02-22 19:28:04",
            "type_str":"拦截件"
	      	},
	      	{
	      		"id":"1",
            "shop_id":"148",
            "waybill_no":"4563456345634",
            "type":"0",
            "amount":"4.00",
            "create_at":"2020-02-22 19:28:04",
            "type_str":"代收货款件"
	      	},
	      ],
        total: '3000',
  	    page: req.body.page,
  	  }
    });
  },
  "POST /Api/Waybill/specialDel": (req, res) => {
    res.send({
      "code":0,
	    "msg":"成功",
	    "data":true
	});
  },
  "POST /Api/Waybill/specialAdd": (req, res) => {
    res.send({
      "code": 0,
	    "msg": "成功",
      // "msg"： '拦截件单号1、单号2、单号3已存在，无法录入',
	    "data":true,
	  });
  },
}