/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  "POST /Api/YZ/CourierStation/getExpress": (req, res) => {
    res.send({
      code: 0,
      msg: "\u6210\u529f",
      data: {
        waybill_number: "7700856101198",
        brand_key: "sto",
        brand_phone: "",
        remarks: "",
        status: "signed",
        list: [
          {
            time: 1541579608,
            status: 1,
            date: "16:33<br/>2018-11-07",
            info:
              "\u4e0a\u6d77\u8679\u6865\u516c\u53f8\u5df2\u7b7e\u6536,\u7b7e\u6536\u4eba\u662f\u672c\u4eba\u7b7e\u6536"
          },
          {
            time: 1541547763,
            status: 0,
            date: "07:42<br/>2018-11-07",
            info:
              "\u4e0a\u6d77\u8679\u6865\u516c\u53f8 \u7684\u6d3e\u4ef6\u5458 \u4e8c\u90e8\u5efa\u6ed4\u5e7f\u573a2208 \u6b63\u5728\u6d3e\u4ef6"
          },
          {
            time: 1541545063,
            status: 0,
            date: "06:57<br/>2018-11-07",
            info:
              "\u5feb\u4ef6\u5df2\u5230\u8fbe \u4e0a\u6d77\u8679\u6865\u516c\u53f8"
          },
          {
            time: 1541529323,
            status: 0,
            date: "02:35<br/>2018-11-07",
            info:
              "\u7531\u4e0a\u6d77\u4e2d\u8f6c\u90e8 \u53d1\u5f80 \u4e0a\u6d77\u8679\u6865\u516c\u53f8"
          },
          {
            time: 1541527557,
            status: 0,
            date: "02:05<br/>2018-11-07",
            info:
              "\u5feb\u4ef6\u5df2\u5230\u8fbe \u4e0a\u6d77\u4e2d\u8f6c\u90e8"
          },
          {
            time: 1541510581,
            status: 0,
            date: "21:23<br/>2018-11-06",
            info:
              "\u7531\u6d59\u6c5f\u676d\u5dde\u4e2d\u8f6c\u90e8 \u53d1\u5f80 \u4e0a\u6d77\u4e2d\u8f6c\u90e8"
          },
          {
            time: 1541508815,
            status: 0,
            date: "20:53<br/>2018-11-06",
            info:
              "\u5feb\u4ef6\u5df2\u5230\u8fbe \u6d59\u6c5f\u676d\u5dde\u4e2d\u8f6c\u90e8"
          },
          {
            time: 1541501589,
            status: 0,
            date: "18:53<br/>2018-11-06",
            info:
              "\u7531\u6d59\u6c5f\u676d\u5dde\u6ee8\u6c5f\u897f\u5174\u516c\u53f8 \u53d1\u5f80 \u6d59\u6c5f\u676d\u5dde\u4e2d\u8f6c\u90e8"
          },
          {
            time: 1541500431,
            status: 0,
            date: "18:33<br/>2018-11-06",
            info:
              "\u7531\u6d59\u6c5f\u676d\u5dde\u6ee8\u6c5f\u897f\u5174\u516c\u53f8 \u53d1\u5f80 \u6d59\u6c5f\u676d\u5dde\u4e2d\u8f6c\u90e8"
          },
          {
            time: 1541500431,
            status: 0,
            date: "18:33<br/>2018-11-06",
            info:
              "\u6d59\u6c5f\u676d\u5dde\u6ee8\u6c5f\u897f\u5174\u516c\u53f8-\u5df2\u8fdb\u884c\u88c5\u888b\u626b\u63cf"
          },
          {
            time: 1541500139,
            status: 0,
            date: "18:28<br/>2018-11-06",
            info:
              "\u6d59\u6c5f\u676d\u5dde\u6ee8\u6c5f\u897f\u5174\u516c\u53f8 \u7684\u6536\u4ef6\u5458 \u6d59\u6c5f\u676d\u5dde\u6ee8\u6c5f\u897f\u5174\u516c\u53f8\u5df2\u6536\u4ef6"
          }
        ],
        day_consumed: "\u5171\u8ba1\u8017\u65f61\u5929"
      }
    });
  },
  //代收货款管理，物流信息
  "POST /Api/orderManage/CollectionOfPayment/express": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: {
        "no":"75354818456096",
        "brand":"zt",
        "status":"signed",
        "order":"asc",
        "data":[
          {
              "time":"2020-05-24 18:50:57",
              "context":"【南台】（18242226199、13154252506） 的 廉兴业（13154252506） 已揽收",
              "status":"collected",
              "shop_info":{
                  "shop_name":"南台",
                  "shop_code":"240044",
                  "shop_phone":"18242226199、13154252506"
              }
          },
          {
              "time":"2020-05-24 20:53:10",
              "context":"快件离开 【南台】 已发往 【盘锦中转】",
              "status":"sending",
              "shop_info":{
                  "shop_name":"南台",
                  "shop_code":"240044",
                  "shop_phone":"18242226199、13154252506"
              }
          },
          {
              "time":"2020-05-24 22:40:37",
              "context":"快件已经到达 【盘锦中转】",
              "status":"sending",
              "shop_info":{
                  "shop_name":"盘锦中转",
                  "shop_code":"42700",
                  "shop_phone":"0427-3338915"
              }
          },
          {
              "time":"2020-05-24 22:40:39",
              "context":"快件离开 【盘锦中转】 已发往 【北京】",
              "status":"sending",
              "shop_info":{
                  "shop_name":"盘锦中转",
                  "shop_code":"42700",
                  "shop_phone":"0427-3338915"
              }
          },
          {
              "time":"2020-05-25 08:53:09",
              "context":"快件已经到达 【北京】",
              "status":"sending",
              "shop_info":{
                  "shop_name":"北京",
                  "shop_code":"01001",
                  "shop_phone":"010-86483232"
              }
          },
          {
              "time":"2020-05-25 09:37:10",
              "context":"快件离开 【北京】 已发往 【北京朝阳亮马桥】",
              "status":"sending",
              "shop_info":{
                  "shop_name":"北京",
                  "shop_code":"01001",
                  "shop_phone":"010-86483232"
              }
          },
          {
              "time":"2020-05-25 10:15:39",
              "context":"快件已经到达 【北京朝阳亮马桥】",
              "status":"sending",
              "shop_info":{
                  "shop_name":"北京朝阳亮马桥",
                  "shop_code":"16370",
                  "shop_phone":"010-67448763、15321118629"
              }
          },
          {
              "time":"2020-05-25 14:09:45",
              "context":"【北京朝阳亮马桥】 的王涛（13521263969） 正在第1次派件, 请保持电话畅通,并耐心等待（95720为中通快递员外呼专属号码，请放心接听）",
              "status":"delivering",
              "shop_info":{
                  "shop_name":"北京朝阳亮马桥",
                  "shop_code":"16370",
                  "shop_phone":"010-67448763、15321118629"
              }
          },
          {
              "time":"2020-05-25 14:31:55",
              "context":"快件已送达【菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】】, 如有问题请电联:（13521263969）, 投诉电话:（057126883287）, 感谢您使用中通快递, 期待再次为您服务!",
              "status":"allograph",
              "shop_info":{
                  "shop_name":"菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】",
                  "shop_code":"1033383_2",
                  "shop_phone":"057126883287"
              }
          },
          {
              "time":"2020-05-26 05:46:03",
              "context":"已签收, 签收人凭取货码签收, 如有疑问请电联:（13521263969）, 投诉电话:（057126883287）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】",
              "status":"signed",
              "shop_info":{
                  "shop_name":"菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】",
                  "shop_code":"1033383_2",
                  "shop_phone":"057126883287"
              }
          }
        ],
      }
    });
  },
  //单号记录，物流信息
  "GET /Api/orderManage/WaybillRecord/getExpressInfo": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: [
        {
            "time":"2020-05-24 18:50:57",
            "context":"【南台】（18242226199、13154252506） 的 廉兴业（13154252506） 已揽收",
            "status":"collected",
            "shop_info":{
                "shop_name":"南台",
                "shop_code":"240044",
                "shop_phone":"18242226199、13154252506"
            }
        },
        {
            "time":"2020-05-24 20:53:10",
            "context":"快件离开 【南台】 已发往 【盘锦中转】",
            "status":"sending",
            "shop_info":{
                "shop_name":"南台",
                "shop_code":"240044",
                "shop_phone":"18242226199、13154252506"
            }
        },
        {
            "time":"2020-05-24 22:40:37",
            "context":"快件已经到达 【盘锦中转】",
            "status":"sending",
            "shop_info":{
                "shop_name":"盘锦中转",
                "shop_code":"42700",
                "shop_phone":"0427-3338915"
            }
        },
        {
            "time":"2020-05-24 22:40:39",
            "context":"快件离开 【盘锦中转】 已发往 【北京】",
            "status":"sending",
            "shop_info":{
                "shop_name":"盘锦中转",
                "shop_code":"42700",
                "shop_phone":"0427-3338915"
            }
        },
        {
            "time":"2020-05-25 08:53:09",
            "context":"快件已经到达 【北京】",
            "status":"sending",
            "shop_info":{
                "shop_name":"北京",
                "shop_code":"01001",
                "shop_phone":"010-86483232"
            }
        },
        {
            "time":"2020-05-25 09:37:10",
            "context":"快件离开 【北京】 已发往 【北京朝阳亮马桥】",
            "status":"sending",
            "shop_info":{
                "shop_name":"北京",
                "shop_code":"01001",
                "shop_phone":"010-86483232"
            }
        },
        {
            "time":"2020-05-25 10:15:39",
            "context":"快件已经到达 【北京朝阳亮马桥】",
            "status":"sending",
            "shop_info":{
                "shop_name":"北京朝阳亮马桥",
                "shop_code":"16370",
                "shop_phone":"010-67448763、15321118629"
            }
        },
        {
            "time":"2020-05-25 14:09:45",
            "context":"【北京朝阳亮马桥】 的王涛（13521263969） 正在第1次派件, 请保持电话畅通,并耐心等待（95720为中通快递员外呼专属号码，请放心接听）",
            "status":"delivering",
            "shop_info":{
                "shop_name":"北京朝阳亮马桥",
                "shop_code":"16370",
                "shop_phone":"010-67448763、15321118629"
            }
        },
        {
            "time":"2020-05-25 14:31:55",
            "context":"快件已送达【菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】】, 如有问题请电联:（13521263969）, 投诉电话:（057126883287）, 感谢您使用中通快递, 期待再次为您服务!",
            "status":"allograph",
            "shop_info":{
                "shop_name":"菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】",
                "shop_code":"1033383_2",
                "shop_phone":"057126883287"
            }
        },
        {
            "time":"2020-05-26 05:46:03",
            "context":"已签收, 签收人凭取货码签收, 如有疑问请电联:（13521263969）, 投诉电话:（057126883287）, 您的快递已经妥投。风里来雨里去, 只为客官您满意。上有老下有小, 赏个好评好不好？【请在评价快递员处帮忙点亮五颗星星哦~】",
            "status":"signed",
            "shop_info":{
                "shop_name":"菜鸟的枣营南里20号楼桥旁北侧菜鸟智能柜【自提柜】",
                "shop_code":"1033383_2",
                "shop_phone":"057126883287"
            }
        }
      ]
    });
  },

};
