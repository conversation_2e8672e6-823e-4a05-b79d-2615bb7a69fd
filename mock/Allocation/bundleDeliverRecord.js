/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  'GET /Api/bundleRecord/getTypeList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: ['上行集包', '下行集包'],
    });
  },
  'POST /Api/YZ/CourierStation/getSubDaksList': (req, res) => {
    const list = Array.from({ length: 70 }).map((v, i) => ({
      id: i,
      cm_id: i,
      kb_id: i,
      company_name: `下属驿站${i}`,
      phone: i,
    }));
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  'POST /Api/Site/list': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        result: [
          {
            site_code: '00',
            site_name: '站点01',
            site_id: '1749265',
            site_charge: '王王',
            site_phone: '17196432627',
            is_inn_area: '1',
            is_gp_area: '1',
          },
        ],
        company_info: {
          0: {
            brand_code: '0000',
            brand_name: '快宝同城',
            id: '15',
          },
        },
      },
    });
  },
  'POST /Api/Company/getDrivers': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          driver_name: '王gggg',
          driver_phone: '13661916490',
        },
        {
          driver_name: '快递员01',
          driver_phone: '13661916492',
        },
      ],
    });
  },
  'POST /Api/Company/packageStatistic': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        list: [
          {
            driver_phone: '13661916490',
            driver_name: '王gggg',
            package_type: '1',
            sender: '王司机',
            receiptor: '小马驿站',
            sender_id: '21',
            receiptor_id: '232',
            package_count: '23',
            start_time: '1970/01/01',
            end_time: '1970/01/01',
            package_type_desc: '上行集包',
          },
          {
            driver_phone: '13661916490',
            driver_name: '啊啊啊',
            package_type: '1',
            sender: '测试网点',
            receiptor: '测试驿站',
            sender_id: '1234',
            receiptor_id: '1748871',
            package_count: '1',
            start_time: '1970/01/01',
            end_time: '1970/01/01',
            package_type_desc: '上行集包',
          },
          {
            driver_phone: '13661916490',
            driver_name: '啊啊啊',
            package_type: '2',
            sender: '测试驿站',
            receiptor: '测试网点',
            sender_id: '1748871',
            receiptor_id: '1234',
            package_count: '2',
            start_time: '1970/01/01',
            end_time: '1970/01/01',
            package_type_desc: '下行集包',
          },
        ],
        total: 3,
        page_size: 10,
        page: 1,
      },
    });
  },
};
