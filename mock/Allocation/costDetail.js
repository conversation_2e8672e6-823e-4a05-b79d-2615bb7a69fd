/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  'POST /v1/TotalDistribution/GpFinanceDetails/getFinanceDetailsList2': (req, res) => {
    res.send(mock({
      code: 0,
      msg: 'success',
      data: {
        'list|40': [
          {
            date: '2023-12-12',
            username: '@id',
            count: '1',
            price: '0.1000',
            brands: 'sto',
            total_price: '0.10',
            courier: '潘用伟-15201946772',
          },
        ],
        total: 50,
        page_size: 40,
        page: 1,
      },
    }));
  },
  'POST /v1/TotalDistribution/GpFinanceDetails/getCourierList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '6682',
          company_info_id: '128',
          company_site_id: '1749439',
          courier_kb_id: '90697010',
          kdy_id: '2225420',
          courier_no: '*********',
          courier_code: '000',
          courier_name: '小草科技',
          courier_phone: '15201946803',
          type: '0',
          switch: '1',
          is_waybill_open: '2',
          allowance_count: '-1',
          created_at: '2021-04-26 14:29:38',
          updated_at: '2021-04-26 14:29:38',
          deleted_at: null,
          status: '1',
          user_type: '2',
          is_delivery_pay: '0',
          is_disable: '0',
        },
        {
          id: '83103',
          company_info_id: '128',
          company_site_id: '1749439',
          courier_kb_id: '*********',
          kdy_id: '2775366',
          courier_no: '0054001',
          courier_code: '001',
          courier_name: '测试人',
          courier_phone: '15201946100',
          type: '0',
          switch: '1',
          is_waybill_open: '2',
          allowance_count: '-1',
          created_at: '2022-07-05 17:18:32',
          updated_at: '2022-07-05 17:18:32',
          deleted_at: null,
          status: '1',
          user_type: '2',
          is_delivery_pay: '0',
          is_disable: '0',
        },
        {
          id: '83110',
          company_info_id: '128',
          company_site_id: '1749439',
          courier_kb_id: '*********',
          kdy_id: '2775367',
          courier_no: '*********',
          courier_code: '002',
          courier_name: '测试101',
          courier_phone: '15201946101',
          type: '0',
          switch: '1',
          is_waybill_open: '2',
          allowance_count: '-1',
          created_at: '2022-11-11 13:54:19',
          updated_at: '2022-11-11 13:54:19',
          deleted_at: null,
          status: '1',
          user_type: '2',
          is_delivery_pay: '0',
          is_disable: '0',
        },
        {
          id: '83372',
          company_info_id: '128',
          company_site_id: '0',
          courier_kb_id: '*********',
          kdy_id: '2775383',
          courier_no: '0054000',
          courier_code: '000',
          courier_name: '测试三',
          courier_phone: '15201946102',
          type: '0',
          switch: '1',
          is_waybill_open: '2',
          allowance_count: '-1',
          created_at: '2023-10-13 15:49:31',
          updated_at: '2023-10-13 15:49:31',
          deleted_at: null,
          status: '1',
          user_type: '2',
          is_delivery_pay: '0',
          is_disable: '0',
        },
      ],
    });
  },
  'POST /v1/TotalDistribution/GpFinanceDetails/getFinanceDetailsByDate2': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
};
