/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  // 状态下拉框选项
  'GET /Api/bundleRecord/getStatusList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: ['已装袋', '交付司机', '司机接收', '司机交付', '已完成'],
    });
  },
  'GET /Api/bundleRecord/getTypeList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: ['上行集包', '下行集包'],
    });
  },
  'POST /Api/YZ/CourierStation/getSubDaksList': (req, res) => {
    const list = Array.from({ length: 70 }).map((v, i) => ({
      id: i,
      cm_id: i,
      kb_id: i,
      company_name: `下属驿站${i}`,
      phone: i,
    }));
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  'POST /Api/Site/list': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        result: [
          {
            site_code: '00',
            site_name: '站点01',
            site_id: '1749265',
            site_charge: '王王',
            site_phone: '17196432627',
            is_inn_area: '1',
            is_gp_area: '1',
          },
        ],
        company_info: {
          0: {
            brand_code: '0000',
            brand_name: '快宝同城',
            id: '15',
          },
        },
      },
    });
  },
  'POST /Api/Company/packageList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        list: [
          {
            id: '188',
            package_no: 'WD202312000111',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '2',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: '3',
            status_desc: '交付司机',
            records: [
              {
                time: '12-18 18:24',
                desc: '业务员【潘用伟】集包交付车辆【】',
              },
              {
                time: '12-18 18:23',
                desc: '业务员【潘用伟】集包装袋投递驿站',
              },
            ],
          },
          {
            id: '186',
            package_no: 'YZ202312000074',
            package_type: '2',
            inn_name: '村村通测试',
            site_name: '村村测试',
            status: '2',
            start_addr: '村村通测试',
            end_addr: '村村测试',
            waybill_count: '2',
            status_desc: '交付司机',
            records: [
              {
                time: '12-18 18:51',
                desc: '业务员【村村通测试】集包交付车辆【】',
              },
              {
                time: '12-18 18:51',
                desc: '业务员【村村通测试】集包装袋寄送网点',
              },
            ],
          },
          {
            id: '180',
            package_no: 'WD202312000108',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
          {
            id: '155',
            package_no: 'WD202312000085',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
          {
            id: '152',
            package_no: 'WD202312000082',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
          {
            id: '151',
            package_no: 'WD202312000081',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
          {
            id: '149',
            package_no: 'WD202312000079',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
          {
            id: '137',
            package_no: 'WD202312000072',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '2',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: '2',
            status_desc: '交付司机',
            records: [
              {
                time: '12-18 19:15',
                desc: '业务员【潘用伟】集包交付车辆【】',
              },
              {
                time: '12-18 18:07',
                desc: '业务员【潘用伟】集包交付车辆【】',
              },
              {
                time: '12-18 17:25',
                desc: '业务员【潘用伟】集包装袋投递驿站',
              },
            ],
          },
          {
            id: '129',
            package_no: 'WD202312000069',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
          {
            id: '121',
            package_no: 'WD202312000068',
            package_type: '1',
            inn_name: '村村通测试',
            site_name: '村村通网点',
            status: '0',
            start_addr: '村村通网点',
            end_addr: '村村通测试',
            waybill_count: 0,
            status_desc: '准备',
            records: [],
          },
        ],
        total: 21,
        page_size: 10,
        page: 1,
      },
    });
  },
};
