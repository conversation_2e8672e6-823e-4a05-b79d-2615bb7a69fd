/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { delay } from 'lodash';
import Mock from 'mockjs';
export default {
  'POST /Api/YZ/Fund/getFee': (req, res) => {
    res.send(Mock.mock({
      msg: '暂无结果',
      code: req.body.keyword === '11111111' ? 1 : 0,
      data: {
        service_fee: '1',
        original_fee: '0',
        apply_fee: '0.03',
        check_status: '1',
        valid_tip: '12分钟后生效，快宝收取入库服务费20%为提现费，每天固定时间从新零售资金账户扣除',
        first_apply: false,
        cabinet_fee:10,
        'charge_ratio|1': [0,1],
        'cabinet_ratio|1': [0,1],
        service_fee_brand:{
          zt: 12,
          sto: 10
        },
        cabinet_fee_brand:{
          yd: 12,
          sto: 10,
          zt: 20,
          esm: 10,
        }

      },
    }));
  },
  'POST /Api/YZ/Fund/feeApply': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: true,
    });
  },
  'POST /Api/YZ/Fund/overview': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((_, index) => {
      result.push({
        cm_id: `${index}`,
        data_range: `2020-4-01到2020-4-30`,
        inn_name: `张${index}`,
        total_waybill: `12${index}`,
        total_fee: `4512${index}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        summery: {
          total_waybill: '175',
          total_fee: '8.43',
        },
        list: result,
        total: 40,
        page: 1,
      },
    });
  },

  'POST /Api/YZ/Fund/feeDetail': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((_, index) => {
      result.push({
        id: `${index}`,
        inn_date: `2020-4-${index}`,
        single_day: '2020-4-30',
        date: `张${index}`,
        total_waybill: `12${index}`,
        total_fee: `4512${index}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        summary: {
          total_waybill: '175',
          total_fee: '8.43',
        },
        list: result,
        total: 40,
        page: 1,
      },
    });
  },
  'POST /Api/YZ/Fund/export': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: true,
    });
  },
  // 保管费配置列表
  'POST /Api/YZ/Fund/storageFeeConfig': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        total: '40',
        list: [
          {
            cm_id: '1957041',
            phone: '18829900094',
            company_name: '旺角驿站测试环境1',
            price: '0.00',
            update_at: '2022-03-30 19:48:02',
            brand_fee: [{ brand: 'sto', fee: '1.00' }],
          },
          {
            cm_id: '2128378',
            phone: '18626102448',
            company_name: '阳光天地',
            price: 0,
            update_at: '-',
            brand_fee: [],
          },
          {
            cm_id: '2225571',
            phone: '17596123053',
            company_name: '测试123',
            price: 0,
            update_at: '-',
            brand_fee: [],
          },
          {
            cm_id: '1748871',
            phone: '13661964640',
            company_name: '风之谷',
            price: '0.10',
            update_at: '2022-03-31 14:28:27',
            brand_fee: [
              { brand: 'yt', fee: '0.10' },
              { brand: 'zt', fee: '0.10' },
              { brand: 'sht', fee: '0.07' },
            ],
          },
          {
            cm_id: '2225207',
            phone: '15201946772',
            company_name: '淞虹驿站',
            price: 0,
            update_at: '-',
            brand_fee: [],
          },
          {
            cm_id: '2225863',
            phone: '15099996666',
            company_name: '陈述log',
            price: 0,
            update_at: '-',
            brand_fee: [],
          },
        ],
        page: '1',
      },
    });
    return;

    const result = [];
    Array.from({
      length: 40,
    }).forEach((_, index) => {
      result.push({
        cm_id: `${index}1`,
        phone: `1556541231${index}`,
        company_name: `申通${index}`,
        price: `${index}0`,
        brand_fee:
          index % 2
            ? [
                {
                  brand: 'sto',
                  fee: 2.56,
                },
                {
                  brand: 'yt',
                  fee: 3.3,
                },
                {
                  brand: 'ht',
                  fee: 0,
                },
              ]
            : [],
        update_at: '2020年5月6日17:16:21',
      });
    });
    res.send({
      msg: 'success',
      code: 0,
      data: {
        list: result,
        total: 40,
        page: 1,
      },
    });
  },

  'POST /Api/YZ/Fund/setStorageFee': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 保管费统计列表
  'POST /Api/YZ/Fund/getStorageFee': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        in_total: '8',
        fee_sum_total: '0.50',
        total: '2',
        page: '1',
        list: [
          {
            cm_id: '1748871',
            in_count: '2',
            fee_sum: '0.20',
            days: '2022-03-24到2022-03-30',
            yz_name: '风之谷',
          },
          {
            cm_id: '1748861',
            in_count: '6',
            fee_sum: '0.30',
            days: '2022-03-24到2022-03-30',
            yz_name: '断小弦儿',
          },
        ],
      },
    });
  },
  // 保管费发放列表
  'POST /Api/YZ/Fund/getStorageFeeSendList': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((_, index) => {
      result.push({
        id: `${index}`, // 驿站kb_id
        league_kb_id: `${index}`, // 驿站kb_id
        cm_id: `${index}`, // 驿站kb_id
        batch_no: `${index}`, // 批次号
        fee_wait: `${index}`, // 待发
        fee_already: `${index}`, // 已发
        fee_decrease: `${index}`, // 核减
        send_status: `${index % 2 ? 1 : 2}`, // 发放状态
        send_month: `${index}月`, // 已发
        create_at: `${index}创建`, // 创建时间
        send_at: `${index}发放`, // 发放时间
        yz_name: `${index}驿站名称`, // 驿站名称
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        fee_wait_sum: '46.20',
        fee_already_sum: '46.20',
        fee_decrease_sum: '46.20',
        total: 40,
        page: 1,
      },
    });
  },

  // 保管费发放明细
  'POST /Api/YZ/Fund/settleDetail': (req, res) => {
    const result = [];
    Array.from({
      length: 25,
    }).forEach((_, index) => {
      result.push({
        create_time: '2020-06-09 17:00:26',
        deduct_fee: '0.00',
        fee: '0.44',
        from_kb_id: '159944',
        from_kb_type: 'city',
        frozen_fee: '0.24',
        id: `${index}`,
        num: '23',
        region: '2020-06',
        send_fee: '0.00',
        to_kb_id: `${index}123`,
        to_kb_type: 'yz',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: [
          {
            id: '14',
            from_kb_id: '159961',
            from_kb_type: 'city',
            to_kb_id: '160008',
            to_kb_type: 'yz',
            num: '27',
            fee: '14.60',
            frozen_fee: '6.00',
            send_fee: '3.00',
            deduct_fee: '5.00',
            create_time: '2020-07-01 10:15:49',
            region: '2020-07',
          },
          {
            id: '10',
            from_kb_id: '159961',
            from_kb_type: 'city',
            to_kb_id: '160008',
            to_kb_type: 'yz',
            num: '24',
            fee: '7.20',
            frozen_fee: '0.60',
            send_fee: '6.00',
            deduct_fee: '0.00',
            create_time: '2020-06-22 16:57:39',
            region: '2020-06',
          },
        ],
        count: 40,
        page: 1,
      },
    });
  },
  // 保管费发放记录
  'POST /Api/YZ/Fund/settleRecord': (req, res) => {
    const result = [];
    Array.from({
      length: 50,
    }).forEach((_, index) => {
      result.push({
        id: `${index}`,
        s_id: `${index}`,
        num: `${index}`,
        frozen_fee: '111',
        send_fee: '111',
        deduct_fee: '111',
        create_time: '2020-05-12 13:30:49',
        ext_no: 'sf5eba3488e69e0',
        region: '2020-05',
        ext_type: 'send',
        inn: '顺丰',
        to_kb_id: `2993${index}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        count: 50,
        page: 1,
      },
    });
  },
  // 保管费发放
  'POST /Api/YZ/Fund/sendStorageFee': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 保管费批量发放
  'POST /Api/YZ/Fund/batchStorageFee': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 服务费设置，获取下属驿站列表
  'POST /Api/YZ/Fund/FeeSettingList': (req, res) => {
    const result = [];
    Array.from({
      length: 50,
    }).forEach((_, index) => {
      result.push({
        company_name: `驿站${index}`,
        cm_id: `${index}`,
        contact_phone: `1234567890${index}`,
        address: `建滔商业广场${index}`,
        status: `${index % 2 == 0 ? 0 : 1}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        total: 50,
        page: 1,
        pageSize: 20,
      },
    });
  },
  // 服务费设置，下属驿站开启或关闭
  'POST /Api/YZ/Fund/FeeEditSetting': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  'POST /Api/YZ/Fund/getOpenBrands': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        { auth: '13', brand: '申通', brand_en: 'sto' },
        { auth: '13', brand: '中通', brand_en: 'zt' },
        { auth: -1, brand: '韵达', brand_en: 'yd' },
        { auth: '13', brand: '百世', brand_en: 'ht' },
        { auth: -1, brand: 'EMS', brand_en: 'ems' },
        { auth: '13', brand: '圆通', brand_en: 'yt' },
        { auth: -1, brand: '顺丰', brand_en: 'sf' },
        { auth: -1, brand: '京东', brand_en: 'jd' },
        { auth: -1, brand: '邮政', brand_en: 'yz' },
        { auth: -1, brand: '极兔', brand_en: 'jt' },
        { auth: -1, brand: '兴盛优选', brand_en: 'xsyx' },
        { auth: -1, brand: '丰网', brand_en: 'fw' },
        { auth: '7', brand: '安能', brand_en: 'ane' },
      ],
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/getDeliveryPayList': (req, res) => {
    const result = [];
    Array.from({
      length: 20,
    }).map((item, index) => {
      result.push({
        date: '2022-03-25',
        operator_kb_code: '000202001' + index,
        count: '16',
        status: index % 2 === 0 ? '已发放' : '未发放',
        totalPrice: 2.04,
        courier_phone: '13671856416',
        courier_name: '二子',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        count: 100,
        page: req.body.page,
        pageSize: 20,
      },
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/getDeliveryPayDetails': (req, res) => {
    const result = [];
    Array.from({
      length: 20,
    }).map((item, index) => {
      result.push({
        id: index,
        date: '2022-03-25',
        operator_kb_code: '000202001' + index,
        count: '16',
        status: index % 2 === 0 ? '已发放' : '未发放',
        totalPrice: 2.04,
        courier_phone: '13671856416',
        courier_name: '二子',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: result,
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/getDeliveryPaySetInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        brandInfo: [
          { brand: 'ems', price: '0.07', brand_name: '邮政' },
          { brand: 'fw', price: '0.50', brand_name: '丰网' },
          { brand: 'ht', price: '0.00', brand_name: '百世' },
          { brand: 'jt', price: '0.10', brand_name: '极兔' },
          { brand: 'sto', price: '2.70', brand_name: '申通' },
          { brand: 'yd', price: '0.00', brand_name: '韵达' },
          { brand: 'yt', price: '5.72', brand_name: '圆通' },
          { brand: 'zt', price: '0.34', brand_name: '中通' },
          { brand: 'zykd', price: '0.00', brand_name: '众邮' },
        ],
        deliveryPayType: '1',
      },
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/setDeliveryPaySetInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/multiEditDeliveryPayStatus': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/manualDistributionFeeDetails': (
    req,
    res,
  ) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  'POST /v1/TotalDistribution/DeliveryFeeDirectPayment/manualDistributionFee': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  'POST /Api/YZ/Fund/brandFeeSet': (req, res) => {
    delay(()=> {
      res.send({
        msg: 'success',
        code: 0,
        data: {},
      });
    },3* 1000)

  },
};
