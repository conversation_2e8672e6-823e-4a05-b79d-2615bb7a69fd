/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

const account = (req, res) => {
  // 信息品牌列表
  const params = JSON.parse(req.body.data);
  const result = [];
  // const privilege = [
  //   'finance',
  //   'system',
  //   'business',
  //   'delivery',
  //   'shop',
  //   'post',
  //   'platform',
  //   'allocation',
  // ];
  // const range = Array.from({ length: 999 }).map((_, index) => `${index}-2`).join(',');
  switch (params.run) {
    case '/Platform/index':
      res.send({
        code: 0,
        msg: 'success',
        data: {
          page: 1,
          size: 20,
          count: 22,
          result: [
            {
              id: '4608',
              created_at: '2025-06-23 09:33:27',
              phone: '***********',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'kbtest',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '',
              blank_pwd: 'kb123456',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4607',
              created_at: '2025-06-23 09:33:11',
              phone: '19941184118',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: 'Cabinet',
              blank_pwd: 'kb123456',
              area_ids: '*',
              sub_privilege_item:
                ',kdg_area-0,kdg_sub-2,kdg_stat-1,kdg_fee-0,kdg_forbid-0,kdg_installment-0,kdg_inStorage-0,kdg_retransmit-0',
              area_name: '全公司',
            },
            {
              id: '4603',
              created_at: '2025-06-20 15:34:52',
              phone: '13388998832',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '111222333',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: 'finance',
              blank_pwd: 'kb123456',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4602',
              created_at: '2025-06-19 11:02:59',
              phone: '15715861399',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'Feng',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: 'system,Cabinet',
              blank_pwd: 'kb123456',
              area_ids: '1750962',
              sub_privilege_item: '*',
              area_name: 'Feng',
            },
            {
              id: '4596',
              created_at: '2025-05-19 10:08:01',
              phone: '***********',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'shishuozheng',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege:
                'business,delivery,allocation,orders,platform,post,system,finance,auto,downloadReport',
              blank_pwd: 'guigui22',
              area_ids: '1750264,1749301,1749299,1749284,1749239,1749236,1749227,1749222,10',
              sub_privilege_item: ',26-1,25-0',
              area_name:
                '主站123、报价测试站点、上海市黄测、嘉定区黄测、站点测试一、托比昂、快宝片区测试、批量导入、zm测试',
            },
            {
              id: '4595',
              created_at: '2025-05-19 09:43:30',
              phone: '***********',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '史测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '',
              blank_pwd: 'guigui22',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4546',
              created_at: '2024-07-24 10:09:15',
              phone: '15618890229',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '15618890229',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: 'post',
              blank_pwd: 'a15618890229',
              area_ids: '1750264',
              sub_privilege_item: '',
              area_name: 'zm测试',
            },
            {
              id: '4540',
              created_at: '2024-06-27 16:48:43',
              phone: '15252130608',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'wm测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'kb12345678',
              area_ids: '1750264,1749239,1750839,1749301,1749299,1749236,1749227,10',
              sub_privilege_item: '',
              area_name:
                '主站123、上海市黄测、嘉定区黄测、站点测试一、快宝片区测试、批量导入、zm测试、aaa',
            },
            {
              id: '4539',
              created_at: '2024-06-15 12:43:34',
              phone: '13838389468',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '13838389468',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'kb123456',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4465',
              created_at: '2024-01-03 15:11:19',
              phone: '13524849951',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'kb123456',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4422',
              created_at: '2023-04-25 06:56:21',
              phone: '17321104581',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'zm测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: '12345678aA',
              area_ids: '1749301,1750264,1750961',
              sub_privilege_item: '',
              area_name: '批量导入、zm测试、dxx测试小号',
            },
            {
              id: '4421',
              created_at: '2023-04-24 08:20:13',
              phone: '18805152185',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'test',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'test123456',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4420',
              created_at: '2023-04-24 06:42:19',
              phone: '13015480797',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'testsun',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: '123321123sS',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '4418',
              created_at: '2022-12-12 01:58:18',
              phone: '19921164380',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '黄韬测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'qwer1234',
              area_ids: '1749299',
              sub_privilege_item: '*',
              area_name: '快宝片区测试',
            },
            {
              id: '4416',
              created_at: '2022-10-27 09:08:53',
              phone: '15030302021',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '片区测试1',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'a123456789',
              area_ids: '10',
              sub_privilege_item: '',
              area_name: '主站123',
            },
            {
              id: '4413',
              created_at: '2022-10-25 02:59:32',
              phone: '15030302020',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '片区测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'a123456789',
              area_ids: '10,1749236,1749289',
              sub_privilege_item: '',
              area_name: '主站123、嘉定区黄测、胜多负少',
            },
            {
              id: '4410',
              created_at: '2022-08-17 05:27:43',
              phone: '15651494896',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'huhao',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: '123123123A',
              area_ids: '10',
              sub_privilege_item: '',
              area_name: '主站123',
            },
            {
              id: '4384',
              created_at: '2021-11-18 03:46:18',
              phone: '17601614094',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '凯杰',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'a123456789',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '2519',
              created_at: '2021-03-31 06:25:12',
              phone: '13700000001',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '测试',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: '12345678a',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '2494',
              created_at: '2021-03-23 05:40:41',
              phone: '13487987523',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '测试1',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'a123456789',
              area_ids: '1749239,1749236,1749284',
              sub_privilege_item: '*',
              area_name: '嘉定区黄测、站点测试一、托比昂',
            },
            {
              id: '303',
              created_at: '2020-07-22 06:31:26',
              phone: '18297947314',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: 'chris',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'chris123456',
              area_ids: '*',
              sub_privilege_item: '',
              area_name: '全公司',
            },
            {
              id: '292',
              created_at: '2020-03-26 05:57:07',
              phone: '18721008369',
              is_active: '1',
              deleted_at: null,
              is_forbiden: '0',
              name: '验证码',
              province: null,
              brand_name: null,
              city: null,
              district: null,
              privilege: '*',
              blank_pwd: 'a123456789',
              area_ids: '1749284,1749292',
              sub_privilege_item: '*',
              area_name: '托比昂、艾泽拉斯1',
            },
          ],
        },
      });
      return;
      Array.from({
        length: 4,
      }).forEach((item, index) => {
        result.push({
          id: `${index}`,
          name: `张${index}`,
          phone: `1831111111${index}`,
          is_forbiden: Math.floor(Math.random() * 2),
          // privilege: '*',
          privilege:
            'business,delivery,allocation,orders,platform,post,system,finance,auto,downloadReport',
          sub_privilege_item: ['23-0', '24-1', '25-2'].join(','),
          blank_pwd: '123456aa',
          area_ids: index % 2 ? '*' : '1,3,5',
          area_name: index % 2 ? '全公司' : '嘉定片区、长宁片区',
        });
      });
      res.send({
        msg: '暂无结果',
        code: 0,
        data: {
          result,
          count: result.length,
        },
      });
      return;
    case '/Platform/addShopInfo':
      const { id } = req.body;
      res.send({
        msg: `已${id ? '修改' : '添加'}`,
        code: 0,
        data: {},
      });
      break;
    case '/Platform/delete':
      res.send({
        msg: '已删除',
        code: 0,
        data: {},
      });
      break;
    case '/Platform/privilege':
      res.send({
        msg: '已指定',
        code: 0,
        data: {},
      });
      break;
    case '/Platform/multiForbidden':
      const { is_forbiden } = req.body;
      const statuses = ['开启', '禁用'];
      res.send({
        msg: `已${statuses[is_forbiden]}`,
        code: 0,
        data: {},
      });
      break;
    case '/Api/Account/update':
      res.send({
        msg: '已更新',
        code: 0,
        data: {},
      });
      break;
    default:
      break;
  }
};

export { account };

export default {
  // 资金账户，订单明细列表
  'POST /v1/TotalDistribution/GpFinanceDetails/getFinanceDetailsList': (req, res) => {
    const list = [];
    Array.from({
      length: 10,
    }).map((_, index) => {
      list.push({
        date: `2021-8-1${index}`,
        count: '5',
        price: '0.100',
        brands: 'ht,yt',
        total_price: '0.5',
      });
    });
    res.send({
      msg: 'success',
      code: 0,
      data: list,
    });
  },
  // 资金账户，订单明细，导出
  'POST /v1/TotalDistribution/GpFinanceDetails/getFinanceDetailsByDate': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 资金账户，账户余额，转入消费账户
  'POST /v1/TotalDistribution/GpFinanceDetails/transform': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 资金账户，账户余额，修改余额预警
  'POST /Api/Company/editBalanceWarning': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
  // 资金账户，账户余额，获取余额预警
  'POST /Api/Company/shopBalanceWarningInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '',
      data: {
        id: '5',
        shop_id: '8',
        kb_id: '7238',
        warning_balance: '301.00',
        warning_status: '1',
        create_time: '2021-12-15 09:38:29',
        update_time: '2021-12-15 09:38:29',
        phone: '***********,17766665555',
      },
    });
  },
  // 账号管理，给子账号分配片区
  'POST /Api/Site/divideAreaPower': (req, res) => {
    res.send({
      msg: 'success',
      code: 0,
      data: {},
    });
  },
};
