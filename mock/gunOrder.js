/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 获取巴枪配置信息
export function getConInfo(req, res) {
  const orderList = [];
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < 2; i++) {
    orderList.push({
      id: i + 1,
      kb_id: 632632,
      kc_code: '00108',
      branch: '一号网点',
      branch_code: '********-快宝测试',
      branch_name: '',
      cm_phone: *********** + i,
      cm_name: '国务院',
      brand: 'tt',
      gun_account: 9999 + i,
      gun_pwd: '25f9e794323b453885f5181f1b624d0b',
      update_time: '2019-03-08 15:34:33',
      create_time: '2019-03-06 16:28:26',
    });
  }

  res.send({
    code: 0,
    msg: '成功',
    data: [
      {
        id: '2409',
        kb_id: '7',
        kc_code: '020',
        branch_code: '********',
        branch_name: '',
        cm_phone: '0',
        cm_name: '',
        brand: 'ems',
        gun_account: '1111',
        gun_pwd: '',
        gun_account_v2: '111111',
        gun_pwd_v2: '',
        devs: '{"imei":"111111"}',
        update_time: '2024-03-17 09:31:53',
        create_time: '2023-08-22 17:52:52',
        third_code: '',
        code: '',
        face_pic: null,
        trace_code: '123',
        is_open: 1,
        org_code: '',
      },
      {
        id: '2474',
        kb_id: '7',
        kc_code: '020',
        branch_code: '8995101',
        branch_name: '',
        cm_phone: '0',
        cm_name: '',
        brand: 'jt',
        gun_account: '111111',
        gun_pwd: '1111111',
        gun_account_v2: '',
        gun_pwd_v2: '',
        devs: { deviceType: 'HUAWEI P70', imei: '*****************' },
        update_time: '2024-01-05 10:29:09',
        create_time: '2024-01-05 10:29:09',
        third_code: '',
        code: '',
        face_pic: null,
        is_open: 1,
      },
      {
        id: '1557',
        kb_id: '7',
        kc_code: '020',
        branch_code: '311113',
        branch_name: '',
        cm_phone: '0',
        cm_name: '',
        brand: 'sto',
        gun_account: '3444555',
        gun_pwd: '',
        gun_account_v2: '***********',
        gun_pwd_v2: '',
        devs: '',
        update_time: '2022-05-19 13:21:54',
        create_time: '2022-05-19 13:21:54',
        third_code: '',
        code: '',
        face_pic: null,
        is_open: 1,
      },
      {
        id: '1357',
        kb_id: '7',
        kc_code: '020',
        branch_code: '581795',
        branch_name: '',
        cm_phone: '0',
        cm_name: '',
        brand: 'yd',
        gun_account: '*********',
        gun_pwd: '**********',
        gun_account_v2: '***********',
        gun_pwd_v2: '2222',
        devs: '',
        update_time: '2024-03-17 09:31:05',
        create_time: '2021-10-13 11:40:45',
        third_code: '',
        code: '',
        face_pic: null,
        is_open: 1,
      },
      {
        id: '1564',
        kb_id: '7',
        kc_code: '020',
        branch_code: '045801',
        branch_name: '',
        cm_phone: '0',
        cm_name: '',
        brand: 'yt',
        gun_account: '**********',
        gun_pwd: '1111',
        gun_account_v2: '',
        gun_pwd_v2: '',
        devs: '',
        update_time: '2023-08-22 17:06:30',
        create_time: '2022-06-05 16:10:29',
        third_code: '',
        code: '',
        face_pic: null,
        is_open: 1,
        device_model: '',
        device_imei: '',
      },
      {
        id: '1359',
        kb_id: '7',
        kc_code: '020',
        branch_code: '35025',
        branch_name: '',
        cm_phone: '0',
        cm_name: '',
        brand: 'zt',
        gun_account: '35025.003',
        gun_pwd: '',
        gun_account_v2: '***********',
        gun_pwd_v2: '123',
        devs: '',
        update_time: '2024-01-05 11:13:35',
        create_time: '2021-10-18 16:21:56',
        third_code: '',
        code: '',
        face_pic: null,
        pda_dev_imei: '1111',
        pda_dev_id: '2222',
        is_open: 1,
      },
    ],
  });
}

// 获取网点编号、名称
export function getBranchInfo(req, res) {
  let orderList = null;
  if (req.body.value == 1) {
    orderList = ['37800-一号网点', 'KT57811000-龙泉0578-7761729'];
  } else if (req.body.value == 2) {
    orderList = ['37800-二号网点', 'KT57811000-龙泉0578-7761729'];
  } else if (req.body.value == 3) {
    orderList = ['37800-三号网点', 'KT57811000-龙泉0578-7761729'];
  } else {
    orderList = ['37800-四号网点', 'KT57811000-龙泉0578-7761729'];
  }
  if (req.body.brand == 'ht') {
    orderList = {
      ht: ['052500-百世深泽', '239003-滁州城南分部', '352000-宁德A站'],
    };
  } else if (req.body.brand == 'yd') {
    orderList = {
      yd: ['052500-深泽', '323700-韵达测试网点', '581795-河北主城区公司深泽县服务部'],
    };
  } else if (req.body.brand == 'sto') {
    orderList = {
      sto: ['052500-深泽', '323700-申通测试网点', '581795-河北主城区公司深泽县服务部'],
    };
  } else {
    orderList = {
      ems: ['011500-深泽', '31002112-彭埠营业部'],
      fw: [
        'ceshi001-测试网点丰网',
        'cs006-测试丰网速运6',
        'fw002-丰网测试002',
        'FW3423423-丰网长宁测试',
      ],
      ht: [
        '011500-和林格尔县',
        '052500-深泽',
        '052511-百世深泽',
        '10001-淞沪网点',
        '164200-孙吴县',
        '212200-扬中',
        '239003-滁州城南分部',
        '310030-百世测试对接',
        '315203-江北一部',
        '352000-宁德A站',
        '410093-平江',
        '66666-全是6',
        '96444-迎宾网点',
      ],
      jt: [
        '2898300-海南老城网点',
        '333333-订单',
        '455101-合肥转运中心',
        '96555-信丰网点',
        'jt0002-极兔测试1123',
        'jt001-极兔快递01路',
      ],
      sto: [
        '0001111-测试11111',
        '226400-测试网点编号',
        '311113-浙江测试36',
        '400000-90',
        '415700-西藏拉萨公司',
        '616750-四川喜德公司',
        '900000-666',
        '900005-测试',
        '900006-总部测试1',
        '900009-测试',
      ],
      tt: [
        '12121-12112121',
        '1213131-假天',
        '234567-天天测试',
        '96333-和元组网点',
        'KT31117000-深泽分公司',
        'KT57811000-天天',
      ],
      yd: [
        '052500-测试深泽',
        '10001-淞沪网点',
        '12343543-韵达2',
        '210369-江苏南京滨江新城公司',
        '323700-丽水韵达网点',
        '3243434-韵达',
        '352100-福建宁德公司',
        '414004-湖南平江县公司',
        '520220-赣州网点',
        '528401-广东中山公司南区分部',
        '581795-河北主城区公司深泽县服务部',
        '615602-四川主城区公司喜德县服务部',
        '96123-廖溪网点',
      ],
      yt: [
        '043205-吉林省吉林市永吉县口前乡分部',
        '100001-淞沪网点',
        '311009-河北省石家庄市深泽县',
        '3454355345-是鬼斧神工',
        '352111-圆通测试网点',
        '430001-圆通测试！！！！！',
        '432008-吉林省吉林市永吉县',
        '444444-狂鼠中心',
        '66666555-666666555',
        '730008-湖南省岳阳市平江县公司',
        '730824-平江伍市镇',
        '8080-圆通兴国网点',
        '834016-四川省凉山彝族自治州喜德县公司',
        '876013-云南省昆明市关上公司',
        '898037-线路测试网点',
        '898819-海南省澄迈县白莲镇',
        '909090-圆通测试',
        '96111-埠头网点',
        '99922-这里是测试网点',
      ],
      zt: [
        '10001-淞虹网点',
        '12222222-测试申通',
        '432008-吉林省吉林市永吉县',
        '456754-江西网点',
        '51405-昆山花桥',
        '550002-假花桥',
        '57720-乐清',
        '666666-666666',
        '75770-测试75770',
        '9600-兴国网点',
      ],
      zykd: [
        '100001-淞虹网点',
        '123-众邮下一站',
        '23334-7777',
        '24-2424',
        'JJW000518-地平线网点',
        'JJW011301-秦皇岛海港公司',
      ],
    };
  }
  res.send({
    code: 0,
    msg: '获取成功',
    data: orderList,
  });
}

// 修改巴枪配置
export function userConfig(req, res) {
  res.send({
    code: 0,
    msg:
      Math.floor(Math.random() * 10) / 2
        ? '网点XXX下维护的工号xxx在梧桐系统内不存在或者找不到工号对应的手机号，无法验证。请在梧桐系统'
        : '成功',
    data: '',
  });
}

// 删除巴枪配置
export function removeInfo(req, res) {
  res.send({
    code: 0,
    msg: '删除成功',
    data: '',
  });
}

// 切换品牌开关状态
export function switchConfig(req, res) {
  res.send({
    code: 0,
    msg: '',
    data: '',
  });
}

export function verifyResult(req, res) {
  res.send({
    code: 0,
    msg: 'success',
    data: {
      ems: {
        success: true,
        errmsg: 'ok',
        errcode: 0,
        result: null,
      },
      jt: {
        success: false,
        errmsg: '需要输入验证码',
        errcode: '9990001',
        result: {
          phone: '18213123112',
          code: '',
          brand: 'jt',
        },
      },
    },
  });
}

export function getIcons(req, res) {
  res.send({
    code: 0,
    msg: 'success',
    data: [
      {
        id: 1,
        title: '派件',
        url: 'https://upload.kuaidihelp.com/tbk/avatar/39.png',
        type: 'top',
      },
      {
        id: 2,
        title: '签收',
        url: 'https://upload.kuaidihelp.com/tbk/avatar/39.png',
        type: 'top',
      },
      {
        id: 3,
        title: '签收',
        url: 'https://upload.kuaidihelp.com/tbk/avatar/39.png',
        type: 'middle',
      },
      // {
      //   id: 4,
      //   title: '签收',
      //   url:
      //     'http://kbcertificate.oss-cn-hangzhou.aliyuncs.com/courier/icon/<EMAIL>',
      //   type: 'bottom',
      // },
    ],
  });
}
export function getDisabledIcon(req, res) {
  res.send({
    code: 0,
    msg: 'success',
    data: {
      icon_ids: [1, 2, 3],
    },
  });
}
