/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

//下属驿站投诉管理接口
export default {
  "POST /Api/YZ/CourierStation/dakComplaint": (req, res) => {
    let result = [];
    Array.from({
      length: 20,
    }).map((itme, index) => {
      result.push({
        id: `${index}`,
        express_phone: "18829900094",
        cm_id: `${index}1957124`,
        inn_name: "阿尔法",
        waybill_no: "2582868686",
        brand: "中通",
        create_at: "2020-06-11 16:58:46",
        is_deal: index % 2 == 0 ? "0" : "1",
        is_deal_desc: index % 2 == 0 ? "未处理" : "已处理",
        deal_desc: "未处理未处理未处理未处理未处理未处理",
        complaint_type: "1",
        complaint_type_desc: "代收点服务态度不好",
        complaint_desc: "代收点服务态度不好代收点服务态度不好代收点服务态度不好",
      });
    });
    res.send({
      code: 0,
      msg: "成功",
      data: {
        list: result,
        total: "20",
        page: 1,
      },
    });
  },
};
