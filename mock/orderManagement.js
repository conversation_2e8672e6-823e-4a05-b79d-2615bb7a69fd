/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable array-callback-return */
import mockjs from 'mockjs';

const { mock } = mockjs;
// 订单管理，获取业务员单号设置表格
export default {
  // 获取驿站列表
  'GET /Api/orderManage/WaybillSource/getSiteNameList': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: [
        {
          site_id: '10',
          site_name: '快宝急送',
        },
        {
          site_id: '12',
          site_name: '站点',
        },
        {
          site_id: '13',
          site_name: '站点3',
        },
        {
          site_id: '30',
          site_name: '斯特3',
        },
        {
          site_id: '50',
          site_name: '123',
        },
        {
          site_id: '77',
          site_name: '费测站点（不要删）',
        },
        {
          site_id: '78',
          site_name: '123456',
        },
        {
          site_id: '80',
          site_name: 'gsdg ',
        },
        {
          site_id: '81',
          site_name: '189站点',
        },
      ],
    });
  },
  // 获取业务员列表
  'GET /Api/orderManage/WaybillSource/getCourierList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        page: 1,
        pageSize: 20,
        totalPages: 1,
        totalCount: 5,
        list: [
          {
            user_id: '5',
            site_name: '站点3',
            courier_info: '二子-***********',
            waybill_source: [
              {
                id: '13441',
                source: {
                  auth_id: '35646',
                  source_type: '6',
                  brand: 'tt',
                  month_no: '0',
                  account: 'ddd',
                  password: '123',
                  customer: 'ddd',
                  quantity: '',
                  brand_name: '天天快递',
                  u_id: 'kop_35646',
                },
                status: '0',
                status_value: '0',
                price: '10.00',
                brand_name: '天天',
                brand_type: 'tt',
                waybill_quantity: '-1',
              },
              {
                id: '13442',
                source: {
                  auth_id: '35675',
                  source_type: '6',
                  brand: 'zt',
                  month_no: '0',
                  account: '111',
                  password: '111',
                  customer: '',
                  quantity: '',
                  brand_name: '中通快递',
                  u_id: 'kop_35675',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
              },
              {
                id: '13443',
                source: {
                  auth_id: '129336',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: '1111',
                  password: '2222',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129336',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13444',
                source: {
                  auth_id: '129336',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: '1111',
                  password: '2222',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129336',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13445',
                source: {
                  auth_id: '129336',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: '1111',
                  password: '2222',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129336',
                },
                status: '0',
                status_value: '0',
                price: '20.00',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '-1',
              },
              {
                id: '13446',
                source: {
                  auth_id: '129336',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: '1111',
                  password: '2222',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129336',
                },
                status: '0',
                status_value: '0',
                price: '20.00',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '10',
            site_name: '站点3',
            courier_info: '135-***********',
            waybill_source: [
              {
                id: '4477',
                source: {
                  auth_id: '35665',
                  source_type: '6',
                  brand: 'yd',
                  month_no: '0',
                  account: '************',
                  password: 'JH7jyrRq5fZDnNBuWscPMhTQ2VAaEd',
                  customer: '',
                  quantity: '',
                  brand_name: '韵达快递',
                  u_id: 'kop_35665',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '韵达',
                brand_type: 'yd',
                waybill_quantity: '6',
              },
              {
                id: '13250',
                source: {
                  auth_id: '35673',
                  source_type: '6',
                  brand: 'sf',
                  month_no: '0',
                  account: '123',
                  password: '123',
                  customer: '',
                  quantity: '',
                  brand_name: '顺丰速运',
                  u_id: 'kop_35673',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '顺丰',
                brand_type: 'sf',
                waybill_quantity: '-1',
              },
              {
                id: '13338',
                source: {
                  branch_code: 'EMS',
                  brand_code: 'EMS',
                  brand_type: 'EMS',
                  brand_name: 'EMS',
                  kb_code: 'ems',
                  branch_name: null,
                  allocated_quantity: 815,
                  cancel_quantity: 63,
                  quantity: -9999,
                  address: [
                    {
                      city: '杭州市',
                      detail: '笕桥镇花园兜街221号',
                      district: '江干区',
                      province: '浙江省',
                      waybill_address_id: '*********',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: ['*************'],
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_EMS',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
              {
                id: '13414',
                source: {
                  auth_id: '129326',
                  source_type: '15',
                  brand: 'sto44',
                  month_no: '0',
                  account: 'aaa',
                  password: 'sss',
                  customer: 'zzXzx',
                  quantity: '',
                  brand_name: '申通快递',
                  u_id: 'kop_129326',
                  branch_name: 'zzXzx',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '申通',
                brand_type: 'sto',
                waybill_quantity: '-1',
              },
              {
                id: '13424',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13425',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '11',
            site_name: '主站123',
            courier_info: '骗你的哈-***********',
            waybill_source: [
              {
                id: '13385',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13386',
                source: {
                  branch_code: '23026',
                  brand_code: 'ZTO',
                  brand_type: 'ZTO',
                  brand_name: '中通',
                  kb_code: 'zt',
                  branch_name: '长宁凌空园',
                  allocated_quantity: 3452,
                  cancel_quantity: 24,
                  quantity: '8',
                  address: [
                    {
                      city: '上海市',
                      detail: '建滔广场6号6楼',
                      district: '长宁区',
                      province: '上海',
                      waybill_address_id: '*********',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_23026',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
              },
              {
                id: '13387',
                source: {
                  branch_code: '51405',
                  brand_code: 'ZTO',
                  brand_type: 'ZTO',
                  brand_name: '中通',
                  kb_code: 'zt',
                  branch_name: '昆山花桥',
                  allocated_quantity: 0,
                  cancel_quantity: 0,
                  quantity: '0',
                  address: [
                    {
                      city: '苏州市',
                      detail: '花桥镇',
                      district: '昆山市',
                      province: '江苏省',
                      waybill_address_id: '13310067',
                    },
                  ],
                  segment_code: 'SVC-STAR',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_51405',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '14',
            site_name: '站点3',
            courier_info: '123-***********',
            waybill_source: [
              {
                id: '4480',
                source: {
                  auth_id: '35665',
                  source_type: '6',
                  brand: 'yd',
                  month_no: '0',
                  account: '************',
                  password: 'JH7jyrRq5fZDnNBuWscPMhTQ2VAaEd',
                  customer: '',
                  quantity: '',
                  brand_name: '韵达快递',
                  u_id: 'kop_35665',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '韵达',
                brand_type: 'yd',
                waybill_quantity: '-1',
              },
              {
                id: '13451',
                source: {
                  branch_code: '200003',
                  brand_code: 'STO',
                  brand_type: 'STO',
                  brand_name: '申通',
                  kb_code: 'sto',
                  branch_name: '上海虹桥公司',
                  allocated_quantity: 25349,
                  cancel_quantity: 185,
                  quantity: '0',
                  address: [
                    {
                      city: '上海市',
                      detail: '通协路269号6号楼6楼A单元',
                      district: '长宁区',
                      province: '上海',
                      waybill_address_id: '********',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_200003',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '申通',
                brand_type: 'sto',
                waybill_quantity: '-1',
              },
              {
                id: '13452',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13453',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13454',
                source: {
                  branch_code: '774892',
                  brand_code: 'YTO',
                  brand_type: 'YTO',
                  brand_name: '圆通',
                  kb_code: 'yt',
                  branch_name: '广西省贺州',
                  allocated_quantity: 66,
                  cancel_quantity: 8,
                  quantity: '0',
                  address: [
                    {
                      city: '上海市',
                      detail: '通协路269号6号楼6楼A单元',
                      district: '长宁区',
                      province: '上海',
                      waybill_address_id: '65040019',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_774892',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '圆通',
                brand_type: 'yt',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '17',
            site_name: '浦江站点',
            courier_info: '钟大大-***********',
            waybill_source: [
              {
                id: '13346',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.02',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '199',
              },
            ],
          },
          {
            user_id: '19',
            site_name: '浦江站点',
            courier_info: '测试业务员-***********',
            waybill_source: [
              {
                id: '13347',
                source: {
                  auth_id: '129335',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: '********',
                  password: '23333',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129335',
                },
                status: '1',
                status_value: '1',
                price: '0.02',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '100',
              },
              {
                id: '13348',
                source: {
                  auth_id: '129336',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: '1111',
                  password: '2222',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129336',
                },
                status: '1',
                status_value: '1',
                price: '0.02',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '100',
              },
            ],
          },
          {
            user_id: '21',
            site_name: '浦江站点',
            courier_info: '大声道-***********',
            waybill_source: [
              {
                id: '13366',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
              {
                id: '13367',
                source: {
                  branch_code: 'EMS',
                  brand_code: 'EMS',
                  brand_type: 'EMS',
                  brand_name: 'EMS',
                  kb_code: 'ems',
                  branch_name: null,
                  allocated_quantity: 0,
                  cancel_quantity: 0,
                  quantity: -9999,
                  address: [
                    {
                      city: '杭州市',
                      detail: '笕桥镇花园兜街221号',
                      district: '江干区',
                      province: '浙江省',
                      waybill_address_id: '*********',
                    },
                  ],
                  segment_code: 'EMS-RECEIVER-PAY',
                  customer_code_list: ['*************'],
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_EMS',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
              {
                id: '13368',
                source: {
                  branch_code: 'EMS',
                  brand_code: 'EMS',
                  brand_type: 'EMS',
                  brand_name: 'EMS',
                  kb_code: 'ems',
                  branch_name: null,
                  allocated_quantity: 815,
                  cancel_quantity: 63,
                  quantity: -9999,
                  address: [
                    {
                      city: '杭州市',
                      detail: '笕桥镇花园兜街221号',
                      district: '江干区',
                      province: '浙江省',
                      waybill_address_id: '*********',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: ['*************'],
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_EMS',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '22',
            site_name: 'ces',
            courier_info: '哈哈-***********',
            waybill_source: [
              {
                id: '13416',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13417',
                source: {
                  auth_id: '35675',
                  source_type: '6',
                  brand: 'zt',
                  month_no: '0',
                  account: '111',
                  password: '111',
                  customer: '',
                  quantity: '',
                  brand_name: '中通快递',
                  u_id: 'kop_35675',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '23',
            site_name: '浦江站点',
            courier_info: '我是业务员-***********',
            waybill_source: [
              {
                id: '4476',
                source: {
                  auth_id: '35665',
                  source_type: '6',
                  brand: 'yd',
                  month_no: '0',
                  account: '************',
                  password: 'JH7jyrRq5fZDnNBuWscPMhTQ2VAaEd',
                  customer: '',
                  quantity: '',
                  brand_name: '韵达快递',
                  u_id: 'kop_35665',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '韵达',
                brand_type: 'yd',
                waybill_quantity: '6',
              },
            ],
          },
          {
            user_id: '25',
            site_name: '浦江站点',
            courier_info: '发的说法-***********',
            waybill_source: [
              {
                id: '13238',
                source: {
                  auth_id: '35646',
                  source_type: '6',
                  brand: 'tt',
                  month_no: '0',
                  account: 'ddd',
                  password: '123',
                  customer: 'ddd',
                  quantity: '',
                  brand_name: '天天快递',
                  u_id: 'kop_35646',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '天天',
                brand_type: 'tt',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '26',
            site_name: '浦江站点',
            courier_info: '测试1-***********',
            waybill_source: [
              {
                id: '13419',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13420',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '28',
            site_name: '浦江站点',
            courier_info: '测试4-***********',
            waybill_source: [
              {
                id: '13422',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13423',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '30',
            site_name: '浦江站点',
            courier_info: '测试6-***********',
            waybill_source: [
              {
                id: '13470',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
              {
                id: '13471',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13472',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '31',
            site_name: '浦江站点',
            courier_info: '啊啊士大夫-***********',
            waybill_source: [
              {
                id: '13526',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '1',
              },
              {
                id: '13531',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '56',
              },
              {
                id: '13532',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '56',
              },
            ],
          },
          {
            user_id: '34',
            site_name: '浦江站点',
            courier_info: '阿道夫-***********',
            waybill_source: [
              {
                id: '13529',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '10',
              },
              {
                id: '13530',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '10',
              },
            ],
          },
          {
            user_id: '42',
            site_name: '浦江站点',
            courier_info: '对账单测试-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '43',
            site_name: '浦江站点',
            courier_info: '报价单测试-17845698799',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '44',
            site_name: 'ces',
            courier_info: '测试4455-19944444444',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '45',
            site_name: '浦江站点',
            courier_info: '张三-15612346780',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '46',
            site_name: '浦江站点',
            courier_info: '钟一-15678945612',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '47',
            site_name: '浦江站点',
            courier_info: '钟二-13245678947',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '48',
            site_name: 'ces',
            courier_info: 'CESHIDE-13567978909',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '53',
            site_name: '浦江站点',
            courier_info: '钟意思-15612345678',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '54',
            site_name: '浦江站点',
            courier_info: '钟品牌-14512345678',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '55',
            site_name: '浦江站点',
            courier_info: '韩信-***********',
            waybill_source: [
              {
                id: '13248',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '56',
            site_name: '浦江站点',
            courier_info: '小乔-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '57',
            site_name: '报价测试站点',
            courier_info: '静安-15636524512',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '58',
            site_name: '浦江站点',
            courier_info: 'sadfas-15156329857',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '59',
            site_name: '浦江站点',
            courier_info: 'dghgfg-***********',
            waybill_source: [
              {
                id: '13253',
                source: {
                  auth_id: '35688',
                  source_type: '15',
                  brand: 'sto44',
                  month_no: '0',
                  account: '天桥东05',
                  password: '123456',
                  customer: '221035',
                  quantity: '0',
                  brand_name: '申通快递',
                  u_id: 'kop_35688',
                  branch_name: '江苏徐州天桥东营业部',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '申通',
                brand_type: 'sto',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '60',
            site_name: '艾泽拉斯',
            courier_info: '亮测试业务员-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '61',
            site_name: '你个der',
            courier_info: '鲁班-15478952654',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '91',
            site_name: '你个der',
            courier_info: '后裔1-14278945611',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '94',
            site_name: '你个der',
            courier_info: '孙尚香-18396587451',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '95',
            site_name: '你个der',
            courier_info: '白起-13296587452',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '96',
            site_name: '你个der',
            courier_info: '百里1-18712345671',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '97',
            site_name: '你个der',
            courier_info: '守约-13265478956',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '116',
            site_name: '报价测试站点',
            courier_info: '测试的额-***********',
            waybill_source: [
              {
                id: '13275',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '121',
            site_name: '报价测试站点',
            courier_info: '精神-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '124',
            site_name: '报价测试站点',
            courier_info: '1553测试-15010009000',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '125',
            site_name: '报价测试站点',
            courier_info: '钟UU-15000015433',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '126',
            site_name: '报价测试站点',
            courier_info: '钟CV-***********',
            waybill_source: [
              {
                id: '3510',
                source: {
                  auth_id: '30802',
                  source_type: '6',
                  brand: 'postx',
                  others: null,
                  month_no: '0',
                  account: '1111',
                  password: '1111',
                  customer: '',
                  quantity: '',
                  brand_name: '中国邮政快递包裹',
                  u_id: 'kop_30802',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '127',
            site_name: '数据导入测试',
            courier_info: '钟区区1-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '129',
            site_name: '',
            courier_info: 'MMPP-16012365478',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '131',
            site_name: '数据导入测试',
            courier_info: '哈哈的-15000015400',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '132',
            site_name: '数据导入测试',
            courier_info: '测试的-15300003333',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '134',
            site_name: '',
            courier_info: '苹果2-***********',
            waybill_source: [
              {
                id: '13272',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '135',
            site_name: '数据导入测试',
            courier_info: '817测试-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '136',
            site_name: '上海市黄测',
            courier_info: '苹果3-18550030016',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '139',
            site_name: '',
            courier_info: '苹果4-18550030092',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '140',
            site_name: '费测站点（不要删）',
            courier_info: '啊啊啊11-15122223333',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '141',
            site_name: '',
            courier_info: '的的-18502326985',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '142',
            site_name: '上海市黄测',
            courier_info: '马云1-18550020001',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '143',
            site_name: '上海市黄测',
            courier_info: '马云2-18550020002',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '144',
            site_name: '上海市黄测',
            courier_info: '马云3-18550020003',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '145',
            site_name: '上海市黄测',
            courier_info: '马云4-18550020004',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '146',
            site_name: '上海市黄测',
            courier_info: '马云5-18550020005',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '147',
            site_name: '上海市黄测',
            courier_info: '马云6-18550020006',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '148',
            site_name: '上海市黄测',
            courier_info: '马云7-18550020007',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '149',
            site_name: '上海市黄测',
            courier_info: '马云8-18550020008',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '150',
            site_name: '上海市黄测',
            courier_info: '马云9-18550020009',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '151',
            site_name: '上海市黄测',
            courier_info: '马云10-18550020010',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '152',
            site_name: '上海市黄测',
            courier_info: '马云11-18550020011',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '153',
            site_name: '上海市黄测',
            courier_info: '马云12-18550020012',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '154',
            site_name: '上海市黄测',
            courier_info: '马云13-18550020013',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '155',
            site_name: '上海市黄测',
            courier_info: '马云14-18550020014',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '156',
            site_name: '上海市黄测',
            courier_info: '马云15-18550020015',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '157',
            site_name: '上海市黄测',
            courier_info: '马云16-18550020016',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '158',
            site_name: '上海市黄测',
            courier_info: '马云呀17-18550020017',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '159',
            site_name: '上海市黄测',
            courier_info: '马云18-18550020018',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '160',
            site_name: '上海市黄测',
            courier_info: '马云19-18550020019',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '161',
            site_name: '上海市黄测',
            courier_info: '马云20-***********',
            waybill_source: [
              {
                id: '13283',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '162',
            site_name: '上海市黄测',
            courier_info: '马云21-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '163',
            site_name: '上海市黄测',
            courier_info: '马云22-18550020022',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '164',
            site_name: '上海市黄测',
            courier_info: '马云23-***********',
            waybill_source: [
              {
                id: '13277',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
              {
                id: '13278',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '165',
            site_name: '上海市黄测',
            courier_info: '马云24-***********',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '166',
            site_name: '上海市黄测',
            courier_info: '马云25-18550020025',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '167',
            site_name: '上海市黄测',
            courier_info: '马云26-18550020026',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '168',
            site_name: '',
            courier_info: '苹果5-18550020099',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '169',
            site_name: '数据导入测试',
            courier_info: '钟区区2-15000015500',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '170',
            site_name: '数据导入测试',
            courier_info: '钟区区3-15000015501',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '171',
            site_name: '数据导入测试',
            courier_info: '钟哈哈1-15999015499',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '172',
            site_name: '数据导入测试',
            courier_info: '钟哈哈2-15999015500',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '173',
            site_name: '数据导入测试',
            courier_info: '钟哈哈3-15999015501',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '174',
            site_name: '数据导入测试',
            courier_info: '钟嘻嘻1-16999015499',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '175',
            site_name: '数据导入测试',
            courier_info: '钟嘻嘻2-16999015500',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '176',
            site_name: '数据导入测试',
            courier_info: '钟嘻嘻3-16999015501',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '177',
            site_name: '数据导入测试',
            courier_info: '偏偏1-16999010499',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '178',
            site_name: '数据导入测试',
            courier_info: '偏偏2-16999010501',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '179',
            site_name: '数据导入测试',
            courier_info: '偏偏3-16999010493',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '180',
            site_name: '数据导入测试',
            courier_info: '不去3-16999010400',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '181',
            site_name: '嘉定区黄测',
            courier_info: '马化腾1-18550020027',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '182',
            site_name: '嘉定区黄测',
            courier_info: '马化腾2-18550020028',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '183',
            site_name: '嘉定区黄测',
            courier_info: '马化腾3-18550020029',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '184',
            site_name: '嘉定区黄测',
            courier_info: '马化腾4-18550020030',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '185',
            site_name: '嘉定区黄测',
            courier_info: '马化腾5-18550020031',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '186',
            site_name: '嘉定区黄测',
            courier_info: '马化腾6-18550020032',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
              },
            ],
          },
          {
            user_id: '187',
            site_name: '数据导入测试',
            courier_info: '白起2-***********',
            waybill_source: [
              {
                id: '13285',
                source: {
                  auth_id: '35675',
                  source_type: '6',
                  brand: 'zt',
                  month_no: '0',
                  account: '111',
                  password: '111',
                  customer: '',
                  quantity: '',
                  brand_name: '中通快递',
                  u_id: 'kop_35675',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '188',
            site_name: '站点',
            courier_info: '23123-***********',
            waybill_source: [
              {
                id: '13284',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '-1',
              },
            ],
          },
          {
            user_id: '189',
            site_name: 'ces',
            courier_info: '测试即时配送-***********',
            waybill_source: [
              {
                id: '13274',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '21',
              },
              {
                id: '13516',
                source: {
                  auth_id: '35675',
                  source_type: '6',
                  brand: 'zt',
                  month_no: '0',
                  account: '111',
                  password: '111',
                  customer: '',
                  quantity: '',
                  brand_name: '中通快递',
                  u_id: 'kop_35675',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '4',
              },
              {
                id: '13518',
                source: {
                  auth_id: '35646',
                  source_type: '6',
                  brand: 'tt',
                  month_no: '0',
                  account: 'ddd',
                  password: '123',
                  customer: 'ddd',
                  quantity: '',
                  brand_name: '天天快递',
                  u_id: 'kop_35646',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '天天',
                brand_type: 'tt',
                waybill_quantity: '77',
              },
              {
                id: '13521',
                source: {
                  auth_id: '35665',
                  source_type: '6',
                  brand: 'yd',
                  month_no: '0',
                  account: '************',
                  password: '33333',
                  customer: '',
                  quantity: '',
                  brand_name: '韵达快递',
                  u_id: 'kop_35665',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '韵达',
                brand_type: 'yd',
                waybill_quantity: '6',
              },
              {
                id: '13534',
                source: {
                  auth_id: '35673',
                  source_type: '6',
                  brand: 'sf',
                  month_no: '0',
                  account: '123',
                  password: '123',
                  customer: '',
                  quantity: '',
                  brand_name: '顺丰速运',
                  u_id: 'kop_35673',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '顺丰',
                brand_type: 'sf',
                waybill_quantity: '666',
              },
              {
                id: '13535',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '111',
              },
              {
                id: '13536',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '111',
              },
            ],
          },
          {
            user_id: '190',
            site_name: '测试即时配送',
            courier_info: '测试即时配送2-***********',
            waybill_source: [
              {
                id: '13461',
                source: {
                  branch_code: '23026',
                  brand_code: 'ZTO',
                  brand_type: 'ZTO',
                  brand_name: '中通',
                  kb_code: 'zt',
                  branch_name: '长宁凌空园',
                  allocated_quantity: 3452,
                  cancel_quantity: 24,
                  quantity: '8',
                  address: [
                    {
                      city: '上海市',
                      detail: '建滔广场6号6楼',
                      district: '长宁区',
                      province: '上海',
                      waybill_address_id: '*********',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_23026',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '13',
              },
              {
                id: '13467',
                source: {
                  auth_id: '129326',
                  source_type: '15',
                  brand: 'sto44',
                  month_no: '0',
                  account: 'aaa',
                  password: 'sss',
                  customer: 'zzXzx',
                  quantity: '',
                  brand_name: '申通快递',
                  u_id: 'kop_129326',
                  branch_name: 'zzXzx',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '申通',
                brand_type: 'sto',
                waybill_quantity: '14',
              },
              {
                id: '13500',
                source: {
                  auth_id: '35673',
                  source_type: '6',
                  brand: 'sf',
                  month_no: '0',
                  account: '123',
                  password: '123',
                  customer: '',
                  quantity: '',
                  brand_name: '顺丰速运',
                  u_id: 'kop_35673',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '顺丰',
                brand_type: 'sf',
                waybill_quantity: '20',
              },
              {
                id: '13505',
                source: {
                  auth_id: '35646',
                  source_type: '6',
                  brand: 'tt',
                  month_no: '0',
                  account: 'ddd',
                  password: '123',
                  customer: 'ddd',
                  quantity: '',
                  brand_name: '天天快递',
                  u_id: 'kop_35646',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '天天',
                brand_type: 'tt',
                waybill_quantity: '99',
              },
              {
                id: '13508',
                source: {
                  auth_id: '35665',
                  source_type: '6',
                  brand: 'yd',
                  month_no: '0',
                  account: '************',
                  password: '33333',
                  customer: '',
                  quantity: '',
                  brand_name: '韵达快递',
                  u_id: 'kop_35665',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '韵达',
                brand_type: 'yd',
                waybill_quantity: '44',
              },
              {
                id: '13513',
                source: {
                  auth_id: '35785',
                  source_type: '6',
                  brand: 'ems',
                  month_no: '0',
                  account: '498456',
                  password: '485464',
                  customer: '',
                  quantity: '',
                  brand_name: 'EMS',
                  u_id: 'kop_35785',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: 'EMS',
                brand_type: 'ems',
                waybill_quantity: '67',
              },
              {
                id: '13514',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '1214',
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '0',
              },
              {
                id: '13515',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: '安达市多大所大所',
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                status_value: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '0',
              },
            ],
          },
        ],
      },
    });
  },
  // 单号源设置，单号源下拉选择
  'POST /Api/orderManage/WaybillSource/listWithBrand': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        tt: {
          kop: [
            {
              auth_id: '35646',
              source_type: '6',
              brand: 'tt',
              month_no: '0',
              account: 'ddd',
              password: '123',
              customer: 'ddd',
              quantity: '',
              brand_name: '天天快递',
            },
          ],
        },
        yd: {
          kop: [
            {
              auth_id: '35665',
              source_type: '6',
              brand: 'yd',
              month_no: '0',
              account: '************',
              password: 'JH7jyrRq5fZDnNBuWscPMhTQ2VAaEd',
              customer: '',
              quantity: '',
              brand_name: '韵达快递',
            },
          ],
        },
        sf: {
          kop: [
            {
              auth_id: '35673',
              source_type: '6',
              brand: 'sf',
              month_no: '0',
              account: '123',
              password: '123',
              customer: '',
              quantity: '',
              brand_name: '顺丰速运',
            },
          ],
        },
        zt: {
          kop: [
            {
              auth_id: '35675',
              source_type: '6',
              brand: 'zt',
              month_no: '0',
              account: '111',
              password: '111',
              customer: '',
              quantity: '',
              brand_name: '中通快递',
            },
            {
              auth_id: '129337',
              source_type: '6',
              brand: 'zt',
              month_no: '0',
              account: '334',
              password: '444',
              customer: '',
              quantity: '',
              brand_name: '中通快递',
            },
          ],
          taobao: [
            {
              branch_code: '23026',
              brand_code: 'ZTO',
              brand_type: 'ZTO',
              brand_name: '中通',
              kb_code: 'zt',
              branch_name: '长宁凌空园',
              allocated_quantity: 3452,
              cancel_quantity: 24,
              quantity: '7',
              address: [
                {
                  city: '上海市',
                  detail: '建滔广场6号6楼',
                  district: '长宁区',
                  province: '上海',
                  waybill_address_id: '*********',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
            {
              branch_code: '51405',
              brand_code: 'ZTO',
              brand_type: 'ZTO',
              brand_name: '中通',
              kb_code: 'zt',
              branch_name: '昆山花桥',
              allocated_quantity: 18631,
              cancel_quantity: 63,
              quantity: '17',
              address: [
                {
                  city: '苏州市',
                  detail: '花桥镇',
                  district: '昆山市',
                  province: '江苏省',
                  waybill_address_id: '13310067',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
            {
              branch_code: '51405',
              brand_code: 'ZTO',
              brand_type: 'ZTO',
              brand_name: '中通',
              kb_code: 'zt',
              branch_name: '昆山花桥',
              allocated_quantity: 0,
              cancel_quantity: 0,
              quantity: '0',
              address: [
                {
                  city: '苏州市',
                  detail: '花桥镇',
                  district: '昆山市',
                  province: '江苏省',
                  waybill_address_id: '13310067',
                },
              ],
              segment_code: 'SVC-STAR',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
          ],
          pdd_universal: [
            {
              branch_code: '31271',
              brand_code: 'ZTO',
              brand_name: '中通快递',
              kb_code: 'zt',
              brand_type: 2,
              branch_name: '高碑店白沟',
              allocated_quantity: '0',
              cancel_quantity: '0',
              quantity: '0',
              address: [
                {
                  country: '中国',
                  province: '河北省',
                  city: '保定市',
                  district: '南市区',
                  detail: '快宝测试地址',
                },
              ],
              auth_id: '35918',
              source_type: '19',
              nickname: '41014143166_2251799814896318',
            },
            {
              branch_code: '51405',
              brand_code: 'ZTO',
              brand_name: '中通快递',
              kb_code: 'zt',
              brand_type: 2,
              branch_name: '昆山花桥',
              allocated_quantity: '287',
              cancel_quantity: '9',
              quantity: '1',
              address: [
                {
                  country: '中国',
                  province: '江苏省',
                  city: '苏州市',
                  district: '昆山市',
                  detail: '花桥花安路快宝网络',
                },
              ],
              auth_id: '35918',
              source_type: '19',
              nickname: '41014143166_2251799814896318',
            },
          ],
        },
        sto44: {
          kop: [
            {
              auth_id: '35688',
              source_type: '15',
              brand: 'sto44',
              month_no: '0',
              account: '天桥东05',
              password: '123456',
              customer: '221035',
              quantity: '0',
              brand_name: '申通快递',
            },
            {
              auth_id: '129326',
              source_type: '15',
              brand: 'sto44',
              month_no: '0',
              account: 'aaa',
              password: 'sss',
              customer: 'zzXzx',
              quantity: '',
              brand_name: '申通快递',
            },
          ],
        },
        ems: {
          kop: [
            {
              auth_id: '35785',
              source_type: '6',
              brand: 'ems',
              month_no: '0',
              account: '498456',
              password: '485464',
              customer: '',
              quantity: '',
              brand_name: 'EMS',
            },
          ],
          taobao: [
            {
              branch_code: 'EMS',
              brand_code: 'EMS',
              brand_type: 'EMS',
              brand_name: 'EMS',
              kb_code: 'ems',
              branch_name: null,
              allocated_quantity: 0,
              cancel_quantity: 0,
              quantity: -9999,
              address: [
                {
                  city: '杭州市',
                  detail: '笕桥镇花园兜街221号',
                  district: '江干区',
                  province: '浙江省',
                  waybill_address_id: '*********',
                },
              ],
              segment_code: 'EMS-RECEIVER-PAY',
              customer_code_list: ['*************'],
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
            {
              branch_code: 'EMS',
              brand_code: 'EMS',
              brand_type: 'EMS',
              brand_name: 'EMS',
              kb_code: 'ems',
              branch_name: null,
              allocated_quantity: 814,
              cancel_quantity: 63,
              quantity: -9999,
              address: [
                {
                  city: '杭州市',
                  detail: '笕桥镇花园兜街221号',
                  district: '江干区',
                  province: '浙江省',
                  waybill_address_id: '*********',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: ['*************'],
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
          ],
          wuliuyun: [
            {
              branch_code: 'EMS',
              branch_name: null,
              brand_code: 'EMS',
              brand_type: '1',
              brand_name: 'EMS',
              kb_code: 'ems',
              allocated_quantity: '599',
              cancel_quantity: '19',
              quantity: '0',
              address: [
                {
                  detail: '笕桥镇花园兜街221号',
                  district: '江干区',
                  city: '杭州市',
                  province: '浙江省',
                  customerCode: '*************',
                },
              ],
              service_info: {
                serviceName: '保价',
                serviceCode: 'SVC-INSURE',
              },
              auth_id: '35661',
              source_type: '12',
              nickname: '快宝网络',
            },
          ],
        },
        post: {
          kop: [
            {
              auth_id: '129276',
              source_type: '6',
              brand: 'post',
              month_no: '0',
              account: '1214',
              password: '1311',
              customer: '',
              quantity: '',
              brand_name: '邮政快包',
            },
            {
              auth_id: '129292',
              source_type: '6',
              brand: 'post',
              month_no: '0',
              account: '安达市多大所大所',
              password: '的方式方式到付发送的',
              customer: '',
              quantity: '',
              brand_name: '邮政快包',
            },
          ],
        },
        sto: {
          taobao: [
            {
              branch_code: '200003',
              brand_code: 'STO',
              brand_type: 'STO',
              brand_name: '申通',
              kb_code: 'sto',
              branch_name: '上海虹桥公司',
              allocated_quantity: 25347,
              cancel_quantity: 185,
              quantity: '0',
              address: [
                {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                  waybill_address_id: '********',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
            {
              branch_code: '352100',
              brand_code: 'STO',
              brand_type: 'STO',
              brand_name: '申通',
              kb_code: 'sto',
              branch_name: '福建宁德公司',
              allocated_quantity: 0,
              cancel_quantity: 0,
              quantity: '0',
              address: [
                {
                  city: '宁德市',
                  detail: '中山路1号（周明测试）',
                  district: '蕉城区',
                  province: '福建省',
                  waybill_address_id: '12355397',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
          ],
          wuliuyun: [
            {
              branch_code: '200003',
              branch_name: '上海虹桥公司',
              brand_code: 'STO',
              brand_type: '2',
              brand_name: '申通',
              kb_code: 'sto',
              allocated_quantity: '1859',
              cancel_quantity: '54',
              quantity: '0',
              address: [
                {
                  detail: '上海虹桥公司',
                  district: '闵行区',
                  city: '上海市',
                  province: '上海',
                },
              ],
              service_info: [
                {
                  serviceName: '代收货款',
                  serviceCode: 'SVC-COD',
                },
                {
                  serviceName: '拦截件',
                  serviceCode: 'SVC-PACKAGE-INTERCEPT',
                },
                {
                  serviceName: '到付',
                  serviceCode: 'SVC-RECEIVER-PAY',
                },
                {
                  serviceName: '保价',
                  serviceCode: 'SVC-INSURE',
                },
              ],
              auth_id: '35661',
              source_type: '12',
              nickname: '快宝网络',
            },
          ],
        },
        yt: {
          taobao: [
            {
              branch_code: '774892',
              brand_code: 'YTO',
              brand_type: 'YTO',
              brand_name: '圆通',
              kb_code: 'yt',
              branch_name: '广西省贺州',
              allocated_quantity: 66,
              cancel_quantity: 8,
              quantity: '0',
              address: [
                {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                  waybill_address_id: '65040019',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
            {
              branch_code: '774892',
              brand_code: 'YTO',
              brand_type: 'YTO',
              brand_name: '圆通',
              kb_code: 'yt',
              branch_name: '广西省贺州',
              allocated_quantity: 0,
              cancel_quantity: 0,
              quantity: '0',
              address: [
                {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                  waybill_address_id: '65040019',
                },
              ],
              segment_code: 'SVC-COD',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
            {
              branch_code: '774892',
              brand_code: 'YTO',
              brand_type: 'YTO',
              brand_name: '圆通',
              kb_code: 'yt',
              branch_name: '广西省贺州',
              allocated_quantity: 0,
              cancel_quantity: 0,
              quantity: '0',
              address: [
                {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                  waybill_address_id: '65040019',
                },
              ],
              segment_code: 'SVC-INTERNATIONAL',
              customer_code_list: null,
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
          ],
          wuliuyun: [
            {
              branch_code: '774892',
              branch_name: '广西省贺州',
              brand_code: 'YTO',
              brand_type: '2',
              brand_name: '圆通',
              kb_code: 'yt',
              allocated_quantity: '81',
              cancel_quantity: '5',
              quantity: '0',
              address: [
                {
                  detail: '水果市场',
                  district: '八步区',
                  city: '贺州市',
                  province: '广西壮族自治区',
                },
              ],
              service_info: {
                serviceName: '拦截件',
                serviceCode: 'SVC-PACKAGE-INTERCEPT',
              },
              auth_id: '35661',
              source_type: '12',
              nickname: '快宝网络',
            },
          ],
        },
        postx: {
          taobao: [
            {
              branch_code: 'POSTB',
              brand_code: 'POSTB',
              brand_type: 'POSTB',
              brand_name: '邮政快包',
              kb_code: 'postx',
              branch_name: null,
              allocated_quantity: 437,
              cancel_quantity: 17,
              quantity: -9999,
              address: [
                {
                  city: '杭州市',
                  detail: '笕桥镇花园兜街221号',
                  district: '江干区',
                  province: '浙江省',
                  waybill_address_id: '117034019',
                },
              ],
              segment_code: 'NORMAL',
              customer_code_list: ['*************'],
              auth_id: '35660',
              source_type: '2',
              nickname: 'alilan02042',
            },
          ],
        },
        fw: {
          pdd_universal: [
            {
              branch_code: '579006000',
              brand_code: 'FENGWANG',
              brand_name: '丰网',
              kb_code: 'fw',
              brand_type: 2,
              branch_name: '丰网速运金华稠江二部',
              allocated_quantity: '33',
              cancel_quantity: '1',
              quantity: '0',
              address: [
                {
                  country: '中国',
                  province: '浙江省',
                  city: '金华市',
                  district: '义乌市',
                  detail: '稠江街道文华路8号',
                },
              ],
              auth_id: '35918',
              source_type: '19',
              nickname: '41014143166_2251799814896318',
            },
          ],
        },
      },
    });
  },
  // 单号设置，编辑单号设置
  'POST /Api/orderManage/WaybillSource/replaceRelation': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 单号设置，新增单号设置
  'POST /Api/orderManage/WaybillSource/addRelation': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 单号记录列表
  'GET /Api/orderManage/WaybillRecord/getRecordList': (req, res) => {
    const result = [];
    Array.from({
      length: 200,
      // eslint-disable-next-line array-callback-return
    }).map((item, index) => {
      result.push({
        id: `${index}`,
        price: `12${index}`,
        brand: 'sf',
        apply_at: `2020年5月${index}日15:16:31`,
        waybill_no: `*********${index}`,
        order_id: `987654321${index}`,
        brand_name: '顺丰',
        courier_info: `张三${index}`,
      });
    });
    res.send({
      msg: '成功',
      code: 0,
      data: {
        list: result,
        page: 1,
        pageSize: 100,
        totalCount: 200,
        totalPages: 2,
      },
    });
  },
  // 微商单号记录列表
  'POST /Api/WsWaybillStatistics/wsWayBillLog': (req, res) => {
    const result = [];
    Array.from({
      length: 50,
    }).forEach((item, index) => {
      result.push({
        data: {
          time: '2020-07-25-2020-07-31',
          id: `${index}2222`,
          share_id: '0',
          order_no: '210209000000017',
          sender_name: '寄件人',
          sender_tel: '',
          sender_mobile: '18321612551',
          sender_province: '上海市',
          sender_city: '上海市',
          sender_district: '长宁区',
          sender_address: '新泾镇通协路建滔商业广场',
          sender_company: '',
          receiver_name: '贝先柳',
          receiver_tel: '',
          receiver_mobile: '18163699217',
          receiver_province: '上海市',
          receiver_city: '上海市',
          receiver_district: '普陀区',
          receiver_address: '通协路靠近建滔商业广场',
          receiver_company: '',
          collection_amount: '0.00',
          to_pay_amount: '0.00',
          order_type: '0',
          create_time: '2021-02-09 17:00:32',
          waybill_no: '9720573258693',
          brand: 'postx',
          print_time: '2021-02-09 17:09:23',
          status: '1',
          retail_id: '3381',
          price: '0.00',
          waybill_status: '未发出',
          brand_name: '中国邮政快递包裹',
        },
        user: {
          id: `${index}333`,
          avatar_url: '',
          nickname: 'My Shop',
          username: '18321612551',
        },
      });
    });
    res.send({
      msg: '成功',
      code: 0,
      data: {
        list: result,
        page: 1,
        page_size: 20,
        total: 200,
      },
    });
  },
  // 单号记录添加下载任务
  'POST /Api/orderManage/WaybillRecord/addTask': (req, res) => {
    res.send({
      code: 0,
      msg: 'ok',
      data: {},
    });
  },
  // 单号记录查看下载任务
  'GET /Api/orderManage/WaybillRecord/getTaskList': (req, res) => {
    const result = [];

    Array.from({
      length: 4,
    }).map((item, index) => {
      result.push({
        exec_time: `2020年5月${index}日15:16:31`,
        status_text: '下载完成',
        status: 1,
        file_name: '2020-5-15-2020-5-15',
        url: '/upload/order/20200602/1.20200602175642.xls',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        pageSize: 15,
        page: 1,
        totalCount: 4,
      },
    });
  },
  // 单号记录，单号使用统计
  'POST /Api/orderManage/WaybillRecord/waybillSourceStatistics': (req, res) => {
    const result = [];

    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        // id: `${index}`,
        courier_name: `${index}张三`,
        courier_phone: `${index}13166252983`,
        price_cnt: '10.00',
        site_name: '222',
        site_id: '222',
        time: '2020-07-25-2020-07-31',
        used_cnt: `${index}`,
        user_id: `${index}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        data: result,
        count: 2,
      },
    });
  },
  // 微商单号记录，单号使用统计
  'POST /Api/WsWaybillStatistics/wsWayBillStatisticsLog': (req, res) => {
    const result = [];

    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        data: {
          num: `${index}`,
          time: '2020-07-25-2020-07-31',
          total_price: '0.00',
        },
        user: {
          id: `user_id_${index}`,
          avatar_url: '',
          nickname: 'My Shop',
          username: '18321612551',
        },
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: result,
    });
  },
  // 单号记录，订单详情
  'GET /Api/orderManage/WaybillRecord/getOrderInfo': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {
        // 订单编号
        order_no: '1',
        // 下单时间
        order_time: '2',
        // 订单状态
        order_status: 8,
        // 受理业务员
        courier_name: '3',
        // 受理业务员手机号
        courier_mobile: '4',
        // 运单号
        waybill_no: '5',
        // 是否实名
        is_real_name: '6',
        // 发货者信息
        sender_name: '7',
        sender_phone: '111****1444',
        sender_province: '9',
        sender_city: '10',
        sender_area: '11',
        // 发件人备注
        remark: '12',
        // 货物类型
        goods_type: '13',
        // 货物重量
        goods_weight: '14',
        // 收货者信息
        receive_name: '15',
        receive_phone: '164****1111',
        receive_province: '17',
        receive_city: '18',
        receive_area: '19',
        receive_address: '20',
      },
    });
  },

  // 单号使用统计，添加下载任务
  'POST /Api/orderManage/WaybillRecord/excelWaybillSourceStatistics': (req, res) => {
    res.send({
      code: 0,
      msg: 'ok',
      data: {},
    });
  },
  // 单号使用统计，查看下载任务
  'POST /Api/orderManage/WaybillRecord/excelWaybillSourceStatisticsList': (req, res) => {
    const result = [];

    Array.from({
      length: 2,
    }).map((item, index) => {
      result.push({
        id: `${index}`,
        shop_id: '7',
        fail_msg: '',
        file_name: '123',
        url: '3333333',
        type: '2',
        condition: { startTime: '', endTime: '', shopName: '', searchName: '' },
        checked: '1',
        add_time: '2020-06-12 17:00:30',
        finished_time: null,
        exec_time: `2020-06-12 17:00:30${index}`,
        status: '0',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        data: result,
        count: 2,
        page: 1,
      },
    });
  },
  // 微商，单号使用统计,，添加下载任务
  'POST /Api/DownTask/addTask': (req, res) => {
    res.send({
      code: 0,
      msg: 'ok',
      data: {},
    });
  },
  // 微商，单号记录、单号使用统计，查看下载任务
  'GET /Api/DownTask/getTaskList': (req, res) => {
    const result = [];

    Array.from({
      length: 20,
    }).forEach((item, index) => {
      result.push({
        exec_time: `2020-06-12 17:00:30${index}`,
        file_name: '123',
        status: '0',
        status_text: '已添加',
        url: '3333333',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        page: 1,
        pageSize: 15,
        totalCount: 20,
        totalPages: 1,
      },
    });
  },
  // 单号使用统计，查看明细列表
  'POST /Api/YZ/Fund/orderStatisticsDetail': (req, res) => {
    const result = [];

    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        order_date: '2020年5月22日11:05:22',
        order_number: `${index}777`,
        brand: `${index}圆通`,
        operator: '夏起翔',
        price: `${index}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        total: 40,
        page: 1,
      },
    });
  },
  // 代收货款管理
  'POST /Api/orderManage/CollectionOfPayment/collectionPaymentlist': (req, res) => {
    const result = [];
    const isOdd = value => value % 2 == 0;

    Array.from({
      length: 50,
    }).map((item, index) => {
      result.push({
        id: `${index}`,
        waybill_no: `2020052618070751496${index}`,
        site_id: `8${index}`,
        shipper_name: `夏起翔${index}`,
        shipper_mobile: `13166252983${index}`,
        shipper_tel: `13166252983${index}`,
        shipping_name: `xqx${index}`,
        shipping_mobile: `13166252983${index}`,
        shipping_tel: `13166252983${index}`,
        collect_courier_id: `1${index}`,
        collect_courier_name: `王路${index}`,
        collect_courier_mobile: `***********${index}`,
        big_customer_name: `12${index}`,
        collection_amount: '4.00',
        pickup_code: 'k25962',
        package_status: '1',
        customer_id: isOdd(index) ? '875112' : '',
        customer_name: index % 3 == 0 ? '王现勇' : '',
        company_name: index % 3 == 0 ? '公司' : '',
        customer_phone: isOdd(index) ? '***********' : '',
        brand: 'sto',
        create_at: '2020年5月27日22:04:10',
        update_at: '2020年5月27日23:04:10',
        status: isOdd(index) ? '1' : '2',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        data: result,
        count: 1000,
        page: 1,
      },
    });
  },
  // 代收货款管理，核销
  'POST /Api/orderManage/CollectionOfPayment/collectionVerification': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 代收货款管理，回款清单，已回款记录
  'POST /Api/orderManage/CollectionOfPayment/writeOffList': (req, res) => {
    const result = [];

    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        collection_amount: `1${index}100.00`,
        company_name: `快宝${index}`,
        count: `${index}0`,
        customer_name: '夏起翔',
        customer_phone: '15270840581',
        remark: null,
        update_at: `2020-06-18 16:57:01${index}`,
        group_waybill_no: '773045033912095',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        data: result,
        count: [{ count: 4 }],
        page: 1,
      },
    });
  },
  // 代收货款管理，回款清单，未回款统计
  'POST /Api/orderManage/CollectionOfPayment/noWriteOffList': (req, res) => {
    const result = [];
    const isOdd = value => value % 2 == 0;
    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        collection_amount: '610.00',
        count: '113',
        customer_name: isOdd(index) ? '夏起翔' : '',
        company_name: isOdd(index) ? '公司名称' : '',
        customer_phone: '15270840581',
        group_id:
          '4865,4611,5125,5127,4618,4875,4877,4879,4881,4375,4376,4378,4634,4636,5153,5154,4388,4904,4650,4907,5165,4910,4399,4912,4915,5171,5177,4411,4412,4669,5185,4418,4420,4421,4422,4426,5195,4432,5202,4694,4696,4444,4956,4958,4447,4450,4706,5218,4965,5221,4455,4713,4459,4972,5230,4720,4721,4723,4981,5241,4988,4480,5004,4749,4750,5264,4498,5270,4761,5017,4763,5019,4764,4765,4510,5284,4777,4779,4527,5295,5299,5050,5306,5051,4541,4797,5313,5059,4550,4810,4818,5331,4572,4829,5085,5086,5087,4576,4579,5092,5093,4584,5096,4585,4586,4596,4853,4598,4855,5111,5117,4350,5118',
        group_waybill_no:
          '202005261806459986,2020052618062631784,2020052618065897027,2020052618065830851,2020052618062689294,2020052618064536699,2020052618064538522,2020052618064517703,2020052618064566202,2020052618061272879,2020052618061211959,2020052618061339155,2020052618062795411,2020052618062753401,2020052618070021258,20200526180700972,2020052618061351185,2020052618064736637,2020052618062883241,2020052618064719398,2020052618070014412,2020052618064869452,2020052618061495663,2020052618064831417,2020052618064898794,2020052618070076626,202005261807004652,2020052618061510274,2020052618061597070,2020052618063038558,202005261807011769,2020052618061625938,202005261806164475,202005261806162542,2020052618061640540,2020052618061690011,2020052618070115531,2020052618061616128,2020052618070170585,2020052618063257278,2020052618063274737,2020052618061717117,2020052618065124470,2020052618065166821,2020052618061799331,2020052618061779609,2020052618063363905,202005261807022463,2020052618065190152,2020052618070233915,2020052618061759170,202005261806',
        remark: null,
        id: `${index}`,

        created_at: `2020年5月${index}日11:05:22`,
        waybill_no: `${index}98765431`,
        operator: '夏起翔',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        data: result,
        count: [{ count: 11 }],
        page: 1,
      },
    });
  },
  // 代收货款管理，查看详情
  'POST /Api/orderManage/CollectionOfPayment/detail': (req, res) => {
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        bindFlag: '0',
        brand: 'ht',
        certificatePath:
          '/x_preview_img_x/BgqPEm6YQD/CloudPrint/2020/07/01/11/38/274306013O1321237966206466769033.jpg',
        channel: 'androids',
        characters: '鲁S济南A136 00 09',
        chargingWeight: '1',
        code1: '136',
        code2: '00',
        code3: '09',
        collectionAmount: '5.00',
        concentratePackage: '济南市内包',
        concentratePackageCode: '',
        createTime: '2020-07-01 11:38:06',
        customLabel: '',
        customerCode: '',
        customerName: '',
        empNo: '',
        freight: '',
        goodsType: '日用品',
        importShopName: '',
        informStatus: '0',
        isEditable: '0',
        isInspection: '1',
        isMonthly: '0',
        isPrint: '0',
        isPrintable: '1',
        isRead: '1',
        isRealname: '已采集',
        isSend: '0',
        mark: '鲁S济南A',
        openId: '',
        orderCategory: '自填',
        orderKinds: 'orderS',
        orderNumber: '507014188600001',
        orgCode: '',
        packageStatus: '',
        packetMoney: '0',
        paymentNotifiable: '0',
        pickerMobile: '*********01',
        pickerName: '夏起翔',
        pickupCode: '0011120',
        price: '0.00',
        printType: '2',
        printedAwardable: '0',
        printerCopyInvisible: '0',
        printerPaperInvisible: '0',
        proposedPrice: '0.00',
        realNameType: '',
        realPay: '0.00',
        receiptsUnderCustody: '0.00',
        receiveAddress: '啊啊啊',
        receiveArea: '黄浦',
        receiveCity: '上海',
        receiveName: '701',
        receivePhone: '18715276549',
        receiveProvince: '上海',
        receiveTel: '',
        remark: '',
        reserveEndTime: null,
        reserveStartTime: null,
        senderAddress: '新泾镇建滔商业广场',
        senderArea: '长宁区',
        senderCity: '上海市',
        senderId: '0',
        senderName: '701',
        senderPhone: '***********',
        senderProvince: '上海市',
        senderTel: '',
        shopId: '67281',
        shopName: '上海快宝公司',
        siteName: '济南市内包',
        source: '',
        status: '2',
        statusText: '已完成',
        stoIsRealname: '0',
        thirdPartyOrderId: '',
        toPayAmount: '',
        wayBillNo: '50911062060959',
        withHoldStatus: '0',
        withHoldType: 'normal',
      },
    });
  },
  // 代收货款管理，下载表格
  'GET /Api/orderManage/CollectionOfPayment/exportOrder': (req, res) => {
    res.send({
      msg: '下载成功',
      code: 0,
      data: {},
    });
  },
  // 客户管理
  'POST /Api/orderManage/Customer/customerList': (req, res) => {
    const result = [];
    Array.from({
      length: 300,
    }).map((item, index) => {
      result.push({
        id: `${index}`,
        work_kb_id: '8139',
        courier_id: '2',
        customer: `${index}快宝急送`,
        linkman_name: 'ffsfs',
        linkman_mobile: '13166252983',
        remark: 'daddsd',
        company_site_id: '10',
        courier_name: '周新瑜',
        courier_phone: '13566666666',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        page_size: 20,
        current_page: 1,
        total_page: 1,
        count: 300,
      },
    });
  },
  // 客户管理，获取公司列表
  'POST /Api/YZ/Fund/companyList': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        company_name: `快宝（上海）网络技术有限公司${index}`,
        company_id: `${index}`,
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
      },
    });
  },
  // 客户管理，添加客户信息
  'POST /Api/orderManage/Customer/addCustomer': (req, res) => {
    res.send({
      msg: '添加成功',
      code: 0,
      data: {},
    });
  },
  // 客户管理，编辑客户信息
  'POST /Api/orderManage/Customer/updateCustomer': (req, res) => {
    res.send({
      msg: '编辑成功',
      code: 0,
      data: {},
    });
  },
  // 客户管理，编辑客户信息
  'POST /Api/orderManage/Customer/deleteCustomer': (req, res) => {
    res.send({
      msg: '删除成功',
      code: 0,
      data: {},
    });
  },
  // 客户管理，获取级联选择器数据
  'POST /Api/orderManage/Customer/getCourierInfoAll': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        'data|1-20': {
          '@increment(1)': {
            value: '@id',
            label: '快宝急送',
            'children|2-10': [
              {
                value: /^1\d{10}/,
                courier_phone: /^1\d{10}/,
                label: `@cname()${mock(/^1\d{10}/)}`,
                courier_no: '@id',
                kdy_id: '@id',
              },
            ],
          },
        },
      }),
    );
  },
  // 订单详情列表
  'POST /Api/YZ/Fund/getOrderDetailList': (req, res) => {
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: {
          company_name: '快宝（上海）网络技术有限公司',
          contacts: '夏起翔',
          phone: '*********',
          operator: '夏起翔',
          info: '农行：*********',
        },
      },
    });
  },
  // 微商户管理
  'POST /Api/Ws/wsInfo': (req, res) => {
    const { page } = req.body;
    res.send(
      mock({
        msg: '暂无结果',
        code: 0,
        data: {
          'list|15': [
            {
              id: '@id()',
              shop_name: '@ctitle(5)',
              phone: /^1\d{10}/,
              courier_name: '@cname()',
              courier_phone: /^1\d{10}/,
              company_site_id: '@id',
              ws_id: '@id',
            },
          ],
          current_page: page,
          page_size: 15,
          count: 150,
        },
      }),
    );
  },
  // 编辑微商客户信息
  'POST /Api/Ws/updateWs': (req, res) => {
    res.send({
      msg: '编辑成功',
      code: 0,
      data: {},
    });
  },
  //
  // 删除微商客户
  'POST /Api/Ws/deleteWs': (req, res) => {
    res.send({
      msg: '删除成功',
      code: 0,
      data: {},
    });
  },
  // 增加微商客户
  'POST /Api/Ws/addWs': (req, res) => {
    res.send({
      msg: '添加成功',
      code: 0,
      data: {},
    });
  },
  // 根据手机号码查询微商信息
  'POST /Api/Ws/searchPhone': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        'data|1': [
          {
            is_register: 1,
            info: {
              shop_name: '我的店铺名',
              mobile: '13166252983',
              id: '17332',
              kb_id: '4643',
            },
          },
          {
            is_register: 0,
            info: {},
          },
        ],
      }),
    );
  },
  // 根据id获取微商客户信息
  'POST /Api/Ws/getTblWsOne': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          id: '11',
          shop_name: '小微企业',
          phone: '1316622983',
          courier_name: '啊嗯27',
          courier_phone: '18715276640',
          courier_no: '74',
        },
      ],
    });
  },

  // 单号设置， 获取微商客户
  'GET /Api/orderManage/WaybillSource/getWechatMerchantList': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          page: 1,
          pageSize: 20,
          totalPages: 1,
          totalCount: 5,
          list: [
            {
              user_id: '17759',
              shop_name: '画不圆的圈',
              phone: '***********',
              waybill_source: [
                {
                  id: '4434',
                  source: {
                    auth_id: '35646',
                    source_type: '6',
                    brand: 'tt',
                    month_no: '0',
                    account: 'ddd',
                    password: '123',
                    customer: 'ddd',
                    quantity: '',
                    brand_name: '天天快递',
                    u_id: 'kop_35646',
                  },
                  status: '0',
                  status_value: '0',
                  price: '0.03',
                  brand_name: '天天',
                  brand_type: 'tt',
                  waybill_quantity: '20',
                },
                {
                  id: '4460',
                  source: {
                    branch_code: '31271',
                    brand_code: 'ZTO',
                    brand_name: '中通快递',
                    kb_code: 'zt',
                    brand_type: 2,
                    branch_name: '高碑店白沟',
                    allocated_quantity: '0',
                    cancel_quantity: '0',
                    quantity: '0',
                    address: [
                      {
                        country: '中国',
                        province: '河北省',
                        city: '保定市',
                        district: '南市区',
                        detail: '快宝测试地址',
                      },
                    ],
                    auth_id: '35659',
                    source_type: '19',
                    nickname: '41014143166_2251799814896318',
                    u_id: 'pdd_universal_31271',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '中通',
                  brand_type: 'zt',
                  waybill_quantity: '10',
                },
                {
                  id: '4467',
                  source: {
                    auth_id: '35635',
                    source_type: '6',
                    brand: 'yd',
                    month_no: '0',
                    account: 'kkk',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '韵达快递',
                    u_id: 'kop_35635',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '韵达',
                  brand_type: 'yd',
                  waybill_quantity: '1',
                },
              ],
            },
            {
              user_id: '332437',
              shop_name: '我的店铺名',
              phone: '***********',
              waybill_source: [
                {
                  id: '4436',
                  source: {
                    auth_id: '35665',
                    source_type: '6',
                    brand: 'yd',
                    month_no: '0',
                    account: '************',
                    password: 'JH7jyrRq5fZDnNBuWscPMhTQ2VAaEd',
                    customer: '',
                    quantity: '',
                    brand_name: '韵达快递',
                    u_id: 'kop_35665',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '韵达',
                  brand_type: 'yd',
                  waybill_quantity: '30',
                },
                {
                  id: '4437',
                  source: {
                    auth_id: '35646',
                    source_type: '6',
                    brand: 'tt',
                    month_no: '0',
                    account: 'ddd',
                    password: '123',
                    customer: 'ddd',
                    quantity: '',
                    brand_name: '天天快递',
                    u_id: 'kop_35646',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '天天',
                  brand_type: 'tt',
                  waybill_quantity: '-1',
                },
                {
                  id: '4438',
                  source: {
                    branch_code: '51405',
                    brand_code: 'ZTO',
                    brand_name: '中通快递',
                    kb_code: 'zt',
                    brand_type: 2,
                    branch_name: '昆山花桥',
                    allocated_quantity: '422',
                    cancel_quantity: '2',
                    quantity: '14',
                    address: [
                      {
                        country: '中国',
                        province: '江苏省',
                        city: '苏州市',
                        district: '昆山市',
                        detail: '曹新路76号(快宝网络测试)',
                      },
                    ],
                    auth_id: '35658',
                    source_type: '18',
                    nickname: 'pdd3982093908',
                    u_id: 'pindd_51405',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '中通',
                  brand_type: 'zt',
                  waybill_quantity: '1',
                },
              ],
            },
            {
              user_id: '17337',
              shop_name: '测试账号',
              phone: '***********',
              waybill_source: [
                {
                  id: '4439',
                  source: {
                    auth_id: '35635',
                    source_type: '6',
                    brand: 'yd',
                    month_no: '0',
                    account: 'kkk',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '韵达快递',
                    u_id: 'kop_35635',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '韵达',
                  brand_type: 'yd',
                  waybill_quantity: '-1',
                },
                {
                  id: '4440',
                  source: {
                    auth_id: '35646',
                    source_type: '6',
                    brand: 'tt',
                    month_no: '0',
                    account: 'ddd',
                    password: '123',
                    customer: 'ddd',
                    quantity: '',
                    brand_name: '天天快递',
                    u_id: 'kop_35646',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '天天',
                  brand_type: 'tt',
                  waybill_quantity: '-1',
                },
                {
                  id: '4447',
                  source: {
                    auth_id: '35664',
                    source_type: '6',
                    brand: 'post',
                    month_no: '0',
                    account: '123',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '邮政快递包裹',
                    u_id: 'kop_35664',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.10',
                  brand_name: '邮政快包',
                  brand_type: 'post',
                  waybill_quantity: '3',
                },
                {
                  id: '4448',
                  source: {
                    auth_id: '35673',
                    source_type: '6',
                    brand: 'sf',
                    month_no: '0',
                    account: '123',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '顺丰速运',
                    u_id: 'kop_35673',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.01',
                  brand_name: '顺丰',
                  brand_type: 'sf',
                  waybill_quantity: '-1',
                },
                {
                  id: '4449',
                  source: {
                    branch_code: '51405',
                    brand_code: 'ZTO',
                    brand_type: 'ZTO',
                    brand_name: '中通',
                    kb_code: 'zt',
                    branch_name: '昆山花桥',
                    allocated_quantity: 16058,
                    cancel_quantity: 28,
                    quantity: 228,
                    address: [
                      {
                        city: '苏州市',
                        detail: '花桥镇',
                        district: '昆山市',
                        province: '江苏省',
                      },
                    ],
                    segment_code: 'NORMAL',
                    auth_id: '35660',
                    source_type: '2',
                    nickname: 'alilan02042',
                    u_id: 'taobao_51405',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '中通',
                  brand_type: 'zt',
                  waybill_quantity: '-1',
                },
                {
                  id: '4459',
                  source: {
                    branch_code: '774892',
                    brand_code: 'YTO',
                    brand_type: 'YTO',
                    brand_name: '圆通',
                    kb_code: 'yt',
                    branch_name: '广西省贺州',
                    allocated_quantity: 25,
                    cancel_quantity: 1,
                    quantity: 1,
                    address: [
                      {
                        city: '上海市',
                        detail: '通协路269号6号楼6楼A单元',
                        district: '长宁区',
                        province: '上海',
                      },
                    ],
                    segment_code: 'NORMAL',
                    auth_id: '35660',
                    source_type: '2',
                    nickname: 'alilan02042',
                    u_id: 'taobao_774892',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '圆通',
                  brand_type: 'yt',
                  waybill_quantity: '-1',
                },
                {
                  id: '4461',
                  source: {
                    auth_id: '35636',
                    source_type: '6',
                    brand: 'jt',
                    month_no: '0',
                    account: '123',
                    password: 'kkk',
                    customer: '',
                    quantity: '',
                    brand_name: '极兔速递',
                    u_id: 'kop_35636',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '极兔',
                  brand_type: 'jt',
                  waybill_quantity: '-1',
                },
                {
                  id: '4482',
                  source: {
                    branch_code: '200003',
                    branch_name: '上海虹桥公司',
                    brand_code: 'STO',
                    brand_type: '2',
                    brand_name: '申通',
                    kb_code: 'sto',
                    allocated_quantity: '965',
                    cancel_quantity: '0',
                    quantity: '241',
                    address: [
                      {
                        detail: '上海虹桥公司',
                        district: '闵行区',
                        city: '上海市',
                        province: '上海',
                      },
                    ],
                    auth_id: '35661',
                    source_type: '12',
                    nickname: '快宝网络',
                    u_id: 'wuliuyun_200003',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '申通',
                  brand_type: 'sto',
                  waybill_quantity: '-1',
                },
              ],
            },
            {
              user_id: '17828',
              shop_name: '旭东小商品城',
              phone: '***********',
              waybill_source: [
                {
                  id: '4422',
                  source: {
                    auth_id: '35665',
                    source_type: '6',
                    brand: 'yd',
                    month_no: '0',
                    account: '************',
                    password: 'JH7jyrRq5fZDnNBuWscPMhTQ2VAaEd',
                    customer: '',
                    quantity: '',
                    brand_name: '韵达快递',
                    u_id: 'kop_35665',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '韵达',
                  brand_type: 'yd',
                  waybill_quantity: '12',
                },
                {
                  id: '4423',
                  source: {
                    auth_id: '35636',
                    source_type: '6',
                    brand: 'jt',
                    month_no: '0',
                    account: '123',
                    password: 'kkk',
                    customer: '',
                    quantity: '',
                    brand_name: '极兔速递',
                    u_id: 'kop_35636',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '极兔',
                  brand_type: 'jt',
                  waybill_quantity: '6',
                },
                {
                  id: '4424',
                  source: {
                    auth_id: '35646',
                    source_type: '6',
                    brand: 'tt',
                    month_no: '0',
                    account: 'ddd',
                    password: '123',
                    customer: 'ddd',
                    quantity: '',
                    brand_name: '天天快递',
                    u_id: 'kop_35646',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '天天',
                  brand_type: 'tt',
                  waybill_quantity: '6',
                },
                {
                  id: '4430',
                  source: {
                    branch_code: '774892',
                    branch_name: '广西省贺州',
                    brand_code: 'YTO',
                    brand_type: '2',
                    brand_name: '圆通',
                    kb_code: 'yt',
                    allocated_quantity: '23',
                    cancel_quantity: '0',
                    quantity: '1',
                    address: [
                      {
                        detail: '水果市场',
                        district: '八步区',
                        city: '贺州市',
                        province: '广西壮族自治区',
                      },
                    ],
                    auth_id: '35661',
                    source_type: '12',
                    nickname: '快宝网络',
                    u_id: 'wuliuyun_774892',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '圆通',
                  brand_type: 'yt',
                  waybill_quantity: '6',
                },
                {
                  id: '4450',
                  source: {
                    auth_id: '35664',
                    source_type: '6',
                    brand: 'post',
                    month_no: '0',
                    account: '123',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '邮政快递包裹',
                    u_id: 'kop_35664',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.50',
                  brand_name: '邮政快包',
                  brand_type: 'post',
                  waybill_quantity: '6',
                },
                {
                  id: '4451',
                  source: {
                    auth_id: '35673',
                    source_type: '6',
                    brand: 'sf',
                    month_no: '0',
                    account: '123',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '顺丰速运',
                    u_id: 'kop_35673',
                  },
                  status: '1',
                  status_value: '1',
                  price: '1.01',
                  brand_name: '顺丰',
                  brand_type: 'sf',
                  waybill_quantity: '6',
                },
                {
                  id: '4458',
                  source: {
                    branch_code: '200003',
                    brand_code: 'STO',
                    brand_type: 'STO',
                    brand_name: '申通',
                    kb_code: 'sto',
                    branch_name: '上海虹桥公司',
                    allocated_quantity: 21052,
                    cancel_quantity: 85,
                    quantity: 27,
                    address: [
                      {
                        city: '上海市',
                        detail: '通协路269号6号楼6楼A单元',
                        district: '长宁区',
                        province: '上海',
                      },
                    ],
                    segment_code: 'NORMAL',
                    auth_id: '35660',
                    source_type: '2',
                    nickname: 'alilan02042',
                    u_id: 'taobao_200003',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '申通',
                  brand_type: 'sto',
                  waybill_quantity: '6',
                },
                {
                  id: '4469',
                  source: {
                    branch_code: '51405',
                    brand_code: 'ZTO',
                    brand_name: '中通快递',
                    kb_code: 'zt',
                    brand_type: 2,
                    branch_name: '昆山花桥',
                    allocated_quantity: '422',
                    cancel_quantity: '2',
                    quantity: '14',
                    address: [
                      {
                        country: '中国',
                        province: '江苏省',
                        city: '苏州市',
                        district: '昆山市',
                        detail: '曹新路76号(快宝网络测试)',
                      },
                    ],
                    auth_id: '35658',
                    source_type: '18',
                    nickname: 'pdd3982093908',
                    u_id: 'pindd_51405',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '中通',
                  brand_type: 'zt',
                  waybill_quantity: '1',
                },
              ],
            },
            {
              user_id: '50',
              shop_name: '杜康2',
              phone: '***********',
              waybill_source: [
                {
                  id: '4442',
                  source: {
                    auth_id: '35635',
                    source_type: '6',
                    brand: 'yd',
                    month_no: '0',
                    account: 'kkk',
                    password: '123',
                    customer: '',
                    quantity: '',
                    brand_name: '韵达快递',
                    u_id: 'kop_35635',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '韵达',
                  brand_type: 'yd',
                  waybill_quantity: '-1',
                },
                {
                  id: '4443',
                  source: {
                    auth_id: '35646',
                    source_type: '6',
                    brand: 'tt',
                    month_no: '0',
                    account: 'ddd',
                    password: '123',
                    customer: 'ddd',
                    quantity: '',
                    brand_name: '天天快递',
                    u_id: 'kop_35646',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '天天',
                  brand_type: 'tt',
                  waybill_quantity: '0',
                },
                {
                  id: '4444',
                  source: {
                    branch_code: '51405',
                    brand_code: 'ZTO',
                    brand_name: '中通快递',
                    kb_code: 'zt',
                    brand_type: 2,
                    branch_name: '昆山花桥',
                    allocated_quantity: '418',
                    cancel_quantity: '2',
                    quantity: '16',
                    address: [
                      {
                        country: '中国',
                        province: '江苏省',
                        city: '苏州市',
                        district: '昆山市',
                        detail: '曹新路76号(快宝网络测试)',
                      },
                    ],
                    auth_id: '35658',
                    source_type: '18',
                    nickname: 'pdd3982093908',
                    u_id: 'pindd_51405',
                  },
                  status: '1',
                  status_value: '1',
                  price: '0.00',
                  brand_name: '中通',
                  brand_type: 'zt',
                  waybill_quantity: '-1',
                },
              ],
            },
          ],
        },
      }),
    );
  },
  // 单号设置，微商编辑单号设置
  'POST /Api/orderManage/WaybillSource/cityShareWechatUpdateRelation': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 单号设置，微商新增单号设置
  'POST /Api/orderManage/WaybillSource/cityShareWechatRelation': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  // 微商单号记录，查看详情
  'POST /Api/WsWaybillStatistics/wsWayBillDetail': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        id: '402',
        share_id: '0',
        order_no: '210304070000065',
        sender_name: '早餐',
        sender_tel: '',
        sender_mobile: '***********',
        sender_province: '广东',
        sender_city: '深圳市',
        sender_district: '龙岗区',
        sender_address: '平湖街道世之鼎物流园',
        sender_company: '',
        receiver_name: '刘煜新',
        receiver_tel: '',
        receiver_mobile: '***********',
        receiver_province: '黑龙江',
        receiver_city: '早餐',
        receiver_district: '大庆',
        receiver_address: '乘风街道东湖10135502',
        receiver_company: '',
        collection_amount: '0.00',
        to_pay_amount: '0.00',
        order_type: '0',
        create_time: '2021-03-04 11:21:28',
        waybill_no: '1136813637527',
        brand: 'post',
        print_time: '2021-03-04 11:21:41',
        status: '1',
        retail_id: '4447',
        price: '0.00',
        realname_status_name: '未实名',
        waybill_status: '未发出',
        note: '这是备注！！！！！',
        courier: {
          courier_id: 0,
          c_order_no: '',
          courier_mobile: '12345678',
          courier_name: '业务员',
          monthly: '',
          is_companypay: '',
          type: '',
          index_shop_id: '',
        },
        item: {
          item_id: '1929351',
          order_id: '*********',
          item_name: '唐僧肉',
          item_short_name: '唐僧肉',
          item_img:
            'http://upload.kuaidihelp.com/vhome/commodities/2019/05/29/201905291514465cee31664fbf0.jpg',
          item_price: '0.01',
          item_weight: '10.00',
          item_freight: '0.00',
          item_num: '5',
          item_note: '备注备注',
          item_sku_id: '',
          is_deleted: '0',
          create_time: '2021-03-04 11:21:28',
          update_time: '0000-00-00 00:00:00',
          item_type: '0',
        },
      },
    });
  },
  // 驿站获取业务员列表
  'POST /Api/orderManage/WaybillSource/getInnerList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        page: 1,
        pageSize: 20,
        totalPages: 1,
        totalCount: 5,
        list: [
          {
            concat_name: '',
            phone: '***********',
            cm_id: '2225711',
            inn_name: '',
            waybill_source: [
              {
                id: '13633',
                source: {
                  auth_id: '129335',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: {
                    auth_id: '129335',
                    source_type: '6',
                    brand: 'postx',
                    account: '********',
                    password: '23333',
                    customer: '',
                    brand_name: '邮政快包',
                  },
                  password: '23333',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129335',
                  id: '13633',
                },
                status: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '-1',
                owner_role: '4',
              },
              {
                id: '13634',
                source: {
                  auth_id: '129336',
                  source_type: '6',
                  brand: 'postx',
                  month_no: '0',
                  account: {
                    auth_id: '129336',
                    source_type: '6',
                    brand: 'postx',
                    account: '1111',
                    password: '2222',
                    customer: '',
                    brand_name: '邮政快包',
                  },
                  password: '2222',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129336',
                  id: '13634',
                },
                status: '1',
                price: '0.00',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '-1',
                owner_role: '4',
              },
              {
                id: '13635',
                source: {
                  auth_id: '129337',
                  source_type: '6',
                  brand: 'zt',
                  month_no: '0',
                  account: {
                    auth_id: '129337',
                    source_type: '6',
                    brand: 'zt',
                    account: '334',
                    password: '444',
                    customer: '',
                    brand_name: '中通快递',
                  },
                  password: '444',
                  customer: '',
                  quantity: '',
                  brand_name: '中通快递',
                  u_id: 'kop_129337',
                },
                status: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
                owner_role: '4',
              },
              {
                id: '13636',
                source: {
                  branch_code: '51405',
                  brand_code: 'ZTO',
                  brand_type: 'ZTO',
                  brand_name: '中通',
                  kb_code: 'zt',
                  branch_name: '昆山花桥',
                  allocated_quantity: 18642,
                  cancel_quantity: 63,
                  quantity: '7',
                  address: [
                    {
                      city: '苏州市',
                      detail: '花桥镇',
                      district: '昆山市',
                      province: '江苏省',
                      waybill_address_id: '13310067',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_51405',
                },
                status: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
                owner_role: '4',
              },
            ],
          },
          {
            concat_name: 'test下属',
            phone: '***********',
            cm_id: '3473003',
            inn_name: 'test下属',
            waybill_source: [
              {
                id: '13245',
                source: {
                  auth_id: '35646',
                  source_type: '6',
                  brand: 'tt',
                  month_no: '0',
                  account: {
                    auth_id: '35646',
                    source_type: '6',
                    brand: 'tt',
                    account: 'ddd',
                    password: '123',
                    customer: 'ddd',
                    brand_name: '天天快递',
                  },
                  password: '123',
                  customer: 'ddd',
                  quantity: '',
                  brand_name: '天天快递',
                  u_id: 'kop_35646',
                },
                status: '1',
                price: '0.01',
                brand_name: '天天',
                brand_type: 'tt',
                waybill_quantity: '500',
                owner_role: '4',
              },
            ],
          },
          {
            concat_name: '李米',
            phone: '***********',
            cm_id: '1957044',
            inn_name: '李米的猜想',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '弄滴',
            phone: '15221444332',
            cm_id: '1957289',
            inn_name: '小江驿站',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '郑超',
            phone: '15082810674',
            cm_id: '1957332',
            inn_name: '快宝开发',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '潘用伟',
            phone: '15201946775',
            cm_id: '2775270',
            inn_name: '测定驿站',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '小辉u',
            phone: '18829900094',
            cm_id: '1957041',
            inn_name: '旺角驿站测试环境1',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '李经理',
            phone: '15099996666',
            cm_id: '2225863',
            inn_name: '陈述log',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '潘用伟',
            phone: '15201946772',
            cm_id: '2225207',
            inn_name: '淞虹驿站',
            waybill_source: [
              {
                id: '10733',
                source: {
                  branch_code: 'POSTB',
                  brand_code: 'POSTB',
                  brand_type: 'POSTB',
                  brand_name: '邮政快包',
                  kb_code: 'postx',
                  branch_name: null,
                  allocated_quantity: 163,
                  cancel_quantity: 13,
                  quantity: 0,
                  address: [
                    {
                      city: '杭州市',
                      detail: '笕桥镇花园兜街221号',
                      district: '江干区',
                      province: '浙江省',
                      waybill_address_id: '117034019',
                    },
                  ],
                  segment_code: 'NORMAL',
                  customer_code_list: ['*************'],
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_POSTB',
                },
                status: '1',
                price: '0.01',
                brand_name: '邮政快包',
                brand_type: 'postx',
                waybill_quantity: '68',
                owner_role: '4',
              },
              {
                id: '13119',
                source: {
                  branch_code: '51405',
                  brand_code: 'ZTO',
                  brand_type: 'ZTO',
                  brand_name: '中通',
                  kb_code: 'zt',
                  branch_name: '昆山花桥',
                  allocated_quantity: 0,
                  cancel_quantity: 0,
                  quantity: 0,
                  address: [
                    {
                      city: '苏州市',
                      detail: '花桥镇',
                      district: '昆山市',
                      province: '江苏省',
                      waybill_address_id: '13310067',
                    },
                  ],
                  segment_code: 'SVC-STAR',
                  customer_code_list: null,
                  auth_id: '35660',
                  source_type: '2',
                  nickname: 'alilan02042',
                  u_id: 'taobao_51405',
                },
                status: '1',
                price: '0.00',
                brand_name: '中通',
                brand_type: 'zt',
                waybill_quantity: '-1',
                owner_role: '4',
              },
            ],
          },
          {
            concat_name: '啦啦',
            phone: '13918521401',
            cm_id: '2128326',
            inn_name: '好友测试',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '赵鸿峰',
            phone: '13015440211',
            cm_id: '2225807',
            inn_name: '发天箱底',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '哦们MSN',
            phone: '13180009000',
            cm_id: '2774866',
            inn_name: '泰迪鹅鹅',
            waybill_source: [
              {
                id: 0,
                source: '',
                status: '暂无',
                price: '暂无',
                brand_name: '暂无',
                brand_type: '暂无',
                waybill_quantity: '暂无',
                owner_role: 0,
              },
            ],
          },
          {
            concat_name: '断小弦',
            phone: '***********',
            cm_id: '1748861',
            inn_name: '断小弦儿',
            waybill_source: [
              {
                id: '13628',
                source: {
                  auth_id: '129276',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: {
                    auth_id: '129276',
                    source_type: '6',
                    brand: 'post',
                    account: '1214',
                    password: '1311',
                    customer: '',
                    brand_name: '邮政快包',
                  },
                  password: '1311',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129276',
                },
                status: '1',
                price: '0.01',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '100',
                owner_role: '4',
              },
              {
                id: '13629',
                source: {
                  auth_id: '129292',
                  source_type: '6',
                  brand: 'post',
                  month_no: '0',
                  account: {
                    auth_id: '129292',
                    source_type: '6',
                    brand: 'post',
                    account: '安达市多大所大所',
                    password: '的方式方式到付发送的',
                    customer: '',
                    brand_name: '邮政快包',
                  },
                  password: '的方式方式到付发送的',
                  customer: '',
                  quantity: '',
                  brand_name: '邮政快包',
                  u_id: 'kop_129292',
                },
                status: '1',
                price: '0.01',
                brand_name: '邮政快包',
                brand_type: 'post',
                waybill_quantity: '100',
                owner_role: '4',
              },
            ],
          },
        ],
      },
    });
  },
  // 驿站编辑单号源
  'POST /Api/orderManage/WaybillSource/addInnerSource': (req, res) => {
    res.send({
      msg: '编辑成功',
      code: 0,
      data: {},
    });
  },
  // 驿站单号记录，获取列表
  'POST /Api/orderManage/WaybillRecord/getInnRecordList': (req, res) => {
    const result = [];
    Array.from({
      length: 50,
    }).forEach((item, index) => {
      result.push({
        id: `${index}`,
        order_id: `987654321${index}`,
        waybill_no: `*********${index}`,
        price: `12${index}`,
        apply_at: `2020年5月${index}日15:16:31`,
        // brand: `sf`,
        brand_name: '顺丰',
        inn_info: `驿站——张三${index}`,
      });
    });
    res.send({
      msg: '成功',
      code: 0,
      data: {
        page: 1,
        pageSize: 20,
        totalPages: 2,
        totalCount: 50,
        list: result,
      },
    });
  },
  // 驿站单号记录，单号使用统计列表
  'POST /Api/orderManage/WaybillRecord/waybillSourceInnStatistics': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).forEach((item, index) => {
      result.push({
        user_id: `${index}`,
        price_cnt: `1${index}`,
        used_cnt: `2${index}`,
        cm_id: `cm_id${index}`,
        cm_name: `cm_id${index}`,
        concat_phone: `${index}13166252983`,
        concat_name: `${index}张三-驿站`,
        time: '2020-07-25-2020-07-31',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        data: result,
        count: 2,
        page: 1,
      },
    });
  },
  // 驿站，单号使用统计，添加下载任务
  'POST /Api/DownTask/YZddTask': (req, res) => {
    res.send({
      code: 0,
      msg: 'ok',
      data: {},
    });
  },
  // 驿站，单号使用统计，查看下载任务
  'POST /Api/DownTask/YZgetTaskList': (req, res) => {
    const result = [];

    Array.from({
      length: 20,
    }).forEach((item, index) => {
      result.push({
        exec_time: `2020-06-12 17:00:30${index}`,
        file_name: '123',
        status: '0',
        status_text: '已添加',
        url: '3333333',
      });
    });
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: result,
        page: 1,
        pageSize: 15,
        totalCount: 20,
        totalPages: 1,
      },
    });
  },
  // 微商单号记录，查看详情
  'POST /Api/WsWaybillStatistics/yzWayBillDetail': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        id: '402',
        share_id: '0',
        order_no: '210304070000065',
        sender_name: '早餐',
        sender_tel: '',
        sender_mobile: '***********',
        sender_province: '广东',
        sender_city: '深圳市',
        sender_district: '龙岗区',
        sender_address: '平湖街道世之鼎物流园',
        sender_company: '',
        receiver_name: '刘煜新',
        receiver_tel: '',
        receiver_mobile: '***********',
        receiver_province: '黑龙江',
        receiver_city: '早餐',
        receiver_district: '大庆',
        receiver_address: '乘风街道东湖10135502',
        receiver_company: '',
        collection_amount: '0.00',
        to_pay_amount: '0.00',
        order_type: '0',
        create_time: '2021-03-04 11:21:28',
        waybill_no: '1136813637527',
        brand: 'post',
        print_time: '2021-03-04 11:21:41',
        status: '1',
        retail_id: '4447',
        price: '0.00',
        realname_status_name: '未实名',
        waybill_status: '未发出',
        note: '这是备注！！！！！',
        courier: {
          courier_id: 0,
          c_order_no: '',
          courier_mobile: '12345678',
          courier_name: '业务员',
          monthly: '',
          is_companypay: '',
          type: '',
          index_shop_id: '',
        },
        item: {
          item_id: '1929351',
          order_id: '*********',
          item_name: '唐僧肉',
          item_short_name: '唐僧肉',
          item_img:
            'http://upload.kuaidihelp.com/vhome/commodities/2019/05/29/201905291514465cee31664fbf0.jpg',
          item_price: '0.01',
          item_weight: '10.00',
          item_freight: '0.00',
          item_num: '5',
          item_note: '备注备注',
          item_sku_id: '',
          is_deleted: '0',
          create_time: '2021-03-04 11:21:28',
          update_time: '0000-00-00 00:00:00',
          item_type: '0',
        },
      },
    });
  },
  // 单号设置，单号充值记录
  'POST /Api/orderManage/WaybillRecord/waybillSourceLog': (req, res) => {
    const { pageNum } = req.body;
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          'data|15': [
            {
              'id|1-10000': 10000,
              'share_id|1-10000': 10000,
              owner_id: '240',
              owner_role: '4',
              user_id: '32',
              user_role: '5',
              'set_type|1': ['unLimit', 'limit'],
              'quantity|1': ['0', '+1', '-2'],
              create_time: '@date',
              source: {
                brand: 'ht',
                title: '3',
                content: '快递公司自有面单',
              },
              vhome_user_id: '32',
              userType: 'vhome',
              name: '微商32',
              courieName: '吧好的VB防护v',
              couriePhone: '15837899712',
              courieTypeName: '微商',
              'brand|1': ['ht', 'zt', 'sf', 'yd'],
              info: '百世快递快递',
              content: '快递公司自有面单(351145_0629)',
              title: '商家账号：351145_0629',
            },
          ],
          page: pageNum,
          total: 40,
        },
      }),
    );
  },
};
