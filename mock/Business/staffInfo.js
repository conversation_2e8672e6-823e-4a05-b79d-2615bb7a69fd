/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  // 车辆信息
  'POST /Api/EmployeeManager/list': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        page: 1,
        page_size: 20,
        list: [
          {
            id: '8',
            shop_id: '1',
            name: '姓名测试44',
            company: '单位测试4',
            post: '岗位测试44',
            job_property: '劳动合同员工',
            vehicle_type: '驾驶证准假车型测试4',
            entry_time: '2024-10-12',
            training_time: '2024-10-13',
            sign_time: '2024-10-14',
            avatar_url:
              'http://upload.kuaidihelp.com/city/images/2024_03/22/165fd5c685f16c109749544.png',
            is_delete: '0',
            created_at: '2024-03-22 10:13:05',
            updated_at: '2024-03-22 10:13:05',
            token: 'fdA71FUf3+V9gU1MOf1jGCXZUZIGqElk8Tkfyyd9YbY=',
          },
          {
            id: '7',
            shop_id: '1',
            name: '姓名测试22',
            company: '单位测试2',
            post: '岗位测试22',
            job_property: '承包区员工',
            vehicle_type: '驾驶证准假车型测试2',
            entry_time: '2024-10-12',
            training_time: '2024-10-13',
            sign_time: '2024-10-14',
            avatar_url: '',
            is_delete: '0',
            created_at: '2024-03-22 10:13:05',
            updated_at: '2024-03-22 10:13:05',
            token: 'rB+IQ5IgL+TM/fvdXgOFESvaOdOyjUee0FHCoozk8Bg=',
          },
          {
            id: '6',
            shop_id: '1',
            name: '姓名测试5',
            company: '单位测试53',
            post: '岗位测试55',
            job_property: '承包区员工',
            vehicle_type: '驾驶证准假车型测试5',
            entry_time: '2024-10-12',
            training_time: '2024-10-13',
            sign_time: '2024-10-14',
            avatar_url: '',
            is_delete: '0',
            created_at: '2024-03-22 10:07:42',
            updated_at: '2024-03-22 10:07:42',
            token: 'hPSTICLrr4j7n1TfPJ1p6JBtA3zVqQIp94bsSJxPpjw=',
          },
          {
            id: '5',
            shop_id: '1',
            name: '姓名测试4',
            company: '单位测试4',
            post: '岗位测试44',
            job_property: '劳动合同员工',
            vehicle_type: '驾驶证准假车型测试4',
            entry_time: '2024-10-12',
            training_time: '2024-10-13',
            sign_time: '2024-10-14',
            avatar_url: '',
            is_delete: '0',
            created_at: '2024-03-22 10:07:42',
            updated_at: '2024-03-22 10:07:42',
            token: '9XQGaxqXNoDiVueNIYtZPuAJdEE30iXArvf/Vf0f+So=',
          },
          {
            id: '4',
            shop_id: '1',
            name: '姓名测试3',
            company: '单位测试33',
            post: '岗位测试33',
            job_property: '劳务合同员工',
            vehicle_type: '驾驶证准假车型测试3',
            entry_time: '2024-10-12',
            training_time: '2024-10-13',
            sign_time: '2024-10-14',
            avatar_url: '',
            is_delete: '0',
            created_at: '2024-03-22 10:07:42',
            updated_at: '2024-03-22 10:07:42',
            token: 'BaGxzKoYEnWgQvukrV/IvdQzSza/zZsvRdglQV8hXGc=',
          },
        ],
        total: '5',
      },
    });
  },
  'POST /Api/EmployeeManager/add': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/EmployeeManager/edit': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/EmployeeManager/delete': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/staff/uploadAvatar': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/EmployeeManager/addFromExcel': (req, res) => {
    res.send({
      code: 1009,
      msg: '表格第57行(不含表头)格式有误：training_time培训时间格式不正确',
      data: {},
    });
  },
};
