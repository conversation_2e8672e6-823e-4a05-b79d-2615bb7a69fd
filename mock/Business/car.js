/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  // 车辆信息
  'POST /Api/Company/carList': (req, res) => {
    res.send({
      msg: '暂无结果',
      code: 0,
      data: {
        list: [
          {
            id: '2',
            car_sn: '120001',
            car_reg_no: '浙B23424',
            driver_name: '王路',
            driver_mobile: '13661916491',
            package_count: '24',
            waybill_count: '134',
            created_at: '2023-12-08 15:16:22',
          },
          {
            id: '1',
            car_sn: '120001',
            car_reg_no: '浙B23423',
            driver_name: '王路',
            driver_mobile: '13661916491',
            package_count: '24',
            waybill_count: '134',
            created_at: '2023-12-08 15:16:22',
          },
        ],
        total: 2,
        page_size: 10,
        page: 1,
      },
    });
  },
  'POST /Api/Company/saveCar': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Company/delCar': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
};
