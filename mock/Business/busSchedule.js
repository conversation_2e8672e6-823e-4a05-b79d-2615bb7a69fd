/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import mockjs from 'mockjs';

const { mock } = mockjs;
export default {
  //
  'POST /Api/Company/timesList': (req, res) => {
    const { page = 1, page_size = 15 } = req.body;
    res.send(
      mock({
        msg: '成功',
        code: 0,
        data: {
          [`list|${page_size}`]: [
            {
              'id|+1': 10,
              times_name: '@name',
              site_name: '@name',
              'times_list|1': ['06:00、7:00、05：00', '18:40、19:00、20:20', '7:05,8:30,9:30'],
            },
          ],
          total: 200,
          page,
        },
      }),
    );
  },
  'POST /Api/Company/delTimes': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Company/saveTimes': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Company/importBusSchedule': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Company/expportBusSchedule': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
};
