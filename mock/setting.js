/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  //配送员列表
  "POST /Api/YZ/CourierStation/settingList": (req, res) => {
    const result=[];
    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        id: `${index}`,
        number: `2004${index}`,
        site_name: `站点${index}`,
        site_id: `${index}`,
        operator: `张${index}`,
        phone: `13344445555`,
        open: index % 2 == 0 ? 1 : 0
      });
    });
    res.send({
      code: 0,
      msg: "成功",
      data: {
        list:result
      }
    });
  }
}