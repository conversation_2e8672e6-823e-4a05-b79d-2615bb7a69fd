/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import moment from "moment";

export default {
  //上交的站点列表
  "POST /Api/YZ/CourierStation/handInDakList": (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        id: `${index}4908`,
        cm_id: "1614976",
        contact_phone: "13524849951",
        company_name: "牛打发了",
        address: "上海市上海市黄浦区啦啦啦",
        pid: "4907",
        is_open: 1,
        is_select: 1,
      });
    });

    res.send({
      msg: "成功",
      code: 0,
      data: {
        list: result,
        total: "40",
        page: 1,
      },
    });
  },
  "POST /Api/YZ/Fund/handInList": (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        order_id: `${index}5506226448200002`,
        create_time: `2020-06-22 17:55:01${index}`,
        freight: "0.01",
        from_kb_id: "8263",
        to_kb_id: "5565",
        from_kb_type: "yz",
        to_kb_type: "yz",
        from_kb_name: "风之谷",
        to_kb_name: "牛打发了",
      });
    });

    res.send({
      msg: "成功",
      code: 0,
      data: {
        path: "https://upload.kuaidihelp.com/finance/excel/2020-08-26/hand_in5f45ce6c40db4.xlsx",
        total: 40,
        count: 40,
        page: "1",
        size: "10",
        list: result,
      },
    });
  },

  "POST /Api/YZ/CourierStation/handInInfo": (req, res) => {
    res.send({
      msg: "成功",
      code: 0,
      data: {
        id: "5309196068600001",
        status: "printed",
        waybill_no: "66000023530301",
        brand: "kbtc",
        shipper_name: "路松松",
        shipper_mobile: "15006182770",
        shipper_tel: "",
        shipper_province: "浙江省",
        shipper_city: "杭州市",
        shipper_district: "下城区",
        shipper_address: "通协路",
        shipping_name: "佛心22222",
        shipping_mobile: "17095130807",
        shipping_tel: "",
        shipping_province: "上海市",
        shipping_city: "上海市",
        shipping_district: "长宁区",
        shipping_address: "秀山路",
        package_weight: "1.00",
        package_info: "日用品",
        package_note: "看看",
        channel: "minpost",
        create_at: "2018-09-19 16:51:26",
        update_at: "2018-10-16 10:59:58",
        is_realname: "0",
        pickup_code: "7FB00051",
        status_desc: "已打印",
        channel_desc: "小邮筒",
        is_realname_desc: "未采集",
      },
    });
  },

  "POST /Api/YZ/CourierStation/feeClose": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },
};
