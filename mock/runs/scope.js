/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default function(req, res) {
  const params = JSON.parse(req.body.data);
  switch (params.run) {
    case "/Delivery/show":
      // 配送范围列表
      const data = [];
      for (let i = 0, len = 2; i < len; i++) {
        data.push({
          id: Math.random(),
          area_name: `配送范围${1 + i}`,
          status: 1,
          area_detail: [
            { lng: 121.374208, lat: i + 31.243715 },
            { lng: 121.355954, lat: i + 31.236682 },
            { lng: 121.340238, lat: i + 31.243611 }
          ]
        });
      }
      res.send({
        code: 0,
        data
      });
      break;
    case "/Delivery/change":
      // 删除，冻结状态切换
      if (params.data.status === 2) {
        // 删除
        res.send({
          code: 0,
          msg: "已删除"
        });
      } else {
        //冻结状态切换
        res.send({
          code: 0,
          msg: `已${params.data.status === 1 ? "开启" : "冻结"}`
        });
      }
      break;
    case "/Delivery/areaAdd":
      const { id } = params.data;
      res.send({
        code: 0,
        data: {
          id: id || Math.random()
        },
        msg: `${id ? "修改" : "添加"}成功`
      });
      // 添加或编辑
      break;
  }
}