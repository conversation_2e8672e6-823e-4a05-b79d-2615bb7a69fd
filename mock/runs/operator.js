/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default function(req, res) {
  const params = JSON.parse(req.body.data);
  switch (params.run) {
    case "/Valuation/add":
      let { type, code = 0 } = params.data;
      res.send({
        code,
        data: {},
        msg: `success`,
      });
      break;
    //  获取公众号号列表
    case "/Delivery/getWxList":
      // 测试使用，bind === true 绑定关系已建立，直接返回绑定工号的快递员列表，否则返回绑定二维码
      if (params.data && (params.data.bind || params.data.wx_mini_prgrm)) {
        const list = [];
        for (let i = 0, len = 3; i < len; i++) {
          list.push({
            iu_id: `11${i}`,
            uid: "o3S2n0ur0mKoGx91TAKohjgPZ-SQ",
            platform: `weixin_shop_${i}`,
            open_platform: `wt_weixin_${i}`,
            platform_type: "weixin",
            nickname: `张${i}`,
            u_note: "樊凯杰",
            is_root: "1",
            is_delete: "0",
            create_time: "2018-11-01 17:11:27",
            update_time: "2018-11-01 17:12:10",
            open_platform_name: `公众号${i}`,
          });
        }
        // params.data.wx_mini_prgrm同时获取公众号、小程序列表
        res.send({
          code: 0,
          msg: "获取成功",
          data: params.data.wx_mini_prgrm
            ? true
            : {
                count: list.length,
                list,
              },
        });
      } else {
        res.send({
          code: 1000,
          msg: "请先绑定公众号",
          data: {},
        });
      }
      break;
    //  扫描后轮询绑定关系
    case "/Delivery/loopCheckQrImg":
      res.send({
        code: params.data.code || 0,
        msg: "已建立绑定关系",
        data: {
          ...params.data,
        },
      });
      break;
    //  获取绑定二维码
    case "/Delivery/auth":
      res.send({
        code: 0,
        msg: "成功",
        data: {
          url:
            "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHn7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyMl8zc2RVdEplRGkxZTZhcDFzMUsAAgQuyBlcAwRYAgAA",
          content: "http://weixin.qq.com/q/022_3sdUtJeDi1e6ap1s1K",
          sceneId: 327807379,
        },
      });
      break;
    //  获取快递员列表
    case "/Delivery/getWxCourierList":
      const courier_list = [];
      for (let i = 0, len = 10; i < len; i++) {
        courier_list.push({
          courier_phone: `1831111111${i}`,
          courier_name: `李${i}`,
          is_exists: i % 2 === 0 ? 1 : 0,
        });
      }
      res.send({
        code: 0,
        msg: "成功",
        data: {
          page: params.data.page || 1,
          size: 20,
          total: 30,
          list: courier_list,
        },
      });
      break;
    //  单量限额设置为不限
    case "/Courier/setItemToCache":
      res.send({
        code: 0,
        msg: "修改成功",
        data: {},
      });
      break;
    //  单量限额修改单量
    case "/Courier/changeNumber":
      res.send({
        code: 0,
        msg: "修改成功",
        data: {},
      });
      break;
    case "/Delivery/cancleAssociate":
      res.send({
        code: 0,
        msg: "已取消关联",
      });
      break;
    //  快拣功能
    case "/Valuation/multiAdd":
      res.send({
        code: 0,
        data: {},
        msg: `success`,
      });
      break;
    //  巴枪
    case "/Valuation/gun":
      res.send({
        code: 0,
        data: {},
        msg: `success`,
      });
      break;
    //  巴枪下拉
    case "/Valuation/select":
      res.send({
        code: 0,
        data: {
          reslout:[
            { brand: "申通", brand_en: "sto", phone: "13661964640", cm_id: 1228367, ali_brand: "STO" },
            { brand: "中通", brand_en: "zt", phone: " 15885240804", cm_id: 1352139, ali_brand: "ZTO" },
            { brand: "韵达", brand_en: "yd", phone: "15201736791", cm_id: 989488, ali_brand: "YUNDA" }
          ]
        },
        msg: `success`,
      });
      break;
  }
}
