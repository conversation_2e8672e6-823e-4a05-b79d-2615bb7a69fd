/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default function(req, res) {
  const params = JSON.parse(req.body.data);
  switch (params.run) {
    // 订单统计
    case "/Order/stat":
      res.send({
        code: 0,
        data: {
          published: { num: parseInt(1000 * Math.random()) },
          accepted: { num: parseInt(1000 * Math.random()) },
          acquired: { num: parseInt(1000 * Math.random()) },
          arrived: { num: parseInt(1000 * Math.random()) },
          canceled: { num: parseInt(1000 * Math.random()) },
        },
      });
      break;
    // 订单调度列表
    case "/Order/orderForm":
      const list = [];
      for (let i = 0, len = 10; i < len; i++) {
        list.push({
          orderNumber: `88888888${i}`,
          appointTime: "2018-12-20 17:53:25",
          finishTime: "2018-12-21 17:53:25",
          refundTime: "",
          arriveTime: "2018-12-20 17:09:25",
          cancelTime: "2018-12-21 10:53:25",
          acquireTime: "2019-01-02 14:40:00",
          acceptTime: "2018-12-20 17:08:54",
          publishTime: "2018-12-19 17:53:25",
          senderName: "乐超",
          senderMobile: "15331838138",
          senderPhone: "",
          senderProvince: "上海市",
          senderCity: "上海市",
          senderArea: "闵行区",
          senderAddress: "吴中路285号",
          brand: "",
          status: params.data.target || "published",
          freight: "61.67",
          receipts: "0.00",
          earningss: "",
          pathDistance: "4.76",
          receiveMobile: "18779155888",
          receivePhone: "",
          receiveName: "薄晨",
          receiveProvince: "上海市",
          receiveCity: "上海市",
          receiveArea: "长宁区",
          receiveAddress: "淞虹路365号",
          goodsInfo: "衣服",
          goodsNote: "康师傅方便面，好吃看得见。",
          goodsWeight: "10.48",
          goodsWay: "send",
          pickupCode: "0000974",
          shipperLng: "107.176048",
          shipperLat: "39.555813",
          shippingLng: "",
          shippingLat: "",
          courierId: `000${i}`,
          courierName: params.data.target !== "published" ? "跑得快" : "",
          courierMobile: "18311111111",
        });
      }
      res.send({
        code: 0,
        data: {
          page: params.data.page || 1,
          pages: 2,
          count: 50,
          list,
        },
        msg: "",
      });
      break;
    //  分配订单
    case "/Order/orderAccept":
      res.send({
        code: 0,
        data: {},
        msg: "已分配",
      });
      break;
    //  取消订单
    case "/Order/orderCancel":
      res.send({
        code: 0,
        data: {},
        msg: "已取消",
      });
      break;
    //    调度快递员列表
    case "/Order/courierLeftList":
      const result = [];
      for (let i = 0, len = 10; i < len; i++) {
        result.push({
          num: parseInt(1000 * Math.random()),
          courier_id: `000${i}`,
          courier_name: `张${i}`,
          courier_mobile: `18311111111${i}`,
          is_work: params.data.is_work || 0,
        });
      }
      res.send({
        code: 0,
        data: {
          working: 10,
          no_working: 100,
          result,
        },
        msg: "",
      });
      break;
  }
}
