/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import moment from 'moment';
import allotRuns from './runs/allot';
import scopeRuns from './runs/scope';
import operatorRuns from './runs/operator';
import { account } from './account';
import { mock } from 'mockjs';

const dataFormat = 'YYYY-MM-DD';

let count = 1;

export default {
  // 信息品牌列表
  'POST /Api/Handle/run': (req, res) => {
    const params = JSON.parse(req.body.data);
    switch (params.run) {
      case '/Valuation/show':
        res.send({
          code: 0,
          msg: '成功',
          data: [
            {
              id: '1',
              shop_id: '7',
              name: '春节计价',
              status: '0',
              basis_weight: '1.2',
              over_basis_weight: '1.2',
              basis_fee: '1.2',
              over_basis_fee: '1.2',
              basis_km: '1.2',
              over_basis_km: '1.2',
              created_at: '2018-12-04 14:23:12',
              updated_at: '2018-12-05 16:03:14',
              deleted_at: null,
            },
            {
              id: '4',
              shop_id: '7',
              name: '夏节计价',
              status: '0',
              basis_weight: '1.2',
              over_basis_weight: '1.2',
              basis_fee: '1.2',
              over_basis_fee: '1.2',
              basis_km: '1.2',
              over_basis_km: '1.2',
              created_at: '2018-12-04 14:43:06',
              updated_at: '2018-12-11 14:59:28',
              deleted_at: null,
            },
            {
              id: '6',
              shop_id: '7',
              name: '秋节计价',
              status: '0',
              basis_weight: '1.2',
              over_basis_weight: '99.9',
              basis_fee: '1.2',
              over_basis_fee: '1.2',
              basis_km: '1.2',
              over_basis_km: '1.2',
              created_at: '2018-12-04 14:51:51',
              updated_at: '2018-12-05 14:17:56',
              deleted_at: null,
            },
            {
              id: '9',
              shop_id: '7',
              name: '冬节计价',
              status: '0',
              basis_weight: '1.2',
              over_basis_weight: '99.9',
              basis_fee: '1.2',
              over_basis_fee: '1.2',
              basis_km: '1.2',
              over_basis_km: '1.2',
              created_at: '2018-12-04 15:57:46',
              updated_at: '2018-12-05 14:18:40',
              deleted_at: null,
            },
            {
              id: '10',
              shop_id: '7',
              name: '四节计价',
              status: '0',
              basis_weight: '1.2',
              over_basis_weight: '1.2',
              basis_fee: '1.2',
              over_basis_fee: '1.2',
              basis_km: '1.2',
              over_basis_km: '1.2',
              created_at: '2018-12-04 16:47:19',
              updated_at: '2018-12-11 15:02:39',
              deleted_at: null,
            },
          ],
        });
        break;
      case '/Valuation/addPlan':
        res.send({
          code: 0,
          msg: '成功',
          data: {},
        });
        break;
      case '/DeliveryOrderCount/historyDetails':
        const data1 = [];
        for (let i = 0; i < 30; i++) {
          data1.push({
            id: `6312145345400004${i}`,
            status: 'finished',
            brand: '',
            shipper_uid: '7',
            shipper_name: '王莽1',
            shipper_mobile: '13612345678',
            shipper_tel: '',
            shipper_province: '上海市',
            shipper_city: '上海市',
            shipper_district: '闵行区',
            shipper_address: '吴中路285号',
            collect_shop_id: '456',
            collect_courier_id: '0',
            collect_courier_mobile: '',
            collect_courier_name: '',
            shipping_uid: '0',
            shipping_name: '赵柳2',
            shipping_mobile: '13987654321',
            shipping_tel: '',
            shipping_province: '上海市',
            shipping_city: '上海市',
            shipping_district: '长宁区',
            shipping_address: '淞虹路365号',
            package_weight: '0.00',
            package_info: '',
            package_note: '',
            package_way: 'send',
            freight: '10.00',
            receipts: '0.00',
            earnings: '0.00',
            path_distance: '10.10',
            canceler: 'system',
            is_receipted: '0',
            is_auto: '0',
            settlement_rule: '',
            pickup_code: '0000866',
            lng: null,
            lat: null,
            published_at: '2019-01-03 14:50:54',
            accepted_at: '2018-12-28 10:09:45',
            acquired_at: null,
            canceled_at: null,
            arrived_at: '2018-12-28 10:09:54',
            finished_at: '2018-12-13 17:59:50',
            appointed_at: null,
          });
        }
        res.send({
          code: 0,
          msg: '获取成功',
          data: {
            count: data1.length,
            page: params.data.page || 1,
            list: data1,
          },
        });
        break;
      case '/DeliveryOrderCount/shopHistoryStatistics':
        res.send({
          code: 0,
          msg: 'success',
          data: {
            list: [
              {
                arrived_ymd: moment(new Date())
                  .add(-1, 'day')
                  .format(dataFormat),
                id: '6312145345400004',
                collect_courier_id: '0',
                collect_courier_name: '',
                receipts_muns: '0.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '1',
              },
              {
                arrived_ymd: moment(new Date())
                  .add(-2, 'day')
                  .format(dataFormat),
                id: '6312046077400007',
                collect_courier_id: '238',
                collect_courier_name: '',
                receipts_muns: '420.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '5',
              },
              {
                arrived_ymd: '2018-12-02',
                id: '6312046077400008',
                collect_courier_id: '238',
                collect_courier_name: '',
                receipts_muns: '80.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '1',
              },
              {
                arrived_ymd: '2018-12-01',
                id: '6312046077400009',
                collect_courier_id: '238',
                collect_courier_name: '',
                receipts_muns: '240.00',
                earnings_muns: '0.00',
                is_receipted_muns: '1',
                count: '3',
              },
              {
                arrived_ymd: '2018-11-30',
                id: '6312046077503898',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '263600.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3295',
              },
              {
                arrived_ymd: '2018-11-29',
                id: '6312046077414114',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '270080.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3376',
              },
              {
                arrived_ymd: '2018-11-28',
                id: '6312046077454243',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '276320.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3454',
              },
              {
                arrived_ymd: '2018-11-27',
                id: '6312046077438554',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '267040.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3338',
              },
              {
                arrived_ymd: '2018-11-26',
                id: '6312046077478790',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '279920.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3499',
              },
              {
                arrived_ymd: '2018-11-25',
                id: '6312046077457786',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '276400.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3455',
              },
              {
                arrived_ymd: '2018-11-24',
                id: '6312046077461507',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '284560.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3557',
              },
              {
                arrived_ymd: '2018-11-23',
                id: '6312046077485110',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '286160.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3577',
              },
              {
                arrived_ymd: '2018-11-22',
                id: '6312046077496441',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '292000.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3650',
              },
              {
                arrived_ymd: '2018-11-21',
                id: '6312046077491686',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '285280.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3566',
              },
              {
                arrived_ymd: '2018-11-20',
                id: '6312046077436013',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '278000.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3475',
              },
              {
                arrived_ymd: '2018-11-19',
                id: '6312046077469024',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '286000.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3575',
              },
              {
                arrived_ymd: '2018-11-18',
                id: '6312046077440990',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '278560.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3482',
              },
              {
                arrived_ymd: '2018-11-17',
                id: '6312046077508542',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '284640.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3558',
              },
              {
                arrived_ymd: '2018-11-16',
                id: '6312046077495451',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '280800.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3510',
              },
              {
                arrived_ymd: '2018-11-15',
                id: '6312046077438081',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '284000.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3550',
              },
              {
                arrived_ymd: '2018-11-14',
                id: '6312046077485772',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '281680.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3521',
              },
              {
                arrived_ymd: '2018-11-13',
                id: '6312046077472504',
                collect_courier_id: '233',
                collect_courier_name: '王麻子',
                receipts_muns: '279280.00',
                earnings_muns: '0.00',
                is_receipted_muns: '0',
                count: '3491',
              },
            ],
            top_list: {
              '2018-12-19': ['123'],
            },
          },
        });
        break;
      case '/Api/Delivery/changeValuation':
        res.send({
          code: 0,
          msg: '成功',
          data: {},
        });
        break;
      case '/Api/Delivery/deleteValuation':
        res.send({
          code: 0,
          msg: '成功',
          data: {},
        });
        break;
      case '/Api/Delivery/getCostList':
        const data3 = [];
        for (let i = 0; i < 30; i++) {
          data3.push({
            key: i,
            create_time: moment(new Date())
              .add(-i, 'day')
              .format(dataFormat),
            sender_name: '谷合成',
            sender_phone: '***********',
            account: 652 * (i + 1),
            direction: 45 * (i + 1),
            fee: 1388 * (i + 1),
            company_fee: 652 * (i + 1),
            sender_fee: 342 * (i + 1),
            count: i * 32,
            status: i % 2 ? '1' : '0',
          });
        }

        res.send({
          code: 0,
          msg: '获取成功',
          data: {
            total: data3.length,
            page: 1,
            size: 10,
            result: data3,
          },
        });
        break;
      case '/Api/Delivery/giveCostOut':
        res.send({
          code: 0,
          msg: '发放成功',
          data: {},
        });
        break;
      case '/Api/Delivery/costSetting':
        res.send({
          code: 0,
          msg: '设置成功',
          data: {},
        });
        break;
      case '/FundsAccount/latestFlow': // 获取最近一月资金流水
        const data4 = [];
        for (let i = 0; i < 30; i++) {
          data4.push({
            date: moment(new Date())
              .add(-i, 'day')
              .format(dataFormat),
            in: 45 * (i + 1),
            out: 67 * (i + 1),
          });
        }
        res.send({
          code: 0,
          msg: '获取成功',
          data: data4,
        });
        break;
      case '/FundsAccount/accountBalance': // 获取资金余额
        res.send({
          code: 0,
          msg: '成功',
          data: {
            avail_money: '2111.68',
            red_packet: 0,
            withdrawable_money: 1110,
            frozen_money: '0.00',
            baidu_userid: '',
            baidu_account: '',
            // alipay_name: "<EMAIL>",
            alipay_userid: '',
            alipay_account: 'alipay_name',
            wxpay_name: '',
            wxpay_openid: '',
            score: '0',
            total_money: 0,
            can_cash_money: 0,
            can_sms_count: 0,
            can_ivr_count: 10,
            cash_money_info: {
              withdrawable_money: '0.00',
              withdrawable_money2: '0.00',
            },
            sms_count_info: {
              fee_count: 0,
              free_count: '0',
              give_count: 0,
            },
            ivr_count_info: {
              fee_count: 0,
              free_count: '10',
            },
            sms_price: '1',
          },
        });
        break;
      case '/FundsAccount/withdraw':
        res.send({
          code: 0,
          msg: '提现成功',
          data: {},
        });
        break;
      case '/FundsAccount/bind': // 绑定账号
        res.send({
          code: 0,
          msg: '绑定成功',
          data: {},
        });
        break;
      case '/FundsAccount/capitalFlow2': // 获取资金流水详细
        const data5 = [];
        for (let i = 0; i < 30; i++) {
          data5.push({
            id: i,
            cm_id: i % 2 ? ['1233333', '123123'] : i % 3 ? ['1233333'] : [],
            type: i % 2 ? '提现' : i % 3 ? '发放配送员配送费' : '退款',
            type_desc: '提现',
            order_number: `cash5c0f8bedca501${i}${params.data.accountType}`,
            money: (i + 1) * 1.8,
            create_time: moment(new Date())
              .add(-i, 'day')
              .format(dataFormat),
            trans_type: 'out',
          });
        }

        res.send({
          code: 0,
          msg: '获取成功',
          data: {
            total: data5.length,
            page: parseInt(params.data.page),
            pageSize: 20,
            list: data5,
          },
        });
        break;
      case '/ShippingFee/add':
        res.send({
          code: 0,
          msg: '设置成功',
          data: {},
        });
        break;
      case '/FundsAccount/transType2':
        res.send({
          code: 0,
          msg: '获取成功',
          data: {
            order_cz: '充值',
            cash: '提现',
            order_city: '配送费收入',
            assign: '发放配送员配送费',
            refund: '退款',
          },
        });
        break;
      case '/Order/feeSettlementList':
        const a = [
          {
            arrived_ymd: '2018-12-17',
            id: '****************',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts_muns: '4.22',
            earnings_muns: '4.02',
            is_receipted_muns: '0',
            count: '20',
          },
          {
            arrived_ymd: '2018-12-14',
            id: '****************',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts_muns: '0.00',
            earnings_muns: '0.00',
            is_receipted_muns: '0',
            count: '27',
          },
          {
            arrived_ymd: null,
            id: '6312046077408997',
            collect_courier_id: '233',
            collect_courier_name: '王麻子',
            collect_courier_mobile: '',
            receipts_muns: '8795440.00',
            earnings_muns: '0.00',
            is_receipted_muns: '0',
            count: '109943',
          },
          {
            arrived_ymd: null,
            id: '6312046076800001',
            collect_courier_id: '238',
            collect_courier_name: '',
            collect_courier_mobile: '',
            receipts_muns: '344.00',
            earnings_muns: '0.00',
            is_receipted_muns: '0',
            count: '6',
          },
          {
            arrived_ymd: null,
            id: '6312046077200004',
            collect_courier_id: '237',
            collect_courier_name: '',
            collect_courier_mobile: '',
            receipts_muns: '205.00',
            earnings_muns: '0.00',
            is_receipted_muns: '1',
            count: '3',
          },
          {
            arrived_ymd: null,
            id: '6312046077400012',
            collect_courier_id: '231',
            collect_courier_name: '张三',
            collect_courier_mobile: '',
            receipts_muns: '80.00',
            earnings_muns: '0.00',
            is_receipted_muns: '0',
            count: '1',
          },
          {
            arrived_ymd: null,
            id: '6312046077400013',
            collect_courier_id: '232',
            collect_courier_name: '李四',
            collect_courier_mobile: '',
            receipts_muns: '80.00',
            earnings_muns: '0.00',
            is_receipted_muns: '0',
            count: '1',
          },
          {
            arrived_ymd: null,
            id: '6312145345400004',
            collect_courier_id: '0',
            collect_courier_name: '',
            collect_courier_mobile: '',
            receipts_muns: '0.00',
            earnings_muns: '0.00',
            is_receipted_muns: '0',
            count: '1',
          },
        ];
        const b = [
          {
            arrived_ymd: '2018-12-17',
            id: '6312175276800001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312176048300004',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312176053300007',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '5%',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312176122400001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '5%',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175297300004',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175300300007',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175306100013',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175330800016',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175405300004',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175489300004',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175402600001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175491900007',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '****************',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175278200004',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175336800019',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312176043300001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312176071600001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '5%',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175290500001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175303400010',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
          {
            arrived_ymd: '2018-12-17',
            id: '6312175470700001',
            collect_courier_id: '237',
            collect_courier_name: '裴淞源',
            collect_courier_mobile: '***********',
            receipts: '0.00',
            earnings: '0.00',
            is_receipted: '0',
            settlement_rule: '',
          },
        ];
        const { courier_id } = params.data;
        res.send({
          code: 0,
          msg: 'success',
          data: {
            count: courier_id ? b.length : a.length,
            list: courier_id ? b : a,
          },
        });
        break;
      case '/FundsAccount/charging':
        res.send({
          code: 0,
          msg: 'success',
          data: {
            out_trade_no: 's_c5c1b08277096c',
            qrcode_url: 'https://qr.alipay.com/bax00786paiuv4k4nk564018',
          },
        });
        break;
      case '/Valuation/switch':
        res.send({
          code: 0,
          msg: 'success',
          data: {},
        });
        break;
      case '/Valuation/delete':
        res.send({
          code: 0,
          msg: 'success',
          data: {},
        });
        break;
      case '/ShippingFee/index':
        res.send({
          code: 0,
          msg: 'success',
          data: [
            {
              id: '1',
              shop_id: '7',
              rate: '0',
              money: '10.0',
              send: '0',
              shipping_rule: '1',
              created_at: '2018-12-11 13:32:04',
              updated_at: '2018-12-11 13:31:46',
              deleted_at: null,
            },
          ],
        });
        break;
      case '/FundsAccount/chargingCheck':
        count++;
        count > 10
          ? res.send({
              code: 0,
              msg: '充值成功',
              data: 'success',
            })
          : res.send({
              code: 0,
              msg: '充值中',
              data: 'padding',
            });
        break;
      case '/Order/callPay':
        res.send({
          code: 0,
          msg: '已发放',
          data: {},
        });
        break;
      case '/Auth/logout':
        res.send({
          code: 0,
          msg: '获取成功',
          data: '',
        });
        break;
      case '/Company/getCompanyInfo':
        res.send({
          code: 0,
          msg: '获取成功',
          data: {
            is_active: '0',
            phone: '***********',
            shop_id: '1',
            name: 'api',
          },
        });
        break;
      case '/FundsAccount/changeWithdrawToAvail':
        res.send({
          code: 0,
          msg: '转移成功',
          data: {},
        });
        break;
      case '/FundsAccount/smsCharging':
        res.send(
          mock({
            code: 0,
            msg: '成功',
            data: {
              out_trade_no: 's_c6d696723836bb691',
              qrcode_url: 'https://img.kuaidihelp.com/xyt/qrcode.png',
            },
          }),
        );
        break;
      case '/FundsAccount/smsChargingCheck':
        res.send(
          mock({
            code: 0,
            msg: '成功',
            data: {
              order_number: 's_c56d12e92f1e7355d',
              money: '8.80',
              'status|1': ['0', '1'],
              status_desc: '充值中',
            },
          }),
        );
        break;
      default:
    }
    // 调度相关
    allotRuns(req, res);
    // 配送范围
    scopeRuns(req, res);
    //  同城 - 业务员
    operatorRuns(req, res);
    // 账户权限
    account(req, res);
  },
};

// 站点列表
export function callPay(req, res) {}
