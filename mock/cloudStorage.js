/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  'POST /v1/TotalDistribution/BottomOrderCloud/getPackageList': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [
          {
            level: 1,
            amount: 30,
            sum: 10,
          },
          {
            level: 2,
            amount: 80,
            sum: 30,
          },
          {
            level: 3,
            amount: 240,
            sum: 200,
          },
          {
            level: 4,
            amount: 700,
            sum: 600,
          },
        ],
      }),
    );
  },
  'POST /v1/TotalDistribution/BottomOrderCloud/getUpgradationCloudStorageCost': (req, res) => {
    res.send({
      code: 0,
      data: 51,
    });
  },
  'POST /v1/TotalDistribution/BottomOrderCloud/editCloudStorageSwitch': (req, res) => {
    res.send({
      code: 0,
    });
  },
  'POST /v1/TotalDistribution/BottomOrderCloud/editSpaceFullType': (req, res) => {
    res.send({
      code: 0,
    });
  },
  'POST /v1/TotalDistribution/BottomOrderCloud/packageChange': (req, res) => {
    res.send({
      code: 0,
    });
  },
  'POST /v1/TotalDistribution/BottomOrderCloud/getCloudStorageInfo': (req, res) => {
    res.send(
      mock({
        code: 0,
        data: {
          'status|1': ['1', '0'],
          'level|1': ['1', '2', '3', '4'],
          create_time: '@datetime',
          expiration_time: '2023-09-28',
          'space_full_type|1': ['1', '0'],
          'auto_renew|1': ['1', '0'],
          'used_sum|10000-100000': 10000,
          relegate_info: {
            'level|1-4': 1,
          },
          'source|1': ['1', '0'],
        },
      }),
    );
  },
  'POST /v1/TotalDistribution/DeliveryAutoArrival/getSwitch': (req, res) => {
    res.send(
      mock({
        code: 0,
        data: {
          delivery_auto_arrival: '1',
          sign_auto_delivery: '1',
          brand_time_check: '1',
          notice_switch: '1',
          'yt_repeat_pi|1': ['0', '1'],
          'record_repeat_check|1': ['0', '1'],
          'forbid_change_mixed_upload_type|1': ['0', '1'],
          'sto_deny_repeat_pi|1': ['0', '1'],
          'zt_deny_repeat_pi|1': ['0', '1'],
          'app_modify_repeat_pi|1': ['0', '1'],
        },
      }),
    );
  },

  'POST /v1/TotalDistribution/DeliveryAutoArrival/setSwitch': (req, res) => {
    res.send({
      code: 0,
    });
  },

  'POST /v1/TotalDistribution/DeliveryAutoArrival/editBrandConfig': (req, res) => {
    res.send({
      code: 0,
    });
  },

  'POST /v1/TotalDistribution/DeliveryAutoArrival/getBrandConfig': (req, res) => {
    if (req.body.type == 0) {
      res.send(
        mock({
          code: 0,
          msg: 'success',
          data: [
            {
              shop_id: '7',
              config_detail: '',
              config_state: '0',
              brand: 'sto',
              brandCn: '@cword(3,5)',
            },
            {
              shop_id: '7',
              config_detail: '',
              config_state: '0',
              brand: 'ztgp',
              brandCn: '@cword(3,5)',
            },
            {
              shop_id: '7',
              config_detail: { vehicle_no: 'CQ20230907' },
              config_state: '0',
              brand: 'yt',
              brandCn: '@cword(3,5)',
            },
            {
              shop_id: '7',
              config_detail: '',
              config_state: '0',
              brand: 'yd',
              brandCn: '@cword(3,5)',
            },
            {
              shop_id: '7',
              config_detail: { vehicle_no: '444' },
              config_state: '1',
              brand: 'jt',
              brandCn: '@cword(3,5)',
            },
            {
              shop_id: '7',
              config_detail: '',
              config_state: '1',
              brand: 'ems',
              brandCn: '@cword(3,5)',
            },
          ],
        }),
      );
    }
    if (req.body.type == 1) {
      res.send({
        code: 0,
        msg: 'success',
        data: [
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'sto',
            type: '1',
            brandCn: '申通',
          },
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'ztgp',
            type: '1',
            brandCn: '中通',
          },
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'yt',
            type: '1',
            brandCn: '圆通',
          },
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'yd',
            type: '1',
            brandCn: '韵达',
          },
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'jt',
            type: '1',
            brandCn: '极兔',
          },
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'ems',
            type: '1',
            brandCn: '邮政',
          },
        ],
      });
    }
    if (req.body.type == 2) {
      res.send({
        code: 0,
        msg: 'success',
        data: [
          {
            shop_id: '7',
            config_detail: null,
            config_state: '0',
            brand: 'all',
            type: '2',
            brandCn: null,
          },
          {
            shop_id: '7',
            config_detail: 0,
            config_state: '0',
            brand: 'sto',
            type: '2',
            brandCn: '申通',
          },
          {
            shop_id: '7',
            config_detail: 0,
            config_state: '0',
            brand: 'ztgp',
            type: '2',
            brandCn: '中通',
          },
          {
            shop_id: '7',
            config_detail: 0,
            config_state: '0',
            brand: 'yt',
            type: '2',
            brandCn: '圆通',
          },
          {
            shop_id: '7',
            config_detail: 0,
            config_state: '0',
            brand: 'yd',
            type: '2',
            brandCn: '韵达',
          },
          {
            shop_id: '7',
            config_detail: 0,
            config_state: '0',
            brand: 'jt',
            type: '2',
            brandCn: '极兔',
          },
          {
            shop_id: '7',
            config_detail: 0,
            config_state: '0',
            brand: 'ems',
            type: '2',
            brandCn: '邮政',
          },
        ],
      });
    }
    if (req.body.type == 3) {
      res.send({
        code: 0,
        msg: 'success',
        data: [
          {
            shop_id: '7',
            config_detail: '代收,到付,拦截,尊享',
            config_state: '0',
            brand: 'ztgp',
            type: '3',
            brandCn: '中通',
          },
          {
            shop_id: '7',
            config_detail: '代收,到付,通缉,电联',
            config_state: '0',
            brand: 'yt',
            type: '3',
            brandCn: '圆通',
          },
          {
            shop_id: '7',
            config_detail: '代收,到付,拦截,电联,派送,签单返回',
            config_state: '0',
            brand: 'yd',
            type: '3',
            brandCn: '韵达',
          },
          {
            shop_id: '7',
            config_detail: '到付,拦截,电联',
            config_state: '0',
            brand: 'jt',
            type: '3',
            brandCn: '极兔',
          },
          {
            shop_id: '7',
            config_detail: '撤单',
            config_state: '0',
            brand: 'ems',
            type: '3',
            brandCn: '邮政',
          },
        ],
      });
    }
  },
  'POST /v1/TotalDistribution/DeliveryAutoArrival/getAllType': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        ztgp: ['代收', '到付', '拦截', '尊享'],
        yt: ['代收', '到付', '通缉', '电联'],
        yd: ['代收', '到付', '拦截', '电联', '派送', '签单返回'],
        jt: ['到付', '拦截', '电联'],
        ems: ['撤单'],
      },
    });
  },
};
