/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from "mockjs";
import { randomValue } from "./_utils";

const { mock } = mockjs;

const defaultResponse = (req, res) => {
  res.send({
    code: 0,
    msg: "success",
    data: {},
  });
};

export default {
  // 获取驿站列表
  "POST /Api/YZ/CourierStation/citysiteFeelist": (req, res) => {
    const result = [];
    Array.from({
      length: req.body.pageSize,
    }).map((item, index) => {
      result.push({
        id: `608313903802612${index}${req.body.page}`,
        waybill_no: `9884027606510${index}${req.body.page}`,
        brand: "postx",
        collect_courier_id: "2225710",
        charging_weight: "1",
        complete_at: "2021-08-31 10:50:38",
        is_compute: "2",
        fee: "0",
        inn_name: "徐肖磊测试驿站",
        quotation_name: "",
        collect_courier_mobile: "18516011018",
        quotation_id: null,
        shop_id: "240",
        shipping_province: "浙江",
      });
      return item;
    });

    res.send({
      code: 0,
      msg: "成功",
      data: {
        list: [...result],
        total: 500,
        page: req.body.page,
        pageSize: req.body.pageSize,
      },
    });
  },

  "POST /Api/YZ/CourierStation/getDownloadList": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: {
        total: 10,
        list: [
          {
            id: "14",
            params:
              "SELECT tbl_order_quotation_fee.waybill_no,tbl_order_quotation_fee.brand,tbl_order_quotation_fee.fee,tbl_order_quotation_fee.quotation_name,tbl_order_quotation_fee.charging_weight,tbl_order_quotation_fee.inn_name,tbl_order_quotation_fee.collect_courier_mobile,tbl_order_quotation_fee.complete_at,tbl_order_quotation_fee.is_compute FROM tbl_order_quotation_fee WHERE tbl_order_quotation_fee.shop_id = '240' AND tbl_order_quotation_fee.complete_at >= '2021-08-10' AND tbl_order_quotation_fee.complete_at <= '2021-09-09 23:59:59' AND tbl_order_quotation_fee.waybill_no IN ('776006482155487')  ORDER BY tbl_order_quotation_fee.complete_at desc",
            is_finish: "1",
            add_time: "2021-09-09 14:25:22",
            update_at: "2021-09-09 15:19:06",
            file_path: "/my_order_yq/2021/09/09/1631171946833017.xlsx",
            type: "9",
            listCount: "1",
            file_name: "新零售-2021-09-09 15:19:06",
            lin_id: "240",
            taskStatus: "1",
            end_time: "2021-09-09 15:19:06",
          },
          {
            id: "15",
            params:
              "SELECT tbl_order_quotation_fee.waybill_no,tbl_order_quotation_fee.brand,tbl_order_quotation_fee.fee,tbl_order_quotation_fee.quotation_name,tbl_order_quotation_fee.charging_weight,tbl_order_quotation_fee.inn_name,tbl_order_quotation_fee.collect_courier_mobile,tbl_order_quotation_fee.complete_at,tbl_order_quotation_fee.is_compute FROM tbl_order_quotation_fee WHERE tbl_order_quotation_fee.shop_id = '240' AND tbl_order_quotation_fee.complete_at >= '2021-08-11' AND tbl_order_quotation_fee.complete_at <= '2021-09-10 23:59:59' AND tbl_order_quotation_fee.waybill_no IN ('9884027606510','1161626727758','JT0003203793594','75800667385024','1156736609058','773115566411308','773115565287161','773115565162697','773115558352097','773115557859468','773115543531320','1196715984057','773113892585602','773113889121944')  ORDER BY tbl_order_quotation_fee.complete_at desc  ",
            is_finish: "0",
            add_time: "2021-09-10 13:50:22",
            update_at: null,
            file_path: null,
            type: "9",
            listCount: "14",
            file_name: null,
            lin_id: "240",
            taskStatus: "0",
            end_time: null,
          },
        ],
      },
    });
  },
  "POST /Api/YZ/CourierStation/feeCompute": defaultResponse,
  "POST /Api/YZ/CourierStation/feeDelete": defaultResponse,
  "POST /Api/YZ/CourierStation/updateWeight": defaultResponse,
  "POST /Api/YZ/CourierStation/downloadFee": defaultResponse,
  "POST /Api/YZ/CourierStation/importWeight": (req, res) => {
    res.send({ code: 1001, msg: "上传文件时出错：末充许的类型", data: {} });
  },
  "GET /Api/YZ/CourierStation/download": defaultResponse,
  "POST /Api/YZ/CourierStation/getSubDaksList": (req, res) => {
    const orderList = [
      { id: "4539", cm_id: "2113758", company_name: "郑州卫校超市" },
      { id: "4529", cm_id: "2111718", company_name: "乐到家便利店" },
      { id: "4527", cm_id: "2112934", company_name: "康桥悦城三号院北门翼便利店" },
      { id: "4522", cm_id: "2112134", company_name: "古荥镇菜鸟驿站" },
      { id: "4519", cm_id: "2111196", company_name: "管城西街15号便利店" },
      { id: "4515", cm_id: "2110186", company_name: "泰和路88号院" },
      { id: "4508", cm_id: "2100627", company_name: "站马屯社区" },
      { id: "4506", cm_id: "2108756", company_name: "裕鸿韵达" },
      { id: "4497", cm_id: "2108145", company_name: "顺和北街" },
      { id: "4483", cm_id: "2106385", company_name: "紫兰苑南门西妈妈驿站" },
      { id: "4480", cm_id: "2105918", company_name: "申通" },
      { id: "4472", cm_id: "2059390", company_name: "晶华城快宝驿站" },
      { id: "4456", cm_id: "2099213", company_name: "新世纪华联超市" },
      { id: "4452", cm_id: "2102854", company_name: "朝阳小区倍全便利快宝驿站" },
      { id: "4451", cm_id: "2102780", company_name: "超级闪送便利店" },
      { id: "4450", cm_id: "2102552", company_name: "明天花园快宝驿站" },
      { id: "4431", cm_id: "2101381", company_name: "水境怡园" },
      { id: "4414", cm_id: "2099833", company_name: "天天快递" },
      { id: "4413", cm_id: "2099785", company_name: "翼来宝西亚斯物流区" },
      { id: "4405", cm_id: "2098907", company_name: "南刘庄2号院" },
      { id: "4401", cm_id: "2089975", company_name: "荥阳市豫龙镇代理点" },
      { id: "4377", cm_id: "2095083", company_name: "郑州工业应用技术学院" },
    ];
    res.send({
      code: 0,
      msg: "success",
      data: orderList,
    });
  },
};
