/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 获取所有下属驿站
export function getYzList(req, res) {
  const list = [];
  Array.from({ length: 70 }).map((v, i) => {
    list.push({
      id: `${i}`,
      cm_id: `${i}`,
      kb_id: `${i}`,
      company_name: `下属驿站${i}`,
      phone: `${i}`,
      ec_name: `快递柜${i}`,
      ec_id: `${i}`,
    });
  });

  res.send({
    code: 0,
    msg: 'success',
    data: list,
  });
}

// 创建报表任务
export function getExportTask(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: null,
  });
}

// 创建报表任务
export function getReport(req, res) {
  const list = [];
  Array.from({ length: 10 }).map((item, index) => {
    list.push({
      id: `00${index}`,
      file_path:
        index === 0
          ? 'https://web.umeng.com/main.php?c=site&a=show&ajax=module=report&tab=0&search=&downloadType=csv'
          : '',
      status: '-1',
      msg: '处理中',
      operator: '操作人',
      file_content: '报表内容',
      create_time: `2019-04-03 15:13:00`,
      file_name: 'asdqwdasda',
      file_type: '1',
      new_file_name: '统计日期范围+分公司+报表类型',
    });
  });
  res.send({
    code: 0,
    msg: '成功',
    data: list,
  });
}

// 重新生成
export function regeneration(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: null,
  });
}

// 获取报表下载类型
export function getReportListType(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      {
        option: 'all',
        content: '全部报表类型',
      },
      {
        option: '1',
        content: '快件信息下发',
      },
      {
        option: '2',
        content: '扫描记录明细',
      },
      {
        option: '3',
        content: '扫描统计汇总',
      },
      {
        option: '4',
        content: '派件统计汇总',
      },
      {
        option: '5',
        content: '派费汇总',
      },
      {
        option: '6',
        content: '派费明细',
      },
      {
        option: '7',
        content: '扫描识别记录',
      },
      {
        option: '8',
        content: '特殊件操作记录',
      },
      {
        option: '9',
        content: '业务员看板汇总',
      },
      {
        option: '10',
        content: '自定义扫描记录',
      },
      {
        option: '11',
        content: '进港分拣',
      },
      {
        option: '12',
        content: '格口统计',
      },
    ],
  });
}
// 获取报表下载获取分类
export function getReportType(req, res) {
  res.send({
    code: 0,
    msg: '成功',
    data: [
      {
        option: 'inn',
        content: '驿站',
      },
      {
        option: 'gp',
        content: '共配',
      },
    ],
  });
}
