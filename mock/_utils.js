/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isFunction } from 'lodash';
import Mock from 'mockjs';

export function delay(then, timer) {
  const list = [0, 500, 1000, 2000, 3000];
  return setTimeout(then, timer || list[parseInt(Math.random() * list.length, 10)]);
}

export function randomValue(list) {
  return list[parseInt(Math.random() * list.length, 10)];
}

export function randomOrderDetail() {
  const goodsList = [
    '日用品',
    '数码产品',
    '衣物',
    '食物',
    '文件',
    '自定义',
    '生鲜：鲫鱼',
    '生鲜：水果',
  ];
  const state = [
    { status: '2', text: '已完成' },
    { status: '1', text: '未完成' },
    { status: '3', text: '已打印' },
    { status: '4', text: '已取消' },
  ];
  const brands = [
    { brand: 'sto', short_name: '申通' },
    { brand: 'zt', short_name: '中通' },
    { brand: 'yt', short_name: '圆通' },
    { brand: 'yd', short_name: '韵达' },
    { brand: 'post', short_name: '邮政' },
  ];
  const isRealnames = ['已实名', '未实名'];
  const orderCategorys = ['orderDak', 'orderWkd'];
  const goodsType = randomValue(goodsList);
  const { status, text: statusText } = randomValue(state);
  const { brand, short_name } = randomValue(brands);
  const isRealname = randomValue(isRealnames);
  const orderCategory = randomValue(orderCategorys);
  return {
    goodsType,
    status,
    statusText,
    brand,
    short_name,
    isRealname,
    orderCategory,
  };
}

export function mockRandomNumber(num) {
  return Math.round(Math.random() * (num || 10));
}

/**
 *
 * @descriptions mock列表
 * @param p0
 * @param opts
 * @returns
 */
export function mockList(p0, opts, req) {
  const { page = 1, size = 30, page_size = size } = req?.body;
  const {
    _random,
    _key = '',
    _id = 'id',
    _idMax = 6,
    _max = page_size,
    _formatter = d => d,
    ...restOpts
  } = opts || {};
  const list = [];
  let len = 0;

  const isEmpty = _random && !!Math.round(Math.random());

  // eslint-disable-next-line no-cond-assign
  while (((len = list.length), len < _max) && !isEmpty) {
    list.push(
      Mock.mock(
        typeof p0 === 'function'
          ? p0(list)
          : {
              ...(_id
                ? {
                    [_id]: `${page}_` + `${list.length}`.padStart(_idMax, '0'),
                  }
                : {}),
              ...p0,
            },
      ),
    );
  }
  let k = _key;
  let extraData = null;
  if (Object.keys(restOpts).length > 0) {
    // 有其他配置项
    k = k || 'list';
    extraData = Mock.mock(restOpts);
  }
  if (k) {
    return _formatter({ [k]: list, ...extraData });
  }
  return _formatter(list);
}

/**
 *
 * @descriptions mock单项数据
 * @param data
 * @returns
 */
export function mockItem(data) {
  const { data: mockData } = Mock.mock(Array.isArray(data) ? { 'data|1': data } : { data });
  return mockData;
}

/**
 *
 * @descriptions 模拟数据响应
 * @param req
 * @param res
 */
export function mockResponse(params, ag1 = mockList, ag2 = null) {
  return (req, res) => {
    if (typeof params === 'undefined') {
      res.send(
        Mock.mock({
          'data|1': [
            { code: 1, msg: 'error', data: {} },
            { code: 0, msg: 'success', data: !isFunction(ag1) ? ag1 : {} },
          ],
        }).data,
      );
      return;
    }

    let mockCreate = null;
    let opts = null;
    if (typeof ag1 === 'function') {
      mockCreate = ag1;
      opts = ag2;
    } else {
      // ag1 配置null时可以执行 mockItem;
      // ag2 配置了方法，ag1不为null，正确执行ag2;
      mockCreate = ag1 && typeof ag2 !== 'function' ? mockList : ag2 || mockItem;
      opts = ag1;
    }
    res.send({
      code: 0,
      msg: 'success',
      data: mockCreate(typeof params === 'function' ? params(req, res) : params, opts, req),
    });
  };
}

/**
 *
 * @description 手机号
 * @returns
 */
export function mockPhone() {
  return /^1[2-9]{1}\d{9}$/;
}

/**
 *
 * @description 城市
 * @param keysMap
 * @returns
 */
export function mockCities(keysMap, more) {
  if (more) {
    return function() {
      return mockCities();
    };
  }

  const { province: p = 'province', city: c = 'city', district: d = 'district' } = keysMap || {};
  const [province, city, district] = Mock.mock('@county(true)').split(/\s/);
  return {
    [p]: province,
    [c]: city,
    [d]: district,
  };
}
