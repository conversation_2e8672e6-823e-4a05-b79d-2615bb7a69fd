/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  // 微信管理，轮询，获取二维码是否被扫
  "POST /v1/PlatformManage/Auth/checkScanCodeStatus": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: 1,
    });
  },
  // 微信管理，授权平台
  "POST /v1/PlatformManage/Auth/authPlatform": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: true,
    });
  },

  // 微信管理，获取授权二维码内容
  "POST /v1/PlatformManage/Auth/authQrcodeInfo": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: [
        {
          platform: "wt_weixin_9",
          platformType: "weixin_mini",
          platformName: "快宝云打印",
        },
        {
          platform: "wt_weixin_99",
          platformType: "weixin",
          platformName: "快宝云打印公众号1",
        },
        {
          platform: "wt_weixin_929",
          platformType: "weixin",
          platformName: "快宝云打印公众号2",
        },
      ],
    });
  },
  // 微信管理，设置，已授权平台列表
  "POST /v1/PlatformManage/Auth/platformList": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: [
        {
          platform: "wt_weixin_9",
          platformType: "weixin_mini",
          platformName: "快宝云打印",
        },
        // {
        //   platform: "wt_weixin_9",
        //   platformType: "weixin",
        //   platformName: "快宝云打印公众号",
        // },
      ],
    });
  },

  // 微信管理，获取授权二维码链接
  "POST /v1/PlatformManage/Auth/authQrcode": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: {
        url:
          "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHn7zwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyMl8zc2RVdEplRGkxZTZhcDFzMUsAAgQuyBlcAwRYAgAA",
        content: "http://weixin.qq.com/q/022_3sdUtJeDi1e6ap1s1K",
        sceneId: 327807379,
      },
    });
  },

  // 微信管理，数据中心，导出数据
  "POST /v1/PlatformManage/FanStatistics/fanStatisticsExport": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: "https://upload.kuaidihelp.com/kb_citysite/courierexcel/2021-02-22/6941613977706.xlsx",
    });
  },
  // 微信管理，数据中心，获取粉丝数据
  "POST /v1/PlatformManage/FanStatistics/platformFanStatistics": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: [
        {
          allCount: 2000011,
          monthCount: 2011,
          platformType: "weixin",
          platform: "weixin_shop_36",
        },
      ],
    });
  },
  // 微信管理，数据中心，业务员列表数据
  "POST /v1/PlatformManage/FanStatistics/fanStatistics": (req, res) => {
    const list = [];
    const { type, page } = req.body;
    Array.from({
      length: 20,
    }).forEach((item, index) => {
      list.push({
        id: `${index}`,
        name: `业务员${index}${type}`,
        account: `业务员账号${index}2345678${type}`,
        address: `站点${index}${type}`,
        weixin: {
          monthCount: `${index}`,
          allCount: `${index}00`,
        },
        weixin_mini: {
          monthCount: `${index}1`,
          allCount: `${index}01`,
        },
      });
    });

    res.send({
      msg: "获取成功",
      code: 0,
      data: {
        total: page == 1 ? 160 : "",
        page,
        size: 20,
        list,
      },
    });
  },
  // 微信管理，数据中心，快宝驿站列表数据
  "POST /Api/wechat/datacenter/kbStationList": (req, res) => {
    const list = [];
    Array.from({
      length: 160,
    }).forEach((item, index) => {
      list.push({
        site: `驿站${index}`,
        site_account: `驿站账号${index}2345678`,
        address: `驿站地址${index}`,
        open_fans: `公众号本月粉丝${index}`,
        open_total_fans: `公众号累计粉丝${index}`,
        miniapp_fans: `小程序本月粉丝${index}`,
        miniapp_total_fans: `小程序累计粉丝${index}`,
      });
    });

    res.send({
      msg: "获取成功",
      code: 0,
      data: {
        total: 160,
        page: 1,
        pageSize: 20,
        list,
      },
    });
  },
  // 微信管理，群发推送，直接推送文本
  "POST /v1/PlatformManage/Push/publishPushTask": (req, res) => {
    res.send({
      msg: "推送成功",
      code: 0,
      data: {},
    });
  },
  // 微信管理，群发推送，链接替换，获取是否有替换的模板
  "POST /v1/PlatformManage/Push/getTemplateUrl": (req, res) => {
    res.send({
      msg: "添加成功",
      code: 0,
      data: {
        url: "https://www.baidu.com",
        miniprogram: {
          appid: "wx********",
          pagepath: "page/index",
        },
        expireIn: "85803",
      },
    });
  },
  // 微信管理，群发推送，链接替换，获取是否有替换的模板
  "POST /v1/PlatformManage/Push/setTemplateUrl": (req, res) => {
    res.send({
      msg: "替换成功",
      code: 0,
      data: {},
    });
  },
  // 微信管理，群发推送，添加关键词回复
  "POST /v1/PlatformManage/Reply/addKeyWordReply": (req, res) => {
    res.send({
      code: 0,
      msg: "添加成功",
      data: {},
    });
  },
  // 微信管理，群发推送，关键词回复列表
  "POST /v1/PlatformManage/Reply/keyWordReplyList": (req, res) => {
    const list = Array.from({
      length: 10,
    }).map((_, index) => ({
      key_word_id: index,
      key_word: `${index}_关键词`,
      content: `${index}_test_content`,
    }));
    res.send({
      code: 0,
      msg: "",
      data: list,
    });
  },
  // 微信管理，群发推送，删除关键词回复
  "POST /v1/PlatformManage/Reply/delKeyWordReply": (req, res) => {
    res.send({
      code: 0,
      msg: "删除成功",
      data: {},
    });
  },
  // 微信管理，群发推送，修改关键词回复
  "POST /v1/PlatformManage/Reply/upKeyWordReply": (req, res) => {
    res.send({
      code: 0,
      msg: "修改成功",
      data: {},
    });
  },
  // 微信管理，群发推送，测试关键词回复，被关注回复
  "POST /v1/PlatformManage/Reply/testKeyWordReply": (req, res) => {
    res.send({
      code: 1,
      msg: "已推送",
      data: {},
    });
  },
  // 微信管理，群发推送，被关注回复内容
  "POST /v1/PlatformManage/Reply/subscribeReply": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: "<a href='http://www.kuaidihelp.com'>这个一个测试关注推送</a>",
    });
  },
  // 微信管理，群发推送，编辑关注回复
  "POST /v1/PlatformManage/Reply/editSubscribeReply": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: true,
    });
  },
  // 微信管理，二维码，业务员列表数据
  "POST /v1/PlatformManage/Salesman/salesmanList": (req, res) => {
    const list = [];
    const { page, type } = req.body;
    const typeName = type === "dak" ? "驿站" : "业务员";
    Array.from({
      length: 20,
    }).forEach((item, index) => {
      list.push({
        id: `${index}`,
        name: `二维码${typeName}${index}`,
        account: `二维码${typeName}账号${index}2345678`,
        address: `上海市 上海市 长宁区 淞虹路${index}`,
      });
    });

    res.send({
      msg: "获取成功",
      code: 0,
      data: {
        total: page == 1 ? 160 : "",
        page,
        size: 20,
        list,
      },
    });
  },
  // 微信管理，二维码，查看二维码
  "POST /v1/PlatformManage/Qrcode/salesmanQrcode": (req, res) => {
    res.send({
      msg: "获取成功",
      code: 0,
      data: {
        qrcodeUrl:
          "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQHK8TwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyU0JJaHBQOFFjcjIxMDAwMHcwMzgAAgS4EKNbAwQAAAAA",
      },
    });
  },

  // 微信管理，二维码，创建导出任务
  "POST /v1/PlatformManage/Qrcode/exportSalesmanQrcode": (req, res) => {
    res.send({
      code: 0,
      msg: "创建导出任务成功",
      data: true,
    });
  },

  // 微信管理，二维码，获取导出结果再进行下载
  "POST /v1/PlatformManage/Qrcode/exportSalesmanQrcodeFile": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: "https://upload.kuaidihelp.com/kb_citysite/courierexcel/2021-02-22/6941613977706.xlsx",
      // data: '',
    });
  },

  // 微信管理，二维码，修改业务员
  "POST /v1/PlatformManage/Qrcode/changeQrcodeSalesman": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: "",
    });
  },

  // 微信管理，设置，设置列表
  "POST /v1/PlatformManage/Config/cityConfigInfo": (req, res) => {
    res.send({
      msg: "获取成功",
      code: 0,
      data: {
        pay_status: "1",
        integral_status: "2",
        advertising_status: "1",
        relation_services_status: "2",
        attention_gzh_status: "2",
        custom_menu: "2",
        revenue_advertise: "2",
        promote_miniprogram: "2",
      },
    });
  },
  // 微信管理，设置，在线支付设置开启关闭
  "POST /Api/PayStatus/handlePayStatus": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，关注公众号设置开启关闭
  "POST /Api/AttentionGzh/handleAttention": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，开启/关闭积分功能
  "POST /Api/Integral/updateIntegralStatus": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，积分功能，获取积分设置详情
  "GET /Api/Integral/showIntegral": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {
        id: "1",
        shop_id: "619",
        status: "1",
        cloud_name: "云店名称",
        cloud_id: "2",
        new_user: {
          point: "9",
          status: "1",
        },
        bind_phone: {
          point: "2",
          status: "2",
        },
        online_pay: {
          point: "4",
          status: "1",
        },
        integral_name: "快宝加盟商",
      },
    });
  },
  // 微信管理，设置，积分功能，绑定云店账号密码
  "POST /Api/Integral/bindCloud": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，积分功能，保存积分设置
  "POST /Api/Integral/handleIntegral": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，获取广告位设置
  "GET /Api/Advertising/getChooseShowRecord": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {
        weixin: {
          status: 1,
          check_piece_page: 1,
          logistics_details_page: 2,
          subscribe_get_page: 2,
          order_submit_page: 1,
          stage_home_page: 2,
          order_details_page: 1,
        },
        weixinMini: {
          status: 2,
          check_piece_page: 1,
          logistics_details_page: 2,
          subscribe_get_page: 1,
          order_submit_page: 1,
          stage_home_page: 2,
          order_details_page: 2,
        },
      },
    });
  },
  // 微信管理，设置，广告位，设置广告位
  "POST /Api/Advertising/setChooseShowRecord": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，广告位列表
  "GET /Api/Advertising/showAdvertising": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: [
        {
          id: "20",
          shop_id: "91",
          url_type: "1",
          app_id: "12312313",
          image_url:
            "http://upload.kuaidihelp.com/advertising/images/2021_08/11/161137d707181a170913784.png",
          advertising_url: "../pijj",
          status: "1",
        },
        {
          id: "21",
          shop_id: "91",
          url_type: "2",
          image_url: "http://upload.kuaidihelp.com/1",
          advertising_url:
            "www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111www.baidu.com111",
          status: "2",
        },
        {
          id: "23",
          shop_id: "91",
          url_type: "2",
          image_url: "http://upload.kuaidihelp.com/1",
          advertising_url: "www.baidu.com",
          status: "1",
        },
        {
          id: "24",
          shop_id: "91",
          url_type: "1",
          app_id: "12312313",
          image_url: "",
          advertising_url: "www.baidu.com222",
          status: "2",
        },
      ],
    });
  },
  // 微信管理，设置，广告位，删除广告位
  "POST /Api/Advertising/deleteAdvertising": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，广告位排序
  "POST /Api/Advertising/sortAdvertising": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，图片上传
  "POST /Api/Advertising/uploadImage": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data:
        "http://upload.kuaidihelp.com/advertising/images/2021_08/17/1611b78381e589310873569.jpg",
    });
  },
  // 微信管理，设置，广告位，添加广告
  "POST /Api/Advertising/createAdvertising": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，修改广告
  "POST /Api/Advertising/updateAdvertising": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，更改广告状态
  "POST /Api/Advertising/statusUpdate": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，获取流量主广告设置列表
  "POST /Api/Advertising/showFlowAd": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: [
        {
          id: "30",
          shop_id: "285",
          type: "1",
          ad_id: "djfijfidsafjoiquii2",
          position: ["logistics_details_page", "subscribe_get_page"],
          status: "1",
          created_at: "2022-02-09 15:06:55",
          updated_at: "2022-02-09 15:20:09",
        },
        {
          id: "31",
          shop_id: "285",
          type: "2",
          ad_id: "djfijfidsafjoiquii2222222222",
          position: ["stage_home_page"],
          status: "2",
          created_at: "2022-02-09 15:06:55",
          updated_at: "2022-02-09 15:20:09",
        },
        {
          id: "32",
          shop_id: "285",
          type: "3",
          ad_id: "djfijfidsafj",
          position: ["order_details_page"],
          status: "1",
          created_at: "2022-02-09 15:06:55",
          updated_at: "2022-02-09 15:20:09",
        },
      ],
    });
  },
  // 微信管理，设置，广告位，添加、设置流量主广告
  "POST /Api/Advertising/setFlowAd": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，删除流量主广告
  "POST /Api/Advertising/delFlowAd": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，广告位，流量主广告上下线
  "POST /Api/Advertising/setFlowAdStatus": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，联系客服，获取客服信息
  "GET /Api/CustomerService/showService": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {
        id: "9",
        shop_id: "91",
        services_qrcode: "http://upload.kuaidihelp.com/1knj",
        services_phone: ["17606137848", "18764893482"],
        // services_qrcode: "",
        // servicePhones: [],
      },
    });
  },
  // 微信管理，设置，联系客服，修改添加客服信息
  "POST /Api/CustomerService/updateService": (req, res) => {
    res.send({
      msg: "success",
      code: 0,
      data: {},
    });
  },
  // 微信管理，设置，联系客服，上传二维码
  "POST /Api/CustomerService/uploadImage": (req, res) => {
    res.send({
      code: 0,
      msg: "上传成功",
      data:
        "http://upload.kuaidihelp.com/customer_service/qrcode/2021_08/17/1611b78381e589310873569.jpg",
    });
  },
  // 微信管理，获取需要解绑平台的二维码
  "POST /v1/PlatformManage/Unbundle/unbundleQrcode": (req, res) => {
    res.send({
      code: 0,
      msg: "",
      data: {
        url:
          "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQG68DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyYVlldGRkdEplRGkxSTFKMU54MVgAAgSp6oFhAwRYAgAA",
        content: "http://weixin.qq.com/q/02aYetddtJeDi1I1J1Nx1X",
        sceneId: "381775387444444",
      },
    });
  },
  // 微信管理，查询用户解绑的结果
  "POST /v1/PlatformManage/Unbundle/unbundleQrcodeInfo": (req, res) => {
    res.send({
      code: 0,
      msg: "解除成功",
      data: 0,
    });
  },
  // 微信管理，设置，自定义菜单，获取自定义菜单
  "POST /v1/PlatformManage/Menu/menu": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {
        menu: {
          button: [
            {
              type: "click",
              name: "个人中心",
              key_word_content: "个人中心",
              key: "click_key1",
              sub_button: [],
            },
            {
              name: "本地服务",
              sub_button: [
                {
                  type: "miniprogram",
                  appid: "wx_78687786756758",
                  pagepath: "pages/query/appointment/index",
                  name: "本地商城",
                  sub_button: [],
                },
                {
                  type: "view",
                  name: "优惠券",
                  url: "coupon",
                },
                {
                  type: "click",
                  name: "商家入驻",
                  key_word_content: "关键词",
                  key: "click_key",
                },
                {
                  type: "view",
                  name: "快宝急送",
                  url: "single_order_place",
                  // url: "https://w.url.cn/s/AlxQwJW",
                  sub_button: [],
                },
              ],
            },
            {
              name: "关键词1",
              sub_button: [
                {
                  type: "miniprogram",
                  appid: "wx_78687786756758",
                  pagepath: "pages/index/index",
                  name: "测试",
                  sub_button: [],
                },
              ],
            },
          ],
        },
      },
    });
  },
  // 微信管理，设置，自定义菜单，获取官方提供的公众号菜单
  "POST /v1/PlatformManage/Menu/officialMenuList": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: [
        {
          name: "查快递",
          key: "waybill_query",
        },
        {
          name: "下单",
          key: "single_order_place",
        },
        {
          name: "批量下单",
          key: "batch_order_place",
        },
        {
          name: "我的订单",
          key: "order_list",
        },
        {
          name: "数据",
          key: "data_export",
        },
        {
          name: "管理",
          key: "manage",
        },
        {
          name: "绑定手机",
          key: "bind_mobile",
        },
        {
          name: "优惠券",
          key: "coupon",
        },
        {
          name: "下单",
          key: "merchant_entry",
        },
        {
          name: "驿站取件",
          key: "dak_record",
        },
        {
          name: "附近驿站",
          key: "near_dak",
        },
        {
          name: "同城急送",
          key: "urgent_delivery",
        },
        {
          name: "身份码",
          key: "user_mark",
        },
        {
          name: "投诉",
          key: "complaint",
        },
      ],
    });
  },
  // 微信管理，设置，自定义菜单，获取官方提供的小程序菜单
  "POST /v1/PlatformManage/Menu/miniProgramMenu": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {
        platform_info: {
          platform: "wt_mini_55",
          platformType: "weixin_mini",
          platformName: "通递驿站",
          appid: "wx_123123123",
        },
        menu_list: [
          {
            name: "查快递",
            page: "pages/query/index",
          },
          {
            name: "预约取件",
            page: "pages/query/appointment/index",
          },
          {
            name: "身份码",
            page: "pages/IDcode/index",
          },
          {
            name: "绑定手机",
            page: "pages/user/relation/edit/index",
          },
          {
            name: "寄快递",
            page: "pages/order/edit/index",
          },
          {
            name: "订单列表",
            page: "pages/order/index",
          },
          {
            name: "附近驿站",
            page: "pages-1/pages/order/shop/index",
          },
          {
            name: "优惠券",
            page: "pages/order/card/index",
          },
          {
            name: "投诉",
            page: "pages-0/pages/query/feedback/index",
          },
          {
            name: "会员积分",
            page: "pages-1/pages/order/integral/index",
          },
        ],
      },
    });
  },
  // 微信管理，设置，自定义菜单，当用户选择同城急送内置链接时，检查用户是否开通此功能
  "POST /v1/PlatformManage/Menu/menuAuth": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: 0,
    });
  },
  // 微信管理，设置，自定义菜单，发布菜单
  "POST /v1/PlatformManage/Menu/publishMenu": (req, res) => {
    res.send({
      code: 0,
      msg: "发布成功",
      data: {},
    });
  },
  // 微信管理，设置，收益广告展示位置设置
  "POST /v1/PlatformManage/EarningsAdvertising/setShowPosition": (req, res) => {
    res.send({
      code: 0,
      msg: "设置成功",
      data: {},
    });
  },
  // 微信管理，设置，收益广告展示位置查询
  "GET /v1/PlatformManage/EarningsAdvertising/showPositionGet": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {
        ad_single_place: 0,
        ad_order_list: 1,
        ad_order_detail: 1,
        ad_order_finish: 1,
        ad_pre_order_finish: 1,
        ad_express_index: 0,
        ad_express_detail: 0,
        ad_print_order: 1,
      },
    });
  },
  // 绑定驿站账号
  "POST /v1/PlatformManage/Config/setInnAccount": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },
  // 解绑下属驿站
  "GET /v1/PlatformManage/Config/cancelInnAccount": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },
  // 微信管理，设置，获取推广小程序配置
  "POST /v1/PlatformManage/Config/promoteMiniProgram": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {
        id: "1",
        shop_id: "7",
        qrcode_status: "1",
        template_status: "2",
        reply_status: "2",
        create_time: "2021-11-15 17:33:40",
        update_time: "2021-11-15 17:33:40",
      },
    });
  },
  // 微信管理，设置，编辑推广小程序配置
  "POST /v1/PlatformManage/Config/editPromoteMiniProgram": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },
  // 微信管理，获得新零售绑定的驿站信息
  "POST /v1/PlatformManage/Config/getInnAccount": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {
        inn_name: "测试123",
        inn_account: "***********",
        inn_id: "2225571",
      },
    });
  },
  // 微信管理，是否新零售加盟商
  "POST /v1/PlatformManage/Config/isCityLeague": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: false,
    });
  },
  // 微信管理，自定义菜单，获取定制微信菜单链接
  "POST /v1/PlatformManage/Menu/wechatMenuUrl": (req, res) => {
    const { menu_key } = req.body;
    res.send({
      code: 0,
      msg: "success",
      data: `${menu_key}_url`,
    });
  },
};
