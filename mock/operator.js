/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  // 导入快递员
  'POST /Api/Courier/importCourierInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '导入成功',
      data: {},
    });
  },
  // 批量删除
  'POST /Api/Courier/delCourier': (req, res) => {
    res.send({
      code: 0,
      msg: '删除成功',
      data: {},
    });
  },
  // 批量导入
  'POST /Api/Upload/uploadFile': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        errorFilePath: 'D:\\upload\\2020-07-21\\3845.xlsx',
        count: 200,
        errorCount: 8,
        successCount: 192,
        percentage: 100,
        errorMsg: '工号与网点编号不匹配',
      },
    });
  },
  // 业务员重新申请
  'POST /Api/Courier/reapplyCourier ': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Courier/transferCourier': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Courier/editStatus': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
};
