/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/**
 * 快宝同城，单号设置接口
 *  */
export default {
  //获取单号设置，配送员设置列表
  'POST /Api/CityWaybill/cityWayBillList': (req, res) => {
    const result = [];
    Array.from({
      length: 40,
    }).map((item, index) => {
      result.push({
        id: `${index}`,
        company_info_id: '13',
        company_site_id: '12',
        courier_kb_id: '0',
        kdy_id: '0',
        courier_no: '*********',
        courier_code: '001',
        courier_name: '23123',
        courier_phone: '13166287941',
        type: '0',
        switch: `${index % 2 ? 0 : 1}`,
        is_waybill_open: `${index % 2 ? 1 : 2}`,
        allowance_count: '3',
        created_at: '2020-08-18 16:28:35',
        updated_at: '2020-08-18 16:28:35',
        deleted_at: null,
        site_name: index % 2 ? null : `站点${index}`,
        is_delivery_pay: `${index % 2 ? 0 : 1}`,
      });
    });
    res.send({
      code: 0,
      msg: '成功',
      data: {
        list: result,
        count: 40,
        page: parseInt(req.body.page) || 1,
      },
    });
  },
  //权限开启关闭
  'POST /Api/CityWaybill/updateIsWayBillOpen': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
  //单号限额
  'POST /Api/CityWaybill/updateWayBillNumber': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {},
    });
  },
};
