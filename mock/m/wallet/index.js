/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  'POST /Api/Finance/rechargePayRecord': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: [
          {
            id: '249933',
            kb_type: 'city',
            type: 'order_cz',
            type_desc: '充值',
            order_number: 's_cd55c158cdb8e9636',
            trade_number: '2024042522001420041410960612',
            desc: '新零售加盟商测试dxx|| 充值成功',
            money: '0.01',
            fee_money: '0.00',
            trans_count: '0',
            business_count: '1',
            create_time: '2024-04-25 19:12:37',
            update_time: '2024-04-25 19:12:37',
            balance_avail: '100.98',
            balance_cash: '99.00',
            balance_frozen: '0.00',
            account: '',
            trans_account: '1',
            unit_price: '0.00',
            sub_id: '0',
            price: '0.00',
            trans_type: 'in',
            pay_method_desc: '微信',
          },
          {
            id: '249933',
            kb_type: 'city',
            type: 'order_cz',
            type_desc: '充值',
            order_number: 's_cd55c158cdb38e9636',
            trade_number: '2024042522001420041410960612',
            desc: '新零售加盟商测试dxx|| 充值成功',
            money: '0.01',
            fee_money: '0.00',
            trans_count: '0',
            business_count: '1',
            create_time: '2024-03-15 19:12:37',
            update_time: '2024-04-25 19:12:37',
            balance_avail: '100.98',
            balance_cash: '99.00',
            balance_frozen: '0.00',
            account: '',
            trans_account: '1',
            unit_price: '0.00',
            sub_id: '0',
            price: '0.00',
            trans_type: 'in',
            pay_method_desc: '微信',
          },
          {
            id: '249933',
            kb_type: 'city',
            type: 'order_cz',
            type_desc: '充值',
            order_number: 's_cd55c158cdb8e96236',
            trade_number: '2024042522001420041410960612',
            desc: '新零售加盟商测试dxx|| 充值成功',
            money: '0.01',
            fee_money: '0.00',
            trans_count: '0',
            business_count: '1',
            create_time: '2024-04-25 19:12:37',
            update_time: '2024-04-25 19:12:37',
            balance_avail: '100.98',
            balance_cash: '99.00',
            balance_frozen: '0.00',
            account: '',
            trans_account: '1',
            unit_price: '0.00',
            sub_id: '0',
            price: '0.00',
            trans_type: 'in',
            pay_method_desc: '微信',
          },
        ],
      }),
    );
  },
  'POST /Api/Finance/rechargePaySign': (req, res) => {
    const data =
      req.body.pay_method === 'wechat'
        ? {
            wechat: {
              appId: 'wx1a6252fee4475cc1',
              nonceStr: 'ALpja7zzIv4k6OHe',
              timeStamp: '**********',
              package: 'prepay_id=wx1111192134178116c030ba38a815ad0001',
              signType: 'MD5',
              sign: '6684F8E5E8D38BA27A473079711058B9',
            },
          }
        : {
            alipayQrCode: {
              out_trade_no: 's_c1739243386850557096',
              qrcode_url: 'https://qr.alipay.com/bax018134w92q64vlbb555d8',
            },
            alih5: {
              out_trade_no: 's_c1739243386850557096',
              mweb_url: 'https://www.kbydy.cn/ttt',
              // mweb_url:
              //   'https://openapi.alipay.com/gateway.do?&app_id=2021001187640769&biz_content=%7B%22out_trade_no%22%3A%22s_c1739243386850557096%22%2C%22product_code%22%3A%22QUICK_WAP_WAY%22%2C%22subject%22%3A%22%E6%99%BA%E6%85%A7%E5%B9%B3%E5%8F%B0%E5%85%85%E5%80%BC%E5%85%85%E5%80%BC%22%2C%22total_amount%22%3A0.01%2C%22quit_url%22%3A%22m.kuaidihelp.com%22%2C%22body%22%3A%22%E6%99%BA%E6%85%A7%E5%B9%B3%E5%8F%B0%E5%85%85%E5%80%BC%22%7D&charset=UTF-8&method=alipay.trade.wap.pay&notify_url=alih5YzWechatPublicCityOrderCzNotify&return_url=m.kuaidihelp.com&sign_type=RSA2&time_expire=2025-02-11+11%3A14%3A47&timestamp=2025-02-11+11%3A09%3A47&version=1.0&sign=p%2B8C2IkMO2dSRC3xpgIITdlwt%2BRbQm49K7rCLkhWLDc44Ts2riIrtilhTX3Hm1ey4zCUXUTBMkfCZbhyS4uusTq6tqM2EVjhuCWPPkpOb371CM7GlRwVUp%2FYHKsWpSnAaojeT1msSEt%2FyMbzhPwAlbOFGi0vAEyDDuiKc8eYgPTZbpg59acVylv0hA77sfgvMzbhUzPN6iOkuO27PtKYP8XSpm22TBBpyqMRFh98scuZ50%2FQ0haXmEFTe4RnHgdXds%2F1q%2FMHwKiQm4h65vAKWYVa75o9bqsIde8JiMYQWRTcLzjdvGpI1PfBBLCytBZ34YME5di8EZiZznF8X2OvsA%3D%3D',
            },
          };
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data,
      }),
    );
  },
  'POST /Api/Finance/accountBalance': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          avail_money: '9243.46',
          red_packet: 0,
          withdrawable_money: 1209,
          frozen_money: '0.00',
          baidu_userid: '',
          baidu_account: '',
          alipay_name: '',
          alipay_userid: '',
          alipay_account: '',
          wxpay_name: '',
          wxpay_openid: '',
          score: '0',
          total_money: 10452.46,
          can_cash_money: 1209,
          can_sms_count: 231087,
          can_ivr_count: 231086,
          cash_money_info: {
            withdrawable_money: '209.00',
            withdrawable_money2: '1000.00',
          },
          sms_count_info: {
            fee_count: 231086,
            free_count: '1',
            give_count: 0,
          },
          ivr_count_info: {
            fee_count: 231086,
            free_count: '0',
          },
        },
      }),
    );
  },
};
