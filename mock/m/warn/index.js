/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  'POST /Api/WarnData/getList': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          list: [
            {
              id: '1',
              phone: '***********',
              'brand|1': ['yt', 'jt', 'yd'],
              type: 'sorting_scanner',
              account: '1212',
              key: '6684F8E5E8D38BA27A473079711058B9',
              create_at: '2025-02-13 10:00:00',
              data: {
                phone: '',
                courier_name: '王萌',
                courier_phone: '***********',
              },
            },
            {
              id: 8,
              brand: 'yd',
              type: 'yd_manager',
              account: '1',
              status: 0,
              key: 'cbf5e44232f305a8073a67c35a927d9a',
              data: {
                id: 1,
                phone: '***********',
                networkName: '牛爷爷网点',
                empCode: '123',
              },
              create_at: '2025-02-26 13:07:08',
            },
            {
              id: '3',
              phone: '***********',
              brand: 'yt',
              type: 'yt_zz_pc',
              account: '1212',
              key: '6684F8E5E8D38BA27A473079711058B9',
              create_at: '2025-02-13 10:00:00',
              data: {
                phone: '',
                courier_name: '王萌',
                courier_phone: '***********',
              },
            },
            {
              id: '4',
              phone: '***********',
              brand: 'jt',
              type: 'jt_device_id',
              account: '1212',
              key: '6684F8E5E8D38BA27A473079711058B9',
              create_at: '2025-02-13 10:00:00',
              data: {
                phone: '',
                courier_name: '王萌',
                courier_phone: '***********',
              },
            },
            {
              id: '5',
              phone: '***********',
              brand: 'sto',
              type: 'sto_xz',
              account: '1212',
              key: '6684F8E5E8D38BA27A473079711058B9',
              create_at: '2025-02-13 10:00:00',
              data: {
                id: 1,
                branch_code: '123',
                phone: '***********',
              },
            },
            {
              id: '6',
              phone: '***********',
              brand: 'zt',
              type: 'zt_pda',
              account: '1212',
              key: '6684F8E5E8D38BA27A473079711058B9',
              create_at: '2025-02-13 10:00:00',
              data: {
                id: 1,
                account: '123',
                phone: '***********',
                device_imei: '123213',
                password: '123',
                device_brand: 'zt',
                branch_code: '123',
              },
            },
            {
              id: 6,
              brand: 'yd',
              type: 'yd_ybx_reg_device_id',
              account: '6508',
              status: 0,
              key: 'd541c90daa527b021122da5b048d9f1a',
              data: {
                phone: '***********',
                courier_name: '王萌',
                courier_phone: '***********',
              },
              create_at: '2025-02-26 13:06:12',
            },
            {
              id: '8',
              phone: '***********',
              brand: 'jt',
              type: 'jt_wc_sms',
              account: '1212',
              key: '6684F8E5E8D38BA27A473079711058B9',
              create_at: '2025-02-13 10:00:00',
              data: {
                phone: '',
                courier_name: '王萌王萌王萌王萌王萌王萌王萌王萌王萌王萌王萌王萌',
                courier_phone: '***********',
              },
            },
            {
              id: '9',
              brand: 'zt',
              type: 'sorting_jggd',
              account: '1',
              status: 1,
              key: 'edafafsdf',
              create_at: '2025-04-25 16:23:33',
              data: {
                id: 1,
                code: '18721008363_10',
                name: '快报测试1',
              },
            },
          ],
        },
      }),
    );
  },
  'POST /Api/WarnData/getShareInfo': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          id: '1',
          phone: '***********',
          brand: 'yd',
          type: 'yd_manager',
          account: '1212',
          key: '6684F8E5E8D38BA27A473079711058B9',
          create_at: '2025-02-13 10:00:00',
          shop_id: 123,
          status: 0,
          data: {
            phone: '***********',
            courier_name: '王萌',
            courier_phone: '***********',
          },
        },
      }),
    );
  },
  'POST /Api/WarnData/saveJtDeviceId': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ztPdaLogin': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ydManagerSendSmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ydManagerVerifySmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/stoXzSendSmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/stoXzVerifySmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ytXzSendSmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ytXzVerifySmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/jtWcSendSmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/jtWcVerifySmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/removeShareInfo': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ydYbxDeviceIdRegCaptcha': (req, res) => {
    res.send(
      mock({
        code: 10008,
        msg: '请绑定韵镖侠账号后再操作！',
        // data: {},
        data: {
          pic_code_base64:
            'iVBORw0KGgoAAAANSUhEUgAAAGQAAAAeCAIAAABVOSykAAAAmUlEQVR42u3WvQmAQAyG4eucxAHsLV3BDaysLBzK3iWcxAVuAQtBDoJeFH9O8sJHyBXXPIQQ18wTUcZBABZYYIEFFlgQgAUWWGDdnK7tZfTf68yHsTJZp4xCrIPnU1hVPkb7vRTlEO3Bsoq1uciqyeoi6wtY3+ysn2IxWcljXdhWms0FVkJYFu8sLniwwAILBbDAAgsssIxnAY4U+Vu8J0IwAAAAAElFTkSuQmCC',
        },
      }),
    );
  },
  'POST /Api/WarnData/ydYbxDeviceIdRegSendSmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ydYbxDeviceIdRegVerifySmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ytZzPcSendSmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
  'POST /Api/WarnData/ytZzPcVerifySmsCode': (req, res) => {
    res.send(mock({ code: 0, msg: '成功' }));
  },
};
