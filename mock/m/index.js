/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  'POST /Api/Wechat/login': (req, res) => {
    res.send(
      mock({
        // 'code|1': ['9999001', '9999002', '9999003', '9999004'],
        code: 0,
        msg: '请求成功',
        data: {
          id: '@id',
          brand_name: '123',
          kb_id: '333224658',
          openid: 'qwerdf',
          phone: '***********',
          pid: '0',
          shop_id: '4515',
          privilege: '*',
          user_type: '1',
        },
      }),
    );
  },
  'POST /Api/Wechat/getScheme': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '请求成功',
        data: {
          link: 'weixin://dl/business/?t=7BZ3O6V9U6a',
        },
      }),
    );
  },
};
