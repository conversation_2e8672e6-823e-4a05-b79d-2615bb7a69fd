/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

/**
 *  格口统计，获取列表
 *  */
const codeStatistic = (req, res) => {
  const list = [];
  Array.from({ length: 20 }).forEach((_, index) => {
    list.push({
      sorting_line_code: index,
      brand: 'zt',
      grid_code: 'zt001',
      fp_num: '146',
      sj_num: '0',
      error_num: 146,
      zql: '0％',
    });
  });
  res.send({
    code: 0,
    msg: 'success',
    data: {
      list,
      page: 1,
      pageSize: 20,
      count: 20,
    },
  });
};

/**
 * 进港分拣，获取列表
 *  */
const listArrival = (req, res) => {
  const list = [];
  Array.from({ length: 20 }).forEach((_, index) => {
    list.push({
      id: index,
      shop_id: '7',
      sorting_line_id: '1',
      sorting_line_code: `${index}分拣线码`,
      waybill: '73223003900028',
      brand: '',
      img: 'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
      seq: 'seq2021-07-23 02:31:02',
      one_code: '1',
      two_code: '2',
      three_code: '3',
      weight: '20kg',
      grid_code: 'grid_code',
      actual_grid_code: 'actual_grid_code',
      is_accuracy: '0',
      gbt_no: 'gbt-001',
      gbt_scan_time: '2021-07-23 10:31:02',
      dj_time: '2021-07-23 10:31:02',
      dj_status: '0',
      send_time: '2021-07-23 10:31:02',
      send_status: '0',
      pj_time: '2021-07-23 10:31:02',
      pj_status: '0',
      next_dj_time: '2021-07-23 10:31:02',
      next_dj_status: '0',
      next_pj_time: '2021-07-23 10:31:02',
      next_pj_status: '0',
      luoge_time: '2021-07-23 10:31:02',
      addr_keys:
        '北京、上海、深圳、北京、上海、深圳、北京、上海、深圳、北京、上海、深圳、北京、上海、深圳、北京、上海、深圳',
      gbt_status: '识别状态',
      match_data: {
        // 分拣模式：0=标准，1=三段码，2=地址分拣，12=先三段码 再地址，21=先地址 再三段码
        sort_type: '12',
        // 匹配类型：address=地址，3code=三段码
        match_form: 'address',
        // 地址来源：ocr=OCR，third=接口
        address_form: 'third',
        // 三段码来源：ocr=OCR，third=接口
        three_code_form: 'third',
        // 提醒件类型
        remind_type: '',
        // 提醒件查询错误
        remind_type_query_error: '成功',
        // 接口三段码
        api_3code: '471-',
        // 接口三段码查询错误原因
        api_3code_query_error: '',
        // 接口三段码匹配错误原因
        api_3code_match_error: '未配置该三段码格口',
        // 接口地址
        api_address: '建涛广场罢了647',
        // 接口地址查询错误原因
        api_address_query_error: '22',
        // 接口地址码匹配错误原因
        api_address_match_error: '',
        // OCR 三段码
        ocr_3code: '',
        // OCR 三段码查询错误原因
        ocr_3code_query_error: '33',
        // OCR 三段码匹配错误原因
        ocr_3code_match_error: '',
        // OCR 地址
        ocr_address:
          '女徽省合肥市瑶海区773319450192568建涛广场罢了647**152****6772上海市上海市长宁区新泾镇星巴克(建沿广场店)773319450192568物品:日用品备注:微信扫一扫揽件码:0822909潘用伟发15201946772收18530215877已验视本包裹由快宝云打印提供智慧技术支持安徽省合肥市瑶海区773319450192568建涛广场罢了647潘**152****6772上海市上海市长宁区新泾镇巴克（建酒广场店）773319450192568物品：日用品备注：揽件码：0822909潘用伟发15201946772 收18530215877已验视本包裹由快宝云打印提供智慧技术支持',
        // OCR 地址查询错误原因
        ocr_address_query_error: '',
        // OCR 地址匹配错误原因
        ocr_address_match_error: '地址方案配置的格口未找到',
        // 图片地址
        file_path: 'https://cdn-img.kuaidihelp.com/qj/miniapp/activity/recruit/step.png',
      },
    });
  });
  res.send({
    code: 0,
    msg: 'success',
    data: {
      list,
      page: 1,
      pageSize: 20,
      totalCount: 20,
    },
  });
};

/**
 * 格口配置，获取分拣线下拉数据
 *  */
const getLineList = (req, res) => {
  const list = [];
  Array.from({ length: 40 }).forEach((_, index) => {
    list.push({
      id: index,
      shop_id: index,
      sorting_line_code: index,
      sorting_line_name: `分拣线名称${index}`,
      sn_code: 'sn码',
      sorting_line_type: '1',
      sorting_line_token: '123131312',
      brand: 'sf',
      status: '1',
      up_scanner: '17521161691',
      down_scanner: '13262627065',
      create_time: '2021-07-02 15:26:01',
      update_time: '2021-07-02 15:26:01',
    });
  });
  res.send({
    code: 0,
    msg: 'success',
    data: list,
  });
};

export default {
  // 分拣线配置，获取支持的品牌
  'POST /Api/Automation/SortLine/listBrand': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          brand: 'all',
          name: '全部品牌',
        },
        {
          brand: 'sto',
          name: '申通',
        },
        {
          brand: 'zt',
          name: '中通',
        },
        {
          brand: 'ht',
          name: '百世',
        },
        {
          brand: 'yd',
          name: '韵达',
        },
        {
          brand: 'yt',
          name: '圆通',
        },
        {
          brand: 'jt',
          name: '极兔',
        },
        {
          brand: 'ems',
          name: '邮政',
        },
        {
          brand: 'zykd',
          name: '众邮',
        },
        {
          brand: 'fw',
          name: '丰网',
        },
      ],
    });
  },
  // 分拣线配置，获取列表
  'POST /Api/Automation/SortLine/list': (req, res) => {
    const list = [];
    Array.from({ length: 20 }).forEach((_, index) => {
      list.push({
        id: index,
        shop_id: '7',
        sorting_line_code: `13899992222_${index}2`,
        sorting_line_name: `${index}_分拣线名称`,
        sn_code: 'sn码',
        sorting_line_type: index % 2 ? '1' : '2',
        zt_token: 'zt',
        ht_token: 'ht',
        // sorting_line_token: "",
        sorting_line_token:
          '{"zt":{"key":"51405-001", "codeRelation":{"relationId":1, "account":"123"}},"ht":{"key":"test001"},"sto":{"isDao":"0", "codeRelation":{"relationId":1, "phone":"***********"}},"yd":{"get3CodeType":"third_ocr","ydManager":{"phone":***********}},"jt":{"type":"0"}}', // "ydManager":{"phone":***********,"empCode":"625500"}
        brand: index % 2 ? 'sto,yt,jt,post,ht,yd,zt' : 'zt,yt',
        up_scanner: `${index}默认上线扫描员`,
        down_scanner: `${index}默认下线扫描员`,
        create_time: '2021-07-02 15:23:15',
        update_time: '2021-07-02 15:23:15',
        start_plan: '["15:00", "", "16:02"]',
        scanner_user_object_list: [
          {
            user_type: 1,
            user_object_list: ['***********', '***********'],
          },
        ],
        scanner_user_object_count: 2,
      });
    });
    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 分拣线配置，删除列表
  'POST /Api/Automation/SortLine/delete': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 分拣线配置，新增分拣线
  'POST /Api/Automation/SortLine/save': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 分拣线配置，授权码列表
  'POST /Api/Automation/SortLineToken/list': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '1',
          sorting_line_id: '1',
          type: '授权对象',
          gbt_no: '123',
          token: '045940b3842f2563d78ca0cab982c9ac174a904e',
          status: '1',
          is_deleted: '0',
          create_time: '2021-07-22 10:01:24',
          update_time: '2021-07-22 10:01:24',
          auth_time: '2021-07-22 10:01:24',
        },
      ],
    });
  },
  // 分拣线配置，取消授权
  'POST /Api/Automation/SortLineToken/del': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 分拣线配置，获取授权码
  'POST /Api/Automation/SortLineToken/addToken': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          code: '1234567890',
        },
      ],
    });
  },
  // 分拣线配置，分拣线编号检测
  'POST /Api/Automation/SortLine/checkCode': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: 0,
    });
  },
  // 分拣线配置，获取上下线扫描员
  'POST /Api/Automation/SortLine/salesmanList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '134',
          company_info_id: '13',
          company_site_id: '0',
          courier_kb_id: '0',
          kdy_id: '0',
          courier_no: '*********',
          courier_code: '002',
          courier_name: '苹果2',
          courier_phone: '18550031231',
          type: '0',
          switch: '1',
          is_waybill_open: '1',
          allowance_count: '7',
          created_at: '2020-08-17 09:47:06',
          updated_at: '2020-08-17 09:47:06',
          deleted_at: null,
          status: '1',
          user_type: '1',
        },
        {
          id: '1343',
          company_info_id: '13',
          company_site_id: '0',
          courier_kb_id: '0',
          kdy_id: '0',
          courier_no: '*********',
          courier_code: '003',
          courier_name: '苹果1',
          courier_phone: '18550031232',
          type: '0',
          switch: '1',
          is_waybill_open: '1',
          allowance_count: '7',
          created_at: '2020-08-17 09:47:06',
          updated_at: '2020-08-17 09:47:06',
          deleted_at: null,
          status: '1',
          user_type: '1',
        },
      ],
    });
  },
  // 分拣线配置，设置分拣模式
  'POST /Api/Automation/SortLine/saveModel': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },

  // 格口配置，获取列表
  'POST /Api/Automation/GeKou/list': (req, res) => {
    const list = [];
    Array.from({ length: 40 }).forEach((_, index) => {
      list.push({
        id: index,
        shop_id: index,
        sorting_line_id: `${index}1`,
        sorting_line_code: `${index}9`,
        grid_type: index % 2 ? 1 : 2,
        addr_keys:
          index % 2
            ? '北京、上海、深圳、北京、上海、深圳、北京、上海、深圳、北京、上海、深圳、北京、上海、深圳、北京、上海、深圳'
            : '',
        gbt_status: index % 2 ? '识别状态' : '',
        brand: 'yd',
        branch_code: 'branch_code11',
        grid_code: index,
        grid_name: `${index}__dasdasd`,
        error_grid_type: 0,
        down_scanner_type: '2',
        down_scanner_object: '15179185977',
        down_scanner_object_name: null,
        lower_node_dispatch: '15179185977',
        lower_node_dispatch_name: '哈哈哈哈',
        one_code: '3123',
        two_code: 'dasd',
        three_code: 'dasd',
        create_time: '2021-07-02 18:23:19',
        update_time: '2021-07-02 18:23:19',
      });
    });
    res.send({
      code: 0,
      msg: '成功',
      data: {
        eq: 0,
        list: [
          {
            id: '91349',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '1',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST106',
            grid_name: '到发到派+代收',
            down_scanner_type: '11',
            down_scanner_object: '15201940002',
            lower_node_dispatch: '17621829954',
            one_code: '470',
            two_code: 'A',
            three_code: '10',
            four_code: '',
            create_time: '2025-05-19 14:51:16',
            update_time: '2025-05-19 14:51:16',
            error_grid_type: '0',
            addr_keys: '',
            line_code: '',
            down_inn_id: '3473051',
            down_scanner_object_name: '张天',
            lower_node_dispatch_name: '17621829954',
            down_inn_name: '淞虹测试驿站',
            down_inn_phone: '15201946772',
            sorting_line_code: '18721008363_08',
          },
          {
            id: '91348',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '1',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST105',
            grid_name: '到派+分拣不代收',
            down_scanner_type: '10',
            down_scanner_object: '15023657418',
            lower_node_dispatch: '',
            one_code: '680',
            two_code: 'D',
            three_code: '02',
            four_code: '',
            create_time: '2025-05-19 14:25:57',
            update_time: '2025-05-19 14:25:57',
            error_grid_type: '0',
            addr_keys: '',
            line_code: '',
            down_inn_id: '3473051',
            down_scanner_object_name: '哦豁',
            lower_node_dispatch_name: '',
            down_inn_name: '淞虹测试驿站',
            down_inn_phone: '15201946772',
            sorting_line_code: '18721008363_08',
          },
          {
            id: '91347',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '1',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST104',
            grid_name: '到派+代收',
            down_scanner_type: '9',
            down_scanner_object: '15023657418',
            lower_node_dispatch: '',
            one_code: '900',
            two_code: 'D',
            three_code: '00',
            four_code: '',
            create_time: '2025-05-19 14:16:45',
            update_time: '2025-05-19 14:16:45',
            error_grid_type: '0',
            addr_keys: '',
            line_code: '',
            down_inn_id: '3473051',
            down_scanner_object_name: '哦豁',
            lower_node_dispatch_name: '',
            down_inn_name: '淞虹测试驿站',
            down_inn_phone: '15201946772',
            sorting_line_code: '18721008363_08',
          },
          {
            id: '91346',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '1',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST103',
            grid_name: '入库不代收',
            down_scanner_type: '7',
            down_scanner_object: '',
            lower_node_dispatch: '',
            one_code: '477',
            two_code: 'H',
            three_code: '01',
            four_code: '',
            create_time: '2025-05-19 14:08:46',
            update_time: '2025-05-19 14:08:46',
            error_grid_type: '0',
            addr_keys: '',
            line_code: '',
            down_inn_id: '3473051',
            down_scanner_object_name: '',
            lower_node_dispatch_name: '',
            down_inn_name: '淞虹测试驿站',
            down_inn_phone: '15201946772',
            sorting_line_code: '18721008363_08',
          },
          {
            id: '91213',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '2',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST100',
            grid_name: '综合异常口',
            down_scanner_type: '0',
            down_scanner_object: '',
            lower_node_dispatch: '',
            one_code: '',
            two_code: '',
            three_code: '',
            four_code: '',
            create_time: '2024-08-21 13:36:16',
            update_time: '2024-08-21 13:36:16',
            error_grid_type: '0',
            addr_keys: null,
            line_code: '',
            down_inn_id: '0',
            down_scanner_object_name: '',
            lower_node_dispatch_name: '',
            down_inn_name: '',
            down_inn_phone: '',
            sorting_line_code: '18721008363_08',
          },
          {
            id: '91335',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '1',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST002',
            grid_name: '申通入库',
            down_scanner_type: '8',
            down_scanner_object: '',
            lower_node_dispatch: '',
            one_code: '710',
            two_code: 'B12',
            three_code: '107',
            four_code: '',
            create_time: '2025-05-19 13:53:00',
            update_time: '2025-05-19 13:53:00',
            error_grid_type: '0',
            addr_keys: '',
            line_code: '',
            down_inn_id: '3473051',
            down_scanner_object_name: '',
            lower_node_dispatch_name: '',
            down_inn_name: '淞虹测试驿站',
            down_inn_phone: '15201946772',
            sorting_line_code: '18721008363_08',
          },
          {
            id: '91211',
            shop_id: '7',
            sorting_line_id: '13',
            grid_type: '1',
            brand: 'sto',
            branch_code: '900006',
            grid_code: 'ST001',
            grid_name: '申通正常口',
            down_scanner_type: '2',
            down_scanner_object: '15023657418',
            lower_node_dispatch: '',
            one_code: '710',
            two_code: '111',
            three_code: '11',
            four_code: '',
            create_time: '2025-05-19 14:06:06',
            update_time: '2025-05-19 14:06:06',
            error_grid_type: '0',
            addr_keys: '',
            line_code: '',
            down_inn_id: '0',
            down_scanner_object_name: '哦豁',
            lower_node_dispatch_name: '',
            down_inn_name: '',
            down_inn_phone: '',
            sorting_line_code: '18721008363_08',
          },
        ],
        page: '1',
        count: 7,
        pageSize: '20',
      },
    });
  },
  // 格口配置，导入配置
  'POST /Api/Automation/GeKou/upload': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 格口配置，获取分拣线下拉数据
  'POST /Api/Automation/GeKou/getLineList': getLineList,
  // 格口配置，获取分拣线下拉数据（本地服务器）
  'POST /admin/SortLine/getLineList  ': getLineList,
  // 格口配置，删除配置
  'POST /Api/Automation/GeKou/del': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 格口配置，新增异常口
  'POST /Api/Automation/GeKou/addType2': (req, res) => {
    res.send({
      code: 0,
      msg: 'XX品牌异常口已存在，请勿重复添加',
      data: {},
    });
  },
  // 格口配置，新增正常格口
  'POST /Api/Automation/GeKou/save': (req, res) => {
    res.send({
      code: 1,
      msg: 'XX品牌异常口已存在，请勿重复添加',
      data: {},
    });
  },
  // 格口配置，编辑异常、正常格口
  'POST /Api/YZ/CourierStation/editGrid': (req, res) => {
    res.send({
      code: 1,
      msg: 'XX品牌异常口已存在，请勿重复添加',
      data: {},
    });
  },
  // 格口配置，新增异常、正常格口，网点编号查询
  'POST /Api/Automation/GeKou/getBranchCode': (req, res) => {
    const { brand } = req.body;
    let data = `${brand}0123456789`;
    if (brand === 'sto' || !brand) {
      data = '';
    }

    res.send({
      code: 0,
      msg: 'success',
      data,
    });
  },
  // 格口配置，新增正常格口，获取下节点操作对象
  'POST /Api/Automation/GeKou/getDownScannerObject': (req, res) => {
    const { down_scanner_type: downScannerType } = req.body;
    const list =
      downScannerType === '3'
        ? [
            {
              branch_code: '410093',
              brand: 'ht',
              create_time: '2021-03-01 16:19:17',
              id: '134',
              kb_id: '7',
              station_code: '999957',
              station_name: '长沙分拨中心',
              station_type: '1',
              update_time: '2021-03-01 16:19:17',
            },
          ]
        : [
            {
              id: '11',
              company_info_id: '13',
              company_site_id: '10',
              courier_kb_id: '51669264',
              kdy_id: '1957428',
              courier_no: '*********',
              courier_code: '035',
              courier_name: '骗你的哈',
              courier_phone: '15179185977',
              type: '0',
              switch: '1',
              is_waybill_open: '1',
              allowance_count: '10',
              created_at: '2020-07-30 10:08:49',
              updated_at: '2020-07-30 10:08:49',
              deleted_at: null,
              status: '1',
              user_type: '1',
            },
            {
              id: '2358',
              company_info_id: '13',
              company_site_id: '0',
              courier_kb_id: '67902670',
              kdy_id: '2225404',
              courier_no: '0002004',
              courier_code: '004',
              courier_name: '花花',
              courier_phone: '18600000001',
              type: '0',
              switch: '1',
              is_waybill_open: '2',
              allowance_count: '-1',
              created_at: '2021-04-14 15:00:33',
              updated_at: '2021-04-14 15:00:33',
              deleted_at: null,
              status: '1',
              user_type: '1',
            },
          ];

    res.send({
      code: 0,
      msg: 'success',
      data: list,
    });
  },
  // 进港分拣，获取列表
  'POST /Api/Automation/SortLine/listArrival': listArrival,
  // 进港分拣，获取列表(本地服务器)
  'POST /admin/SortLine/listArrival ': listArrival,
  // 格口统计，获取列表
  'POST /Api/Automation/GeKou/codeStatistic': codeStatistic,
  // 格口统计，获取列表(本地服务器)
  'POST /admin/SortLine/codeStatistic': codeStatistic,
  // 格口配置，下发配置
  'POST /Api/Automation/GeKou/sysData': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/Automation/GeKou/checkSysData': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 地址分拣，地址分拣列表
  'POST /Api/Automation/addrScheme/list': (req, res) => {
    const { page, size } = req.body;
    res.send(
      mock({
        msg: '成功',
        code: 0,
        data: {
          [`list|${size}`]: [
            {
              addr_scheme: '上海市+北翟路+(100-200奇数)、北京市+长安路+(100-200偶数)',
              'codes|1': ['1,2,4', '7,8,9'],
              create_time: '@date',
              'id|+1': 1,
              is_deleted: '0',
              shop_id: '7',
              sort: '001',
              sorting_line_code: '18721008363_09',
              'sorting_line_id|+1': 1,
              update_time: '@date',
            },
          ],
          total: 200,
          page,
        },
      }),
    );
  },
  // 地址分拣，删除配置
  'POST /Api/Automation/addrScheme/del': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 地址分拣，新增配置
  'POST /Api/Automation/addrScheme/save': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 地址分拣，获取已使用的优先级
  'POST /Api/Automation/addrScheme/sortList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [1, 2, 4],
    });
  },
  // 判断是否是老用户
  'POST /Api/Automation/SortLine/config': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        isOldUser: 1,
      },
    });
  },
  'POST /Api/Automation/GeKou/brandAbnormalType': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        sto: [
          {
            name: '综合异常口',
            type: 0,
          },
          {
            name: '错分口',
            type: 5,
          },
          {
            name: '拦截',
            type: 6,
          },
          {
            name: '代收',
            type: 8,
          },
          {
            name: '到付',
            type: 9,
          },
          {
            name: '电联',
            type: 10,
          },
          {
            name: '送货上门',
            type: 3,
          },
          {
            name: '申咚咚',
            type: 12,
          },
          {
            name: '客户声音',
            type: 14,
          },
        ],
        zt: [
          {
            name: '综合异常口',
            type: 0,
          },
          {
            name: '错分口',
            type: 5,
          },
          {
            name: '代收',
            type: 8,
          },
          {
            name: '到付',
            type: 9,
          },
          {
            name: '拦截',
            type: 6,
          },
          {
            name: '尊享',
            type: 11,
          },
          {
            name: '标快',
            type: 4,
          },
          {
            name: '好快',
            type: 15,
          },
        ],
        yt: [
          {
            name: '综合异常口',
            type: 0,
          },
          {
            name: '错分口',
            type: 5,
          },
          {
            name: '代收',
            type: 8,
          },
          {
            name: '到付',
            type: 9,
          },
          {
            name: '通缉',
            type: 7,
          },
          {
            name: '电联',
            type: 10,
          },
          {
            name: '尊享',
            type: 11,
          },
          {
            name: '圆准达',
            type: 16,
          },
        ],
        yd: [
          {
            name: '综合异常口',
            type: 0,
          },
          {
            name: '错分口',
            type: 5,
          },
          {
            name: '代收',
            type: 8,
          },
          {
            name: '到付',
            type: 9,
          },
          {
            name: '拦截',
            type: 6,
          },
          {
            name: '电联',
            type: 10,
          },
          {
            name: '派送',
            type: 17,
          },
          {
            name: '签单返回',
            type: 18,
          },
          {
            name: '退回',
            type: 19,
          },
        ],
        jt: [
          {
            name: '综合异常口',
            type: 0,
          },
          {
            name: '错分口',
            type: 5,
          },
          {
            name: '到付',
            type: 9,
          },
          {
            name: '拦截',
            type: 6,
          },
          {
            name: '电联',
            type: 10,
          },
        ],
        ems: [
          {
            name: '综合异常口',
            type: 0,
          },
          {
            name: '错分口',
            type: 5,
          },
          {
            name: '到付',
            type: 9,
          },
          {
            name: '撤单',
            type: 20,
          },
        ],
        all: [
          {
            name: '到付',
            type: 9,
          },
          {
            name: '撤单',
            type: 20,
          },
        ],
      },
    });
  },
  'POST /Api/Automation/SortLine/getBrandScannerList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        list: [
          {
            brand: 'yt',
            phone: '***********',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '1234',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '123',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '18',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '17',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '15',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '14',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '13',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '12',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '11',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '9',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '8',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '7',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '6',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '5',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '4',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '3',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '2',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '1',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********21',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********1',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********2',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********3',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********411',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********422',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********433',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********444',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '13838389437',
            is_use: 1,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '23838389437',
            is_use: 1,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '33838389437',
            is_use: 1,
            is_default: 1,
            name: '张三',
          },
          {
            brand: 'yt',
            phone: '***********',
            name: '李四',
          },
          {
            brand: 'yd',
            phone: '***********',
            is_use: 0,
            name: '张三',
          },
          {
            brand: 'yd',
            phone: '13838389439',
            is_use: 1,
            is_default: 1,
            name: '张三',
          },
          {
            brand: 'jt',
            phone: '***********',
            is_use: 0,
            name: '张三',
          },
        ],
      },
    });
  },
  'POST /Api/Automation/SortLine/ydManagerCheckSms': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        list: [
          {
            networkName: '四川芦山县公司',
            empCode: '625500007',
            realName: '阿牛伍呷',
            phone: '18583888862',
            cardNo: '513432199506281417',
            gender: '1',
            cpCode: '625500',
            cpType: 4,
            age: '29',
            nickName: '',
            workingState: null,
            lb: '2',
            shortName: '芦山',
          },
        ],
      },
    });
  },
  'POST /Api/Automation/ScannerSmsLogin/getInfo': (req, res) => {
    res.send({
      code: 0,
      msg: '',
      data: {
        login: 1,
        brand: 'yt',
        phone: '***********',
      },
    });
  },
  'POST /Api/Automation/ScannerSmsLogin/sendSms': (req, res) => {
    res.send({
      code: 0,
      msg: '',
      data: {},
    });
  },
  'POST /Api/Automation/ScannerSmsLogin/smsLogin': (req, res) => {
    res.send({
      code: 0,
      msg: '',
      data: {},
    });
  },
  'POST /Api/Automation/SortLine/getYdManagerInfo': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        id: 1,
        phone: '18583888862',
        data: {
          networkName: '四川芦山县公司',
          empCode: '625500007',
          realName: '阿牛伍呷',
          phone: '18583888862',
          cardNo: '513432199506281417',
          gender: '1',
          cpCode: '625500',
          cpType: 4,
          age: '29',
          nickName: '',
          workingState: null,
          lb: '2',
          shortName: '芦山',
        },
        create_at: '2024-11-25 13:22:23',
      },
    });
  },
  'POST /Api/Automation/SortLine/getYdManagerInfoList': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: 1,
          phone: '18583888862',
          list: [
            {
              networkName: '四川芦山县公司',
              empCode: '625500007',
              realName: '阿牛伍呷',
              phone: '18583888862',
              cardNo: '513432199506281417',
              gender: '1',
              cpCode: '625500',
              cpType: 4,
              age: '29',
              nickName: '',
              workingState: null,
              lb: '2',
              shortName: '芦山',
            },
            {
              networkName: '四川芦山县公司',
              empCode: '625500008',
              realName: '阿牛伍呷',
              phone: '18583888862',
              cardNo: '513432199506281417',
              gender: '1',
              cpCode: '625500',
              cpType: 4,
              age: '29',
              nickName: '',
              workingState: null,
              lb: '2',
              shortName: '芦山',
            },
          ],
          create_at: '2024-11-25 13:22:23',
        },
        {
          id: 2,
          phone: '18583888863',
          list: [
            {
              networkName: '四川芦山县公司',
              empCode: '625500009',
              realName: '阿牛伍呷',
              phone: '18583888863',
              cardNo: '513432199506281417',
              gender: '1',
              cpCode: '625500',
              cpType: 4,
              age: '29',
              nickName: '',
              workingState: null,
              lb: '2',
              shortName: '芦山',
            },
          ],
          create_at: '2024-11-25 13:22:23',
        },
      ],
    });
  },
  'POST /Api/Automation/SortLine/ydManagerSendSms': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [],
    });
  },
  'POST /Api/Automation/SortLine/unbindYdManager': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [],
    });
  },
  'POST /Api/Automation/SortLine/bindYdManager': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [],
    });
  },

  'POST /Api/Automation/SortingStoXzScanner/unbind': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingStoXzScanner/sendSms': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingStoXzScanner/checkSms': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingStoXzScanner/bind': (req, res) => {
    res.send(
      mock({
        // 'code|1': [0, 1001],
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingStoXzScanner/getInfoList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          id: 3,
          phone: '********',
          branch_code: 123,
          create_at: '2024-12-17 14:51:53',
        },
        {
          id: 4,
          phone: '********',
          branch_code: 123,
          create_at: '2024-12-17 14:51:53',
        },
      ],
    });
  },
  'POST /Api/Automation/SortingStoXzScanner/getInfo': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          id: '3',
          phone: '321',
          create_at: '2024-12-17 14:51:53',
          branch_code: 123,
        },
      }),
    );
  },
  'POST /Api/Automation/SortingZtPdaScanner/unbind': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingZtPdaScanner/login': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingZtPdaScanner/bind': (req, res) => {
    res.send(
      mock({
        // 'code|1': [0, 1001],
        code: 0,
        msg: 'success',
        data: [],
      }),
    );
  },
  'POST /Api/Automation/SortingZtPdaScanner/getInfoList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          id: '3',
          account: '********',
          device_imei: '1212112',
          device_brand: '23223',
          device_type: '1121',
          create_at: '2024-12-17 14:51:53',
          branch_code: 123,
        },
        {
          id: '4',
          account: '********',
          device_imei: '1212112',
          device_brand: '23223',
          device_type: '1121',
          create_at: '2024-12-17 14:51:53',
          branch_code: 123,
        },
      ],
    });
  },
  'POST /Api/Automation/SortingZtPdaScanner/getInfo': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          id: '3',
          account: '123',
          device_imei: '1212112',
          device_brand: '1212112',
          create_at: '2024-12-17 14:51:53',
          branch_code: 123,
        },
      }),
    );
  },
};
