/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { randomValue, delay } from './_utils';

const proxy = {
  /**
   * 单号源列表
   */

  // 获取所有单号源
  'POST /web/Waybill/getSources': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        taobao: [
          {
            valid: true,
            status: 1,
            user_name: 'alilan0204',
            auth_id: '101',
            source: [
              {
                branch_code: '200003',
                brand_code: 'STO',
                brand_type: 2,
                brand_name: '申通快递',
                kb_code: 'sto',
                branch_name: '上海虹桥公司',
                allocated_quantity: 14166,
                cancel_quantity: 27,
                quantity: '892',
                address: {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                },
                is_default: 0,
              },
              {
                branch_code: '352100',
                brand_code: 'STO',
                brand_type: 2,
                brand_name: '申通快递',
                kb_code: 'sto',
                branch_name: '福建宁德公司',
                allocated_quantity: 0,
                cancel_quantity: 0,
                quantity: '0',
                address: {
                  city: '宁德市',
                  detail: '中山路1号（周明测试）',
                  district: '蕉城区',
                  province: '福建省',
                },
                is_default: 0,
              },
              {
                branch_code: '51405',
                brand_code: 'ZTO',
                brand_type: 2,
                brand_name: '中通快递',
                kb_code: 'zt',
                branch_name: '昆山花桥',
                allocated_quantity: 11254,
                cancel_quantity: 3,
                quantity: '182',
                address: {
                  city: '苏州市',
                  detail: '花桥镇',
                  district: '昆山市',
                  province: '江苏省',
                },
                is_default: 0,
              },
            ],
            sourceName: '淘宝单号-',
          },
        ],
        wuliuyun: [
          {
            auth_id: '1',
            user_name: '快宝网络',
            valid: true,
            status: 1,
            source: [
              {
                branch_code: '200003',
                branch_name: '上海虹桥公司',
                brand_code: 'sto',
                brand_type: '2',
                brand_name: '申通快递',
                kb_code: 'sto',
                allocated_quantity: '533',
                cancel_quantity: '0',
                quantity: '0',
                address: [
                  {
                    detail: '上海虹桥公司',
                    district: '闵行区',
                    city: '上海市',
                    province: '上海',
                  },
                ],
                is_default: 0,
              },
            ],
            sourceName: '菜鸟单号-',
          },
        ],
        kop: [
          {
            accountId: '3516',
            account: '325636_0008',
            brand: 'yt',
            brand_name: '圆通速递',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_yt.png',
            num: '',
            branch_name: '圆通速递',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '3517',
            account: '海棠湾',
            brand: 'tt',
            brand_name: '天天快递',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_tt.png',
            num: '',
            branch_name: '天天快递',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '3521',
            account: '325636_0008',
            brand: 'ht',
            brand_name: '百世快递',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_ht.png',
            num: '16',
            branch_name: '百世快递',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '3522',
            account: '**************',
            brand: 'ems',
            brand_name: 'EMS',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_ems.png',
            num: '',
            branch_name: 'EMS',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '11206',
            account: '************',
            brand: 'sto118',
            brand_name: '申通代收货款面单',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
            num: '0',
            branch_name: '申通代收货款面单',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '15536',
            account: '安定1',
            brand: 'sto44',
            brand_name: '申通通用面单',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
            num: '0',
            branch_name: '申通通用面单',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '17136',
            account: '嵩明莫咯咯啵',
            brand: 'sf',
            brand_name: '',
            status: '2',
            logo: null,
            num: '',
            branch_name: '',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '22961',
            account: '525553',
            brand: 'sf',
            brand_name: '',
            status: '2',
            logo: null,
            num: '',
            branch_name: '',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
          {
            accountId: '22962',
            account: '123458',
            brand: 'tt',
            brand_name: '天天快递',
            status: '2',
            logo: 'http://img.kuaidihelp.com/brand_logo/icon_tt.png',
            num: '',
            branch_name: '天天快递',
            is_default: 0,
            sourceName: '快递公司单号-',
          },
        ],
      },
    });
  },
  // 获取所有单号源
  'POST /Api/orderManage/WaybillSource/alreadyList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        kop: [
          {
            auth_id: '6',
            source_type: '10',
            brand: 'sto',
            account: 'asdf',
            password: '6666',
            customer: 'aaa',
            quantity: '0',
          },
          {
            auth_id: '55',
            source_type: '10',
            brand: 'sto',
            account: 'asdf',
            password: '6666',
            customer: 'aaa',
            quantity: '3',
          },
          {
            auth_id: '56',
            source_type: '10',
            brand: 'jt',
            account: 'asdfjt',
            password: '6666',
            customer: 'aaa',
            quantity: '20',
          },
        ],
        third: [
          {
            key: 'taobao',
            nickName: 'SGZROGJVSEJJbHRqK3N3',
            auth_id: '8',
            source_type: '12',
            list: [
              {
                branch_code: '200003',
                brand_code: 'STO',
                brand_type: 2,
                brand_name: '申通快递',
                kb_code: 'sto',
                branch_name: '上海虹桥公司',
                allocated_quantity: 12816,
                cancel_quantity: 26,
                quantity: '964',
                address: {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '352100',
                brand_code: 'STO',
                brand_type: 2,
                brand_name: '申通快递',
                kb_code: 'sto',
                branch_name: '福建宁德公司',
                allocated_quantity: 0,
                cancel_quantity: 0,
                quantity: '0',
                address: {
                  city: '宁德市',
                  detail: '中山路1号（周明测试）',
                  district: '蕉城区',
                  province: '福建省',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '51405',
                brand_code: 'ZTO',
                brand_type: 2,
                brand_name: '中通快递',
                kb_code: 'zt',
                branch_name: '昆山花桥',
                allocated_quantity: 10455,
                cancel_quantity: 2,
                quantity: '424',
                address: {
                  city: '苏州市',
                  detail: '花桥镇',
                  district: '昆山市',
                  province: '江苏省',
                },
                segment_code: 'NORMAL',
              },
            ],
          },
          {
            key: 'wuliuyun',
            nickName: 'alilan02042',
            auth_id: '9',
            source_type: '2',
            list: [
              {
                branch_code: '200003',
                brand_code: 'STO',
                brand_type: 'STO',
                brand_name: '申通',
                kb_code: 'sto',
                branch_name: '上海虹桥公司',
                allocated_quantity: 16620,
                cancel_quantity: 27,
                quantity: 162,
                address: {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '352100',
                brand_code: 'STO',
                brand_type: 'STO',
                brand_name: '申通',
                kb_code: 'sto',
                branch_name: '福建宁德公司',
                allocated_quantity: 0,
                cancel_quantity: 0,
                quantity: 0,
                address: {
                  city: '宁德市',
                  detail: '中山路1号（周明测试）',
                  district: '蕉城区',
                  province: '福建省',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '774892',
                brand_code: 'YTO',
                brand_type: 'YTO',
                brand_name: '圆通',
                kb_code: 'yt',
                branch_name: '广西省贺州',
                allocated_quantity: 3,
                cancel_quantity: 0,
                quantity: 3,
                address: {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '51405',
                brand_code: 'ZTO',
                brand_type: 'ZTO',
                brand_name: '中通',
                kb_code: 'zt',
                branch_name: '昆山花桥',
                allocated_quantity: 12743,
                cancel_quantity: 3,
                quantity: 215,
                address: {
                  city: '苏州市',
                  detail: '花桥镇',
                  district: '昆山市',
                  province: '江苏省',
                },
                segment_code: 'NORMAL',
              },
            ],
          },
        ],
      },
    });
  },
  // 获取第三方单号源
  'POST /common/WaybillSource/third': (req, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {
        cainiao: [
          {
            valid: true,
            status: 1,
            user_name: '可用的',
            auth_id: '101',
            source: [
              {
                branch_code: '200003',
                brand_code: 'STO',
                brand_type: 2,
                brand_name: '申通快递',
                kb_code: 'sto',
                branch_name: '上海虹桥公司',
                allocated_quantity: 12816,
                cancel_quantity: 26,
                quantity: '964',
                address: {
                  city: '上海市',
                  detail: '通协路269号6号楼6楼A单元',
                  district: '长宁区',
                  province: '上海',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '352100',
                brand_code: 'STO',
                brand_type: 2,
                brand_name: '申通快递',
                kb_code: 'sto',
                branch_name: '福建宁德公司',
                allocated_quantity: 0,
                cancel_quantity: 0,
                quantity: '0',
                address: {
                  city: '宁德市',
                  detail: '中山路1号（周明测试）',
                  district: '蕉城区',
                  province: '福建省',
                },
                segment_code: 'NORMAL',
              },
              {
                branch_code: '51405',
                brand_code: 'ZTO',
                brand_type: 2,
                brand_name: '中通快递',
                kb_code: 'zt',
                branch_name: '昆山花桥',
                allocated_quantity: 10455,
                cancel_quantity: 2,
                quantity: '424',
                address: {
                  city: '苏州市',
                  detail: '花桥镇',
                  district: '昆山市',
                  province: '江苏省',
                },
                segment_code: 'NORMAL',
              },
            ],
          },
          {
            valid: true,
            status: 2,
            user_name: '失效的',
            auth_id: '102',
            source: [],
          },
        ],
        wuliuyun: [
          {
            auth_id: '1',
            user_name: '快宝网络',
            valid: true,
            status: 1,
            source: [
              {
                branch_code: '200003',
                branch_name: '上海虹桥公司',
                brand_code: 'sto',
                brand_type: '2',
                brand_name: '申通快递',
                kb_code: 'sto',
                allocated_quantity: '512',
                cancel_quantity: '0',
                quantity: '8',
                address: [
                  {
                    detail: '上海虹桥公司',
                    district: '闵行区',
                    city: '上海市',
                    province: '上海',
                  },
                ],
              },
            ],
          },
        ],
        pdd: [
          {
            relation_id: '2816',
            user_name: 'pdd3982093908',
            status: '1',
            owner_id: '398209',
            source: [
              {
                branch_code: '400000',
                brand_code: 'ZTO',
                brand_name: '中通快递',
                kb_code: 'zt',
                brand_type: 2,
                branch_name: '快宝专用',
                allocated_quantity: '0',
                cancel_quantity: '0',
                quantity: '0',
                address: [
                  {
                    province: '上海市',
                    city: '上海市',
                    district: '长宁区',
                    detail: '通协路269号建滔广场',
                  },
                ],
              },
            ],
          },
        ],
        pdd_universal: [
          {
            relation_id: '46',
            user_name: '郑海亮_2251799813693250',
            status: '1',
            owner_id: '2251799813693250',
            source: [
              {
                branch_code: '400000',
                brand_code: 'STO',
                brand_name: '申通快递',
                kb_code: 'sto',
                brand_type: 2,
                branch_name: '快宝测试',
                allocated_quantity: '0',
                cancel_quantity: '0',
                quantity: '0',
                address: [
                  {
                    province: '上海市',
                    city: '上海市',
                    district: '长宁区',
                    detail: '通协路269号建滔广场',
                  },
                ],
              },
            ],
          },
        ],
      },
    });
  },
  // 签约快递员列表
  'POST /common/WaybillSource/agree': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '160',
          userId: '1950685',
          courierMobile: '13524386704',
          uid: '20',
          courier_id: '268368',
          status: '0',
          is_companypay: '1',
          monthly: '公司月结运费',
          since: '1',
          nickname: '测试环境2',
          default_waybill: '{  }',
          protocol_time: '2019-07-31 15:44:37',
          create_time: '2019-10-24 13:34:01',
          relieve_time: '2019-10-23 14:43:49',
          istest: '0',
          confirm_time: null,
          province: null,
          city: null,
          district: null,
          agr_addr: null,
          type: '0',
          is_default: '0',
          brand: 'sto',
          waybill_sum: '-9999',
          waybill_price: '1',
          brandInfo: '申通',
          realName: '林仁翔',
          indexShopName: '快宝测试',
        },
        {
          id: '129756',
          userId: '0',
          courierMobile: '13564440381',
          uid: '20',
          courier_id: '927495',
          status: '0',
          is_companypay: '0',
          monthly: '现付',
          since: '0',
          nickname: '测试环境2',
          default_waybill: '',
          protocol_time: '2019-07-12 14:24:09',
          create_time: '2019-07-12 14:23:54',
          relieve_time: null,
          istest: '0',
          confirm_time: null,
          province: null,
          city: null,
          district: null,
          agr_addr: null,
          type: '0',
          is_default: '0',
          brand: 'yjt',
          waybill_sum: '-9999',
          waybill_price: '0.00',
          brandInfo: '一键通',
          realName: '刘义亮',
          indexShopName: '西藏拉萨公司',
        },
        {
          id: '129858',
          userId: '0',
          courierMobile: '13671856416',
          uid: '20',
          courier_id: '428220',
          status: '0',
          is_companypay: '0',
          monthly: '现付',
          since: '0',
          nickname: '测试环境2',
          default_waybill: '',
          protocol_time: null,
          create_time: '2019-10-29 09:41:48',
          relieve_time: null,
          istest: '0',
          confirm_time: null,
          province: null,
          city: null,
          district: null,
          agr_addr: null,
          type: '0',
          is_default: '0',
          brand: 'sto',
          waybill_sum: '0',
          waybill_price: '0.00',
          brandInfo: '申通',
          realName: '断小弦测试',
          indexShopName: '西藏拉萨公司',
        },
      ],
    });
  },
  // 大客户授权列表
  'POST /common/WaybillSource/kop': (_, res) => {
    // 'POST /Api/orderManage/WaybillSource/companyAuth': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          accountId: '5',
          account: '**************',
          brand: 'ems',
          brand_name: 'EMS',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_ems.png',
          num: '1221',
        },
        {
          accountId: '173',
          account: '李客户1',
          brand: 'sto44',
          brand_name: '申通通用面单',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
          num: '',
        },
        {
          accountId: '263',
          account: 'Ndndjx',
          brand: 'zjs',
          brand_name: '宅急送',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_zjs.png',
          num: '',
        },
        {
          accountId: '264',
          account: 'Djdjjcx',
          brand: 'gt',
          brand_name: '国通快递',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_gt.png',
          num: '',
        },
        {
          accountId: '266',
          account: 'Ndnx x',
          brand: 'ys',
          brand_name: '优速快递',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_ys.png',
          num: '',
        },
        {
          accountId: '267',
          account: 'Snd b',
          brand: 'dp',
          brand_name: '德邦快递',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_dp.png',
          num: '',
        },
        {
          accountId: '273',
          account: '0020999',
          brand: 'se',
          brand_name: '速尔快递',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_se.png',
          num: '',
        },
        {
          accountId: '288',
          account: 'Bsbsuxh',
          brand: 'sf',
          brand_name: '顺丰速运',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_sf.png',
          num: '',
        },
        {
          accountId: '300',
          account: 'Jebsbdvs',
          brand: 'sto44',
          brand_name: '申通通用面单',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
          num: '',
        },
        {
          accountId: '302',
          account: '************',
          brand: 'ht',
          brand_name: '百世快递',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_ht.png',
          num: '',
        },
        {
          accountId: '336',
          account: '5569999',
          brand: 'sto118',
          brand_name: '申通代收货款面单',
          status: '2',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
          num: '',
        },
      ],
    });
  },
  // 大客户授权列表
  // 'POST /common/WaybillSource/kop': (_, res) => {
  'POST /Api/orderManage/WaybillSource/companyAuth': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [],
    });
  },
  // 快递员自有面单列表
  'POST /common/WaybillSource/courier': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          authId: '1511',
          courierPhone: '***********',
          courierName: '错误工号',
          courierNo: '**********',
          courierId: '237',
          branchName: '西藏拉萨公司',
          brand: 'sto',
          quantity: -9999,
        },
        {
          authId: '1509',
          courierPhone: '***********',
          courierName: '操作13',
          courierNo: '2018008513',
          courierId: '988080',
          branchName: '上海嘉定公司',
          brand: 'sto',
          quantity: -9999,
        },
        {
          authId: '1518',
          courierPhone: '15201736791',
          courierName: '张三',
          courierNo: '4000000001',
          courierId: '621441',
          branchName: '快宝测试',
          brand: 'sto',
          quantity: 3,
        },
        {
          authId: '1521',
          courierPhone: '18616353908',
          courierName: '周明',
          courierNo: '51405.020',
          courierId: '0',
          branchName: '昆山花桥',
          brand: 'zt',
          quantity: -9999,
        },
      ],
    });
  },
  // 77商家权列表
  'POST /common/WaybillSource/branch': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          branchCode: '400000',
          branchName: '快宝测试',
          courier_phone: '',
          price: 0,
          status: '审核中',
          brand: 'sto',
        },
        {
          branchCode: '400002',
          branchName: '快宝测试',
          courier_phone: '',
          price: 0,
          status: '使用中',
          brand: 'sto',
        },
      ],
    });
  },
  // 大客户品牌列表
  'POST /Api/orderManage/WaybillSource/kopBrandList': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          brand: 'sto',
          brand_name: '申通通用面单',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
          list: [
            {
              brand: 'sto44',
              brand_name: '申通通用电子面单',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_sto.png',
              input: 3,
              input_text: ['请输入网点编号', '账号', '密码'],
            },
          ],
        },
        {
          brand: 'zt',
          brand_name: '中通快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_zt.png',
          list: [
            {
              brand: 'zt',
              brand_name: '中通快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_zt.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'yt',
          brand_name: '圆通速递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_yt.png',
          list: [
            {
              brand: 'yt',
              brand_name: '圆通速递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_yt.png',
              input: 2,
              input_text: ['请输入商家代码', '请输入密钥串'],
            },
          ],
        },
        {
          brand: 'yd',
          brand_name: '韵达快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_yd.png',
          list: [
            {
              brand: 'yd',
              brand_name: '韵达快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_yd.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'sf',
          brand_name: '顺丰速运',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_sf.png',
          list: [
            {
              brand: 'sf',
              brand_name: '顺丰速运',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_sf.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'tt',
          brand_name: '天天快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_tt.png',
          list: [
            {
              brand: 'tt',
              brand_name: '天天快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_tt.png',
              input: 3,
              input_text: ['请输入网点名称', '请输入大客户账号', '请输入大客户密码'],
            },
          ],
        },
        {
          brand: 'ht',
          brand_name: '百世快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_ht.png',
          list: [
            {
              brand: 'ht',
              brand_name: '百世快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_ht.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'ems',
          brand_name: 'EMS',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_ems.png',
          list: [
            {
              brand: 'ems',
              brand_name: 'EMS',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_ems.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'zjs',
          brand_name: '宅急送',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_zjs.png',
          list: [
            {
              brand: 'zjs',
              brand_name: '宅急送',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_zjs.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'dp',
          brand_name: '德邦快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_dp.png',
          list: [
            {
              brand: 'dp',
              brand_name: '德邦快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_dp.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'ys',
          brand_name: '优速快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_ys.png',
          list: [
            {
              brand: 'ys',
              brand_name: '优速快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_ys.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'gt',
          brand_name: '国通快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_gt.png',
          list: [
            {
              brand: 'gt',
              brand_name: '国通快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_gt.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'post',
          brand_name: '邮政包裹',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_post.png',
          list: [
            {
              brand: 'post',
              brand_name: '邮政包裹',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_post.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
        {
          brand: 'se',
          brand_name: '速尔快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_se.png',
          list: [
            {
              brand: 'se',
              brand_name: '速尔快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_se.png',
              input: 3,
              input_text: ['请输入网点编号', '请输入客户编号', '请输入快递员编号'],
            },
          ],
        },
        {
          brand: 'jt',
          brand_name: '极兔快递',
          logo: 'http://img.kuaidihelp.com/brand_logo/icon_jt1.png',
          list: [
            {
              brand: 'jt',
              brand_name: '极兔快递',
              logo: 'http://img.kuaidihelp.com/brand_logo/icon_jt1.png',
              input: 2,
              input_text: ['账号', '密码'],
            },
          ],
        },
      ],
    });
  },
  // 快递员自有面单 ：获取验证码
  'POST /common/SourceAccount/getCode': (_, res) => {
    res.send({
      code: 0,
      data: {},
      msg: 'success',
    });
  },
  // 77商家搜索
  'POST /common/SourceAccount/searchBranch': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          branchCode: '238100',
          branchName: '安徽含山公司',
          brand: 'sto',
        },
        {
          branchCode: '238101',
          branchName: '安徽含山昭关镇服务点',
          brand: 'sto',
        },
        {
          branchCode: '238131',
          branchName: '安徽含山运漕镇服务点',
          brand: 'sto',
        },
        {
          branchCode: '238141',
          branchName: '安徽含山铜闸镇服务点',
          brand: 'sto',
        },
        {
          branchCode: '238161',
          branchName: '安徽含山林头镇服务点',
          brand: 'sto',
        },
        {
          branchCode: '238171',
          branchName: '安徽含山陶厂镇服务点',
          brand: 'sto',
        },
        {
          branchCode: '238181',
          branchName: '安徽含山仙踪镇服务点',
          brand: 'sto',
        },
        {
          branchCode: '238191',
          branchName: '安徽含山清溪镇服务点',
          brand: 'sto',
        },
      ],
    });
  },
  // 团队分享单号源
  'POST /common/WaybillSource/group': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: [
        {
          id: '231',
          admin_uid: '32',
          group_name: '团队快递品牌1',
          waybill_share_info: {
            source: {
              authId: '1517',
              courierPhone: '15802160711',
              courierName: '快宝测试',
              courierNo: '400000',
              courierId: '480052',
              branchName: '西藏拉萨公司',
              brand: 'sto',
              quantity: -9999,
            },
            type: 'courier',
            auth_id: '1511',
          },
        },
        {
          id: '232',
          admin_uid: '322',
          group_name: '团队快递品牌2',
          waybill_share_info: {
            source: {
              address: [
                {
                  province: '上海市',
                  city: '上海市',
                  district: '长宁区',
                  detail: '通协路269号建滔广场',
                },
              ],
              allocated_quantity: '0',
              branch_code: '400000',
              branch_name: '快宝专用',
              brand_code: 'ZTO',
              brand_name: '中通快递',
              brand_type: 2,
              cancel_quantity: '0',
              kb_code: 'zt',
              quantity: '0',
            },
            type: 'pdd',
            auth_id: '1511',
          },
        },
      ],
    });
  },

  /** *
   * 单号源授权
   */
  // 根据工号获取快递员信息
  'POST /common/SourceAccount/getCourierInfo': (_, res) => {
    res.send(
      randomValue([
        {
          code: 0,
          msg: 'success',
          data: {
            courierPhone: '***********',
            showPhone: '132*****0525',
            courierNo: '850000',
            courierName: '西藏拉萨',
            branchName: '西藏拉萨公司',
            brand: 'sto',
          },
        },
        { code: 1, msg: '无法获取该快递员' },
      ]),
    );
  },
  // 大客户单号源授权
  'POST /common/SourceAccount/bindKop': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 77商家授权
  'POST /common/SourceAccount/bindBranch': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 绑定快递员单号源
  'POST /common/SourceAccount/bindCourier': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 绑定物流云单号源通过token
  'POST /Api/orderManage/WaybillSource/tokenAuth': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 物流云普通授权
  'POST /API/orderManage/WaybillSource/getThirdAuthUrl': (_, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {
          result: {
            url:
              'http://lcp.cloud.cainiao.com/permission/isv/grantpage.do?isvAppKey=305926&ext=eyJhcHBfaWQiOiI1MDAwMSIsInNpZ24iOiI5ZDAyMGI0MTllOGZmMTc0ZDhmMzJjMjYzZjA2MThhZiIsInBsYXRmb3JtIjoidl9ob21lIiwicGxhdGZvcm1fdWlkIjoiMjAiLCJhdXRoX3BsYXRmb3JtIjoiQ0FJTklBT0NMT1VEIiwiY2FsbF9iYWNrIjoiaHR0cDpcL1wvdmFwaS5rdWFpZGloZWxwLmNvbVwvdjdcL3dheWJpbGxcL0F1dGhcL2NhbGxiYWNrIiwicGxhdGZvcm1fY3VzdG9tZXIiOiIifQ==&redirectUrl=https://kop.kuaidihelp.com/v2/CallBack/CaiNiaoCallBack/caiNiaoCallBack#cainiao',
            remark: '',
            pay_type: '',
            add_service: [],
          },
          custom_value: '',
          reason: 'ok',
          unique_no: 'ad3c873eaccaa228a398b6199737c12700000000',
        },
      });
    });
  },
  // 绑定淘宝单号源
  'POST /Api/orderManage/WaybillSource/getThirdAuthUrl': (req, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {
          url:
            'https://oauth.taobao.com/authorize?response_type=code&client_id=23566819&state=eyJhcHBfaWQiOiI5MDAwMCIsInBsYXRmb3JtIjoiY2l0eSIsInBsYXRmb3JtX3VpZCI6IjE0OCIsInBsYXRmb3JtX2N1c3RvbWVyIjoiIiwiYXV0aF9wbGF0Zm9ybSI6IkNBSU5JQU9UQiIsImNhbGxfYmFjayI6Imh0dHA6XC9cL2NpdHkua3VhaWRpaGVscC5jb21cL3YxXC9DYWxsYmFja1wvd2F5YmlsbEF1dGg/c2hvcElkPTE0OCZ0eXBlPXRhb2JhbyIsInNpZ24iOiI3NThmMDJhMjVhYmM0OWQ0ZDU4YTY2MjYxMjdjZThmNiJ9&view=wap&redirect_uri=http://wangciyuantest.kuaidihelp.com/v2/CallBack/CaiNiaoTBCallBack/CaiNiaoTbCallBack',
          remark: '',
          pay_type: '',
          add_service: [],
        },
      });
    });
  },
  // 绑定拼多多商家单号源
  'POST /Api/orderManage/WaybillSource/pddMerchant': (_, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {
          url: 'https://mms.pinduoduo.com/service-market/service-detail?detailId=192',
          cookieData:
            'qP/zWJnY8QU3n8tc8CmZbqqwjv4WT5KQyU0IlAr9r1ZbfUhFIeClY3JErKO6tJX9FI3U5ayZ3rG2/+V7xcPDPJy1YOn2FCrL+6dLaOwF4cYTrCaFL8R7W9EeK0hOUQxB',
        },
      });
    });
  },
  // 绑定拼多多通用单号源
  'POST /Api/orderManage/WaybillSource/pddUniversal': (_, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data: {
          url:
            'https://wb.pinduoduo.com/logistics/auth?response_type=code&client_id=6ec8e69a9cf4408babbecee590c3b993&redirect_uri=http%3A%2F%2Fvapi.kuaidihelp.com%2Fv8%2Fwaybill%2FAuth%2Fcallback&state=uniweb_vhome_20',
          cookieData:
            'qP/zWJnY8QU3n8tc8CmZbqqwjv4WT5KQyU0IlAr9r1ZbfUhFIeClY3JErKO6tJX9FI3U5ayZ3rG2/+V7xcPDPJy1YOn2FCrL+6dLaOwF4cYTrCaFL8R7W9EeK0hOUQxB',
        },
      });
    });
  },
  // 微店授权
  'POST /web/ThirdAuth/wd': (_, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data:
          'https://oauth.open.weidian.com/oauth2/authorize?response_type=code&state=uniweb_sync_1&appkey=686241&redirect_uri=http://vapi.kuaidihelp.com/v1/vhome/auth/Callback/weidian',
      });
    });
  },
  // 有赞授权
  'POST /web/ThirdAuth/yz': (_, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data:
          'https://open.youzan.com/oauth/authorize?response_type=code&client_id=de1bba2c2baae3a3f3&state=uniweb_sync_1&redirect_uri=http://vapi.kuaidihelp.com/v1/vhome/auth/Callback/youzan',
      });
    });
  },
  // 快手授权
  'POST /web/ThirdAuth/ks': (_, res) => {
    delay(() => {
      res.send({
        code: 0,
        msg: 'success',
        data:
          'https://s.kwaishop.com/oauth/authorize?app_id=ks685673045318821036&response_type=code&scope=user_info,merchant_refund,merchant_item,merchant_order,user_base&redirect_uri=https://vapi.kuaidihelp.com/v9/auth/Callback/ks&state=uniweb_sync_20',
      });
    });
  },
  /** *
   * 单号源删除解绑
   */
  // 取消授权
  'POST /Api/orderManage/WaybillSource/delAuthInfo': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 大客户单号源取消授权
  'POST /common/SourceAccount/cancelKop': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 取消淘宝单号源
  'POST /common/SourceAccount/cancelTb': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 取消物流云单号源
  'POST /common/SourceAccount/cancelWly': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 解绑快递员自有面单
  'POST /common/SourceAccount/unbindCourier': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 解绑拼多多商家单号源
  'POST /common/SourceAccount/cancelPdd': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  // 解绑拼多多通用单号源
  'POST /common/SourceAccount/cancelPddUniversal': (_, res) => {
    res.send({
      code: 0,
      msg: 'success',
      data: {},
    });
  },
  'POST /Api/orderManage/WaybillSource/getCompanyTicket': (_, res) => {
    setTimeout(() => {
      res.send({
        code: 0,
        msg: '',
        data: [
          {
            id: '6',
            quantity: '10',
          },
          {
            id: '55',
            quantity: '-9999',
          },
          {
            auth_id: '56',
            quantity: '0',
          },
        ],
      });
    }, 10000);
  },
};

export default proxy;
