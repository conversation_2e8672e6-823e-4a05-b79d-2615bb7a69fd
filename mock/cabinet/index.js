/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  // 获取黑名单列表
  'POST /Api/Statistics/getAreaEcCurrentDayStat': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          in_number: 0, // 今日投件数
          pickUp: 0, // 今日取件数
          send_number: 0, // 今日寄件数
          profit: '0.00', // 今日总收入
          delay: 180, // 滞留件
          notice: 14, // 今日通知失败
          upload: 0, // 今日上传失败
          shield: 0, // 禁用格口
        },
      }),
    );
  },
  'POST /Api/Statistics/getAreaEcInOutStat': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          '2025-05-20': {
            storage_num: 0, // 入库数
            out_num: 0, // 出库数
            back_num: 0, // 退回数
          },
          '2025-05-21': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
          '2025-05-22': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
          '2025-05-23': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
          '2025-05-24': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
          '2025-05-25': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
          '2025-05-26': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
          '2025-05-27': {
            'storage_num|1-100': 1,
            'out_num|1-100': 1,
            'back_num|1-100': 1,
          },
        },
      }),
    );
  },
  'POST /Api/Statistics/getAreaEcBrandStat': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          storage_list: [
            { brand: 'EMS', num: 45877 },
            { brand: '中通', num: 28126 },
            { brand: '圆通', num: 21138 },
            { brand: '韵达', num: 20370 },
            { brand: '极兔', num: 13719 },
            { brand: '申通', num: 12777 },
          ],
          out_list: [
            { brand: 'EMS', num: 45877 },
            { brand: '中通', num: 28126 },
            { brand: '圆通', num: 21138 },
            { brand: '韵达', num: 20370 },
            { brand: '极兔', num: 13719 },
          ],
          send_list: [
            { brand: 'EMS', num: 45877 },
            { brand: '中通', num: 28126 },
            { brand: '圆通', num: 21138 },
            { brand: '韵达', num: 20370 },
            { brand: '极兔', num: 13719 },
          ],
        },
      }),
    );
  },
  'POST /Api/Statistics/getAreaEcIncomeStat': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: '成功',
        data: {
          '2025-05-20|1-100': 1,
          '2025-05-21|1-100': 1,
          '2025-05-22|1-100': 1,
          '2025-05-23|1-100': 1,
          '2025-05-24|1-100': 1,
          '2025-05-25|1-100': 1,
          '2025-05-26|1-100': 1,
          '2025-05-27|1-100': 1,
          '2025-05-28|1-100': 1,
        },
      }),
    );
  },
  'POST /Api/Statistics/getAreaEcSendStat': (req, res) => {
    res.send(
      mock({
        code: 0,
        msg: 'success',
        data: {
          '2025-05-20': '0',
          '2025-05-21': '8',
          '2025-05-22': '0',
          '2025-05-23': '0',
          '2025-05-24': '0',
          '2025-05-25': '0',
          '2025-05-26': '0',
          '2025-05-27': '0',
          '2025-05-28': '0',
          '2025-05-29': '0',
          '2025-05-30': '0',
          '2025-05-31': '0',
          '2025-06-01': '0',
          '2025-06-02': '0',
          '2025-06-03': '0',
          '2025-06-04': '0',
          '2025-06-05': '0',
          '2025-06-06': '0',
          '2025-06-07': '0',
          '2025-06-08': '0',
          '2025-06-09': '0',
          '2025-06-10': '0',
          '2025-06-11': '0',
          '2025-06-12': '1',
          '2025-06-13': '0',
          '2025-06-14': '0',
          '2025-06-15': '0',
          '2025-06-16': '0',
          '2025-06-17': '0',
          '2025-06-18': '0',
          '2025-06-19': '0',
        },
      }),
    );
  },
  'POST /Api/YZ/Cabinet/revisePrice': (req, res) => {
    res.send({ code: 0 });
  },
  'POST /Api/YZ/Cabinet/cabinetPrice': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        {
          size: '1',
          amount: '0.02',
          deposit: '0.02',
        },
        {
          size: '2',
          amount: '0.01',
          deposit: '0.01',
        },
        {
          size: '3',
          amount: '0.01',
          deposit: '0.01',
        },
        {
          size: '4',
          amount: '0.01',
          deposit: '0.01',
        },
        {
          size: '5',
          amount: '0.01',
          deposit: '0.01',
        },
        {
          size: '6',
          amount: '0.05',
          deposit: '0.06',
        },
      ],
    });
  },
  'POST /Api/YZ/Cabinet/setBrandDiscount': (req, res) => {
    res.send({ code: 0 });
  },

  'POST /Api/YZ/Cabinet/brandDiscount': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        open: '1',
        discount: {
          9: ['sf', 'jd'],
          2.2: ['zt', 'yd', 'yz', 'ems'],
        },
      },
    });
  },

  'POST /Api/YZ/Cabinet/getBrandList': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: [
        'sto',
        'yz',
        'zykd',
        'ddmc',
        'mtyx',
        'cxyx',
        'xsyx',
        'qita',
        'zt',
        'yd',
        'zjs',
        'hw',
        'wph',
        'tmcs',
        'sn',
        'fedex',
        'kbtc',
        'ztky',
        'htky',
        'sfky',
        'jd',
        'yt',
        'dn',
        'ems',
        'ane',
        'ys',
        'jt',
        'sf',
        'dp',
      ],
    });
  },
  'POST /Api/YZ/Cabinet/getOutTimeFee': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        id: '176',
        rate_details: {
          free_hours: '0',
          initial_charge: '0.1',
          additional_hours: '1',
          additional_charge: '0.1',
          max_charge: '0.1',
        },
        holiday_free: '0',
        weekend_free: '1',
      },
    });
  },
  'POST /Api/YZ/Cabinet/setOutTimeFee': (req, res) => {
    res.send({ code: 0 });
  },

  'POST /Api/YZ/Cabinet/setBrandOutTimeFee': (req, res) => {
    res.send({ code: 0 });
  },

  'POST /Api/YZ/Cabinet/getBrandOutTimeFee': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        brand_list: [
          {
            rate_details: {
              free_hours: '24',
              initial_charge: '1.0',
              additional_hours: '2',
              additional_charge: '1.0',
              max_charge: '3',
            },
            holiday_status: true,
            holiday_free: '1',
            weekend_free: '1',
            brand_list: ['sto', 'zt', 'yd'],
            id: '0',
          },
        ],
        status: '1',
      },
    });
  },

  'POST /Api/YZ/Cabinet/setOutTimeRake': (req, res) => {
    res.send({ code: 0 });
  },

  'POST /Api/YZ/Cabinet/getOutTimeRake': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: {
        // timeout_user: '1',
        // timeout_config: {
        //   type: '1',
        //   data: '0.03',
        //   start: '2025-05-22',
        //   end: '2026-05-22',
        // },
      },
    });
  },
  'POST /Api/YZ/Cabinet/prohibitSetFee': (req, res) => {
    res.send({ code: 0 });
  },
};
