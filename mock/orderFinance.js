/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from "mockjs";

const { mock } = mockjs;
import { randomValue } from "./_utils";

export default {
  // 获取驿站列表
  "POST /Api/YZ/CourierStation/getList": (req, res) => {
    res.send({
      msg: "成功",
      code: 0,
      data: {
        total: 10,
        list: [
          {
            quotation: [],
            cm_id: "2225710",
            company_name: "徐肖磊测试驿站",
            phone: "18516011018",
            id: "123",
          },
          {
            quotation: [
              {
                id: "100211",
                name: "(加盟商)晋级赛开始考试",
                brand: "sto,jt",
                customer: "散客",
                courier_id: "1614783",
                type: "2",
                create_time: "2021-09-01 09:57:50",
                update_time: "2021-09-01 15:38:35",
                is_leaguer: "1",
                leaguer_status: "1",
                league_quotation_id: "10021",
              },
              {
                id: "1001421",
                name: "测试报价单",
                brand: "sto,jt",
                customer: "散客",
                courier_id: "1614783",
                type: "2",
                create_time: "2021-09-01 09:57:50",
                update_time: "2021-09-01 15:38:35",
                is_leaguer: "1",
                leaguer_status: "2",
                league_quotation_id: "10014",
              },
            ],
            cm_id: "1614783",
            company_name: "测试",
            phone: "15201736791",
            id: "1234",
          },
        ],
      },
    });
  },
  "POST /Api/YZ/CourierStation/quotationList": (req, res) => {
    res.send({
      msg: "成功",
      code: 0,
      data: {
        total: 10,
        list: [
          {
            id: "10021" + req.body.page,
            name: "晋级赛开始考试",
            brand: "jt",
            shop_id: "240",
            create_time: "2021-09-01 09:56:45",
            update_time: "2021-09-01 09:56:45",
          },
          {
            id: "100142" + req.body.page,
            name: "(加盟商)晋级赛开始考试",
            brand: "jd,sto",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "10014" + req.body.page,
            name: "测试报价单",
            brand: "zt,yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "10015" + req.body.page,
            name: "测试报价单",
            brand: "zt",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "10017" + req.body.page,
            name: "测试报价单",
            brand: "yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "10023424" + req.body.page,
            name: "测试报价单",
            brand: "yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "10017743523" + req.body.page,
            name: "测试报价单",
            brand: "yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "1001715234" + req.body.page,
            name: "测试报价单",
            brand: "yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "100179453345" + req.body.page,
            name: "测试报价单",
            brand: "yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
          {
            id: "100172363" + req.body.page,
            name: "测试报价单",
            brand: "yd",
            shop_id: "240",
            create_time: "2021-09-01 09:57:50",
            update_time: "2021-09-01 15:38:35",
          },
        ],
      },
    });
  },
  // 检查名称是否已存在
  "POST /Api/YZ/CourierStation/quotationCheckName": (req, res) => {
    res.send(
      randomValue([
        {
          code: 1,
          msg: "名称重复",
          data: {},
        },
        {
          code: 0,
          msg: "success",
          data: {},
        },
      ])
    );
  },
  "POST /v1/Quotation/Quotation/kuaidiBrands": (req, res) => {
    res.send({
      code: 0,
      data: {
        tt: "天天快递",
        yd: "韵达快递",
        ht: "百世快递",
        yt: "圆通快递",
        zt: "中通快递",
        sto: "申通快递",
      },
    });
  },
  "POST /Api/YZ/CourierStation/quotationView": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {
        id: "10021",
        name: "晋级赛开始考试",
        brand: "sto,jt",
        shop_id: "240",
        create_time: "2021-09-01 09:56:45",
        update_time: "2021-09-01 09:56:45",
        areas: [
          {
            id: "56058",
            main_id: "10021",
            shop_id: "240",
            provices: "上海、浙江、江苏、山东、安徽",
            f_weight: "0.4",
            s_weight: "0.4",
            f_kg: "1.0",
            s_kg: "1.0",
            create_time: "2021-09-01 09:56:45",
            update_time: "2021-09-01 09:56:45",
          },
          {
            id: "56054",
            main_id: "10021",
            shop_id: "240",
            provices: "北京、重庆、广东",
            f_weight: "2",
            s_weight: "2",
            f_kg: "1.0",
            s_kg: "1.0",
            create_time: "2021-09-01 09:56:45",
            update_time: "2021-09-01 09:56:45",
          },
        ],
      },
    });
  },
  "POST /Api/YZ/CourierStation/setInnQuotation": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },

  "POST /Api/YZ/CourierStation/deleteCheck": (req, res) => {
    res.send(
      randomValue([
        {
          code: 1,
          msg: "报价单已经被使用，是否需要强制删除",
          data: {},
        },
        {
          code: 0,
          msg: "success",
          data: {},
        },
      ])
    );
  },
  "POST /Api/YZ/CourierStation/quotationDelete": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },
  "POST /Api/YZ/CourierStation/quotationSave": (req, res) => {
    res.send({
      code: 0,
      msg: "success",
      data: {},
    });
  },
};
