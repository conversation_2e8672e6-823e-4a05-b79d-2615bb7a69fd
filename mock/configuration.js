/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import {
  getConInfo,
  getBranchInfo,
  userConfig,
  removeInfo,
  switchConfig,
  verifyResult,
  getIcons,
  getDisabledIcon,
} from './gunOrder.js';

const proxy = {
  // 获取巴枪配置信息
  'POST /v1/TotalDistribution/GunInformation/getConInfo': getConInfo,
  'POST /v1/TotalDistribution/ExpressCompanyInfo/getBranchInfo': getBranchInfo,
  'POST /v1/TotalDistribution/GunInformation/userConfig': userConfig,
  'POST /v1/TotalDistribution/GunInformation/removeInfo': removeInfo,
  'POST /v1/TotalDistribution/GunInformation/updateStatus': switchConfig,
  'POST /v1/TotalDistribution/GunInformation/batchVerifyGunAccount': verifyResult,
  'POST /Api/Courier/getIconList': getIcons,
  'POST /Api/Courier/getCourierShieldIcons': getDisabledIcon,
  'POST /Api/Courier/saveCouriersShieldIcons': (req, res) => res.send({ code: 0 }),
  'POST /Api/Courier/saveConfig': (req, res) => res.send({ code: 0 }),
  'POST /v1/TotalDistribution/ExpressCompanyInfo/checkStoAccount': (req, res) =>
    res.send({ code: 0 }),
  'POST /v1/TotalDistribution/GunInformation/sendLoginSmsCode': (req, res) =>
    res.send({ code: 0 }),
  'POST /v1/TotalDistribution/GunInformation/login': (req, res) => res.send({ code: 0 }),
};

export default proxy;
