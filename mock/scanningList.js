/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

// 扫描记录统计
export function getScanningList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 64; i++) {
    orderList.push({
      "waybill_no":`66123456789003${i}`,
      "shop_name":"总部测试",
      "cm_name":"快宝同城测试",
      "cm_code":`00001${i}`,
      "waybill_type":"3",
      "cnt":"2",
      "sj":`1${i}`,
      "pj":`0${i}`,
      "qsj":`1${i}`,
      "name_code":"快宝同城测试-001",
      "branch_code":`000${i}1`,
    });
  }

  let size = 10;
  if (params.size) {
    size = params.size * 1;
  }
  res.send({
    code: 0,
    msg: "获取成功",
    data: {
      cnt: orderList.length,
      pageSize:size,
      // page: parseInt(params.page, 10) || 1,
      ret: orderList,
    },
  });
}

// 扫描记录查询（单号）
export function getSingleList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 10; i++) {
    orderList.push({
      scan_time:"04/15 00:00",
      update_time:"04/15 17:06",
      waybill_type:"签收扫描",
      shop_name:"总部测试",
      cm_name:"快宝同城测试",
      dev_id:`B52748710493645${i}`,
      cm_code:1+i,
      sort:1555257606+i,
      scan_content:"已签收",
    });
  }

  let size = 10;
  if (params.size) {
    size = params.size * 1;
  }

  res.send({
    code: 0,
    msg: "获取成功",
    data: orderList,
  });
}

// 扫描记录查询（业务员）
export function getWorkList(req, res) {
  const params = req.body;
  const orderList = [];
  for (let i = 0; i < 30; i++) {
    orderList.push({
      waybill_no:`66123456789005${i}`,
      scan_time:"04/15 00:00",
      update_time:"04/15 17:08",
      waybill_type:"收件扫描",
      dev_id:`B52748710493645${i}`,
      cm_code:`001${i}`,
      sort:1555257608+i,
      scan_content:"已收件",
      steid: `${i}`,
    });
  }

  let size = 10;
  if (params.size) {
    size = params.size * 1;
  }

  res.send({
    code: 0,
    msg: "获取成功",
    data: {
      cnt: orderList.length,
      pageSize:size,
      page: parseInt(params.page, 10) || 1,
      ret: orderList,
    },
  });
}