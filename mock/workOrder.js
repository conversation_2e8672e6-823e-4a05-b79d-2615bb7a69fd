/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from "mockjs";

export default {
  // 检查是否开启工单系统
  "POST /Api/Feedback/checkSysStatus": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: {
        status: mockjs.Random.integer(0, 1),
        des: "",
      },
    });
  },
  // 开启或关闭工单系统
  "POST /Api/Feedback/updateSysStatus": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: {
        registered: mockjs.Random.integer(0, 1),
      },
    });
  },
  // 获取工单系统登录token
  "POST /Api/Feedback/login": (req, res) => {
    res.send({
      code: 0,
      msg: "成功",
      data: {
        token: "6cff05c06a92ba4727bda67007dcf8f1",
      },
    });
  },
};
