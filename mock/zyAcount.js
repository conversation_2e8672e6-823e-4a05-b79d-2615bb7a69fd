/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export default {
  'POST /Api/ChinaPost/Branch/subBranch': (req, res) => {
    let list = [];
    const id = req.body.id || '0';

    switch (id) {
      case '0':
        list = [
          {
            id: '-3',
            pid: '0',
            name: '全部地区',
            level: '1',
          },
          {
            id: '1',
            pid: '0',
            name: '浙江省',
            level: '1',
          },
          {
            id: '10',
            pid: '0',
            name: '江苏省',
            level: '1',
          },
        ];
        break;
      case '1':
        list = [
          {
            id: '2',
            pid: '1',
            name: '杭州市',
            level: '2',
          },
          {
            id: '100',
            pid: '1',
            name: '宁波市',
            level: '2',
          },
        ];
        break;
      case '2':
        list = [
          {
            id: '3',
            pid: '2',
            name: '西湖区',
            level: '3',
          },
          {
            id: '5',
            pid: '2',
            name: '拱墅区',
            level: '3',
          },
        ];
        break;
      case '3':
        list = [
          {
            id: '4',
            pid: '3',
            name: '桥西支局',
            township: '新城街道',
            level: '4',
          },
        ];
        break;
      case '5':
        list = [
          {
            id: '5-1',
            pid: '5',
            name: '拱墅的支局',
            township: '新城街道',
            level: '4',
          },
        ];
        break;
      default:
        break;
    }
    res.send({
      msg: '获取成功',
      code: 0,
      data: list,
    });
  },
  // 获取中邮站点列表数据
  'POST /Api/ChinaPost/Account/list': (req, res) => {
    const list = [];
    Array.from({
      length: 25,
    }).forEach((item, index) => {
      list.push({
        super: `${index}`,
        uid: `uid${index}`,
        phone: `**********${index}`,
        name: `杭州${index}`,
        branch_id: '2',
        branch_level: '2',
        privilege: [3, 4, 5],
        status: '2',
        status_desc: '使用中',
        branch: [
          {
            id: '0',
            name: '中国邮政总公司',
            level: '0',
            pid: '-1',
          },
          {
            id: '1',
            name: '浙江省',
            level: '1',
            pid: '0',
          },
          {
            id: '2',
            name: '杭州市',
            level: '2',
            pid: '1',
          },
          {
            id: '3',
            pid: '2',
            name: '西湖区',
            level: '3',
          },
          // {
          //   'id': '4',
          //   'pid': '3',
          //   'name': "桥西支局",
          //   'level': '4',
          // },
        ],
      });
    });

    res.send({
      msg: '获取成功',
      code: 0,
      data: {
        total: 25,
        page: 1,
        pageSize: 10,
        list,
      },
    });
  },
  // 中邮，修改账号权限及删除
  'POST /Api/ChinaPost/Account/status': (req, res) => {
    res.send({
      msg: '切换成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，添加及编辑账号
  'POST /Api/ChinaPost/Account/save': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，账号权限分配
  'POST /Api/ChinaPost/Account/privilegeSetting': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，获取下属驿站
  'POST /Api/ChinaPost/Dak/subDak': (req, res) => {
    const { status } = req.body;

    res.send({
      msg: '成功',
      code: 0,
      data: {
        total: 25,
        list: [
          {
            cm_id: '2113758',
            inn_name: status == 2 ? '已禁用驿站名称1' : '驿站名称1',
            concat_name: '张三丰1',
            phone: '************',
            concat_phone: '************',
            province: '浙江省',
            city: '杭州市',
            district: '西湖区',
            town: '桥西支局',
            concat_location: '庆春路231号',
            create_at: '2020-10-12 12:30',
            disabled_at: '2021-12-1 17:44:41',
            join_at: '2020-10-13 11:30',
            yesterday_in_num: -1,
            in_growth_num: 0,
            branch: [
              {
                id: '0',
                pid: '-1',
                name: '中国邮政总公司',
                level: '0',
              },
              {
                id: '1',
                name: '浙江省',
                level: '1',
                pid: '0',
              },
              {
                id: '2',
                name: '杭州市',
                level: '2',
                pid: '1',
              },
              {
                id: '3',
                pid: '2',
                name: '西湖区',
                level: '3',
              },
              {
                id: '4',
                pid: '3',
                name: '桥西支局',
                level: '4',
              },
            ],
            qrcode:
              'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFc8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyWlp0WDV0UFdlSGwxMDAwMHcwM20AAgRF_hFcAwQAAAAA',
          },
          {
            cm_id: '3',
            inn_name: status == 2 ? '已禁用驿站名称12' : '驿站名称12',
            concat_name: '张三丰12',
            phone: '************2',
            concat_phone: '************2',
            province: '浙江省',
            city: '杭州市',
            district: '西湖区',
            town: '桥西支局',
            concat_location: '庆春路231号',
            create_at: '2020-10-12 12:30',
            join_at: '2020-10-13 11:30',
            disabled_at: '2021-12-1 17:44:41',
            yesterday_in_num: 111,
            in_growth_num: -11,
            branch: [
              {
                id: '0',
                pid: '',
                name: '中国邮政总公司',
                level: '0',
              },
              {
                id: '1',
                name: '浙江省',
                level: '1',
                pid: '0',
              },
              {
                id: '2',
                name: '杭州市',
                level: '2',
                pid: '1',
              },
              {
                id: '5',
                pid: '2',
                name: '拱墅区',
                level: '3',
              },
            ],
            qrcode:
              'https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=gQFc8DwAAAAAAAAAAS5odHRwOi8vd2VpeGluLnFxLmNvbS9xLzAyWlp0WDV0UFdlSGwxMDAwMHcwM20AAgRF_hFcAwQAAAAA',
          },
        ],
        page: 1,
        pageSize: 20,
      },
    });
  },
  // 中邮，获取代审核驿站列表
  'POST /Api/ChinaPost/Dak/waitingAudit': (req, res) => {
    const list = [];

    Array.from({
      length: 25,
    }).forEach((item, index) => {
      list.push({
        cm_id: `${index}`,
        inn_name: `待审核驿站名称${index}`,
        concat_name: `张三丰${index}`,
        phone: `15818999283${index}`,
        concat_phone: `15818999283${index}`,
        province: '浙江省',
        city: '杭州市',
        district: '西湖区',
        town: '桥西支局',
        concat_location: '庆春路231号',
        create_at: '2020-10-12 12:30',
        join_time: '2020-10-13 11:30',
        branch: [
          {
            id: '0',
            pid: '-1',
            name: '中国邮政总公司',
            level: '0',
          },
          {
            id: '1',
            name: '浙江省',
            level: '1',
            pid: '0',
          },
          {
            id: '2',
            name: '杭州市',
            level: '2',
            pid: '1',
          },
        ],
      });
    });

    res.send({
      msg: '成功',
      code: 0,
      data: {
        total: 25,
        list: [
          {
            cm_id: '11',
            inn_name: '待审核驿站名称11',
            concat_name: '张三丰11',
            phone: '************1',
            concat_phone: '************1',
            province: '浙江省',
            city: '杭州市',
            district: '西湖区',
            town: '桥西支局',
            concat_location: '庆春路231号',
            create_at: '2020-10-12 12:30',
            join_time: '2020-10-13 11:30',
            update_at: '2020-10-12 11:30',
            branch: [
              {
                id: '0',
                pid: '-1',
                name: '中国邮政总公司',
                level: '0',
              },
              {
                id: '1',
                name: '浙江省',
                level: '1',
                pid: '0',
              },
              {
                id: '2',
                name: '杭州市',
                level: '2',
                pid: '1',
              },
            ],
          },
          {
            cm_id: '22',
            inn_name: '待审核驿站名称22',
            concat_name: '张三丰22',
            phone: '1581899928322',
            concat_phone: '1581899928322',
            province: '浙江省',
            city: '杭州市',
            district: '西湖区',
            town: '桥西支局',
            concat_location: '庆春路231号',
            create_at: '2020-10-12 12:30',
            join_time: '2020-10-13 11:30',
            update_at: '2020-10-13 11:30',
            branch: [
              {
                id: '0',
                pid: '-1',
                name: '中国邮政总公司',
                level: '0',
              },
              {
                id: '10',
                pid: '0',
                name: '江苏省',
                level: '1',
              },
            ],
          },
        ],
        page: 1,
        pageSize: 20,
      },
    });
  },
  // 中邮，禁用或恢复驿站
  'POST /Api/ChinaPost/Dak/setAble': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，审核驿站（拒绝或通过）
  'POST /Api/ChinaPost/Dak/checkSub': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，添加支局
  'POST /Api/ChinaPost/Branch/addBranch': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，编辑支局
  'POST /Api/ChinaPost/Branch/editBranch': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 中邮，下属驿站修改支局
  'POST /Api/ChinaPost/Dak/changeBranch': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: {},
    });
  },
  // 共配权限修改，获取省级公司
  'POST /Api/ChinaPost/ZyGpJur/getProvincial': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: [
        {
          id: '1',
          pid: '0',
          name: '上海市',
          level: '1',
        },
        {
          id: '334',
          pid: '0',
          name: '浙江省',
          level: '1',
        },
        {
          id: '335',
          pid: '0',
          name: '江苏省',
          level: '1',
        },
      ],
    });
  },
  // 共配权限修改，获取市级公司
  'POST /Api/ChinaPost/ZyGpJur/getCity': (req, res) => {
    res.send({
      msg: '成功',
      code: 0,
      data: [
        {
          id: '1149',
          pid: '334',
          name: '嘉兴市',
          level: '2',
        },
        {
          id: '682',
          pid: '334',
          name: '宁波市',
          level: '2',
        },
        {
          id: '336',
          pid: '334',
          name: '杭州市',
          level: '2',
        },
        {
          id: '1899',
          pid: '334',
          name: '丽水市',
          level: '2',
        },
        {
          id: '2',
          pid: '334',
          name: '杭州市',
          level: '2',
        },
      ],
    });
  },

  // 中邮导出
  'POST /Api/YZ/CourierStation/exportAllInn': (req, res) => {
    res.send({
      code: 0,
      msg: '导出成功',
      data: {},
    });
  },
  // 中邮，添加支局，获取乡镇数据列表
  'POST /Api/ChinaPost/Branch/getTownshipBranch': (req, res) => {
    res.send({
      code: 0,
      msg: '导出成功',
      data: [
        {
          name: '新城街道',
          pid: '8342',
          code: '411623001000',
        },
        {
          name: '东城街道',
          pid: '8342',
          code: '411623002000',
          bind: 1,
        },
        {
          name: '老城街道',
          pid: '8342',
          code: '411623003000',
        },
        {
          name: '黄寨镇',
          pid: '8342',
          code: '411623101000',
        },
        {
          name: '练集镇',
          pid: '8342',
          code: '411623102000',
        },
        {
          name: '魏集镇',
          pid: '8342',
          code: '411623103000',
        },
        {
          name: '固墙镇',
          pid: '8342',
          code: '411623104000',
        },
        {
          name: '白寺镇',
          pid: '8342',
          code: '411623105000',
        },
        {
          name: '巴村镇',
          pid: '8342',
          code: '411623106000',
        },
        {
          name: '谭庄镇',
          pid: '8342',
          code: '411623107000',
        },
        {
          name: '邓城镇',
          pid: '8342',
          code: '411623108000',
        },
        {
          name: '胡吉镇',
          pid: '8342',
          code: '411623109000',
        },
        {
          name: '城关乡',
          pid: '8342',
          code: '411623200000',
        },
        {
          name: '平店乡',
          pid: '8342',
          code: '411623202000',
        },
        {
          name: '袁老乡',
          pid: '8342',
          code: '411623203000',
        },
        {
          name: '化河乡',
          pid: '8342',
          code: '411623204000',
        },
        {
          name: '姚集镇',
          pid: '8342',
          code: '411623110',
        },
        {
          name: '舒庄乡',
          pid: '8342',
          code: '411623206000',
        },
        {
          name: '大武乡',
          pid: '8342',
          code: '411623207000',
        },
        {
          name: '张明乡',
          pid: '8342',
          code: '411623208000',
        },
        {
          name: '郝岗镇',
          pid: '8342',
          code: '411623111',
        },
        {
          name: '张庄镇',
          pid: '8342',
          code: '411623210000',
        },
        {
          name: '汤庄乡',
          pid: '8342',
          code: '411623211000',
        },
      ],
    });
  },
};
