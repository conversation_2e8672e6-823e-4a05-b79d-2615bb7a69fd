/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import mockjs from 'mockjs';

const { mock } = mockjs;

export default {
  // 获取黑名单列表
  'POST /Api/YZ/CourierStation/blacklist': (req, res) => {
    const { page = 1 } = req.body;
    res.send(
      mock({
        code: 0,
        msg: '请求成功',
        data: {
          'list|15': [
            {
              id: '@id',
              league_id: '773',
              cm_id: '0',
              phone: '17196432627',
              remark: 'sfaf',
              create_at: '2020-02-25',
              source: '自行添加',
            },
          ],
          total: 60,
          size: 15,
          page,
        },
      }),
    );
  },
  // 添加黑名单
  'POST /Api/YZ/CourierStation/addBlack': (req, res) => {
    res.send({
      code: 0,
      msg: '添加成功',
      data: {},
    });
  },
  // 删除黑名单
  'POST /Api/YZ/CourierStation/delBlack': (req, res) => {
    res.send({
      code: 0,
      msg: '删除成功',
      data: {},
    });
  },
  'POST /Api/YZ/CourierStation/setSync': (req, res) => {
    res.send({
      code: 0,
      msg: '同步成功',
      data: {},
    });
  },
  'POST /Api/YZ/CourierStation/getSync': (req, res) => {
    res.send({
      code: 0,
      msg: '成功',
      data: '1',
    });
  },
  'POST /Api/YZ/CourierStation/importBlack': (req, res) => {
    res.send({
      code: '0',
      msg: '上传成功',
      data: {},
    });
  },
  // 获取黑名单扫描列表
  'POST /Api/YZ/CourierStation/getBlackPhoneRecord': (req, res) => {
    const list = [];

    Array.from({ length: 30 }).forEach((_, index) => {
      list.push({
        id: index,
        league_id: '7',
        branch_ids: '',
        cm_id: '2127890',
        inn_name: '虹桥一号驿站',
        waybill_no: '6466464646',
        brand: 'sht',
        black_phone: '15179185977',
        operator_phone: '15179185977',
        create_at: '2021-09-27 17:18:30',
      });
    });

    res.send({
      code: 0,
      msg: '请求成功',
      data: {
        list,
        total: 30,
        size: 10,
        page: 1,
      },
    });
  },
};
