/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { setStorageSync, getStorageSync } from './storage';

export const getLocationParams = () => {
  const { search } = window.location;
  return Object.fromEntries(new URLSearchParams(search));
};
/**
 *   http://localhost:8000/system/business/client?__host__=gp.chinapostcnps.com
 *  从路径中获取host 仅在本地有效  host会放在 sessionStorage 中
 * @returns
 */
const storageType = 'session';
const hostKey = '__host__';
const shopIdKey = '__shop_id__';

/**
 * 根据域名或shop_id获取配置
 * @param {Object} target - 配置对象
 * @returns {Object} - 匹配的配置
 */
export const getOptByHost = target => {
  if (process.env.NODE_ENV !== 'development') {
    // 生产环境：优先检查shop_id，然后检查域名
    const { shop_id } = getLocationParams();
    if (shop_id && !shop_id.includes('.')) {
      // shop_id存在且不包含点，转换为数字进行匹配
      const shopIdNum = parseInt(shop_id, 10);
      if (!isNaN(shopIdNum) && target[shopIdNum]) {
        return target[shopIdNum];
      }
    }
    // 回退到域名匹配
    return target[window.location.hostname];
  }

  // 开发环境：支持__host__和__shop_id__参数
  const { __host__, __shop_id__ } = getLocationParams();

  // 处理shop_id参数
  if (__shop_id__) {
    setStorageSync(shopIdKey, { [shopIdKey]: __shop_id__ }, storageType);
  }

  // 处理host参数
  if (__host__) {
    setStorageSync(hostKey, { [hostKey]: __host__ }, storageType);
  }

  const hostStorage = getStorageSync(hostKey, storageType) || {};
  const shopIdStorage = getStorageSync(shopIdKey, storageType) || {};
  const hostData = hostStorage.data ?? {};
  const shopIdData = shopIdStorage.data ?? {};

  // 优先使用shop_id匹配（如果存在且不包含点）
  const currentShopId = __shop_id__ || shopIdData[shopIdKey];
  if (currentShopId && !currentShopId.includes('.')) {
    const shopIdNum = parseInt(currentShopId, 10);
    if (!isNaN(shopIdNum) && target[shopIdNum]) {
      return target[shopIdNum];
    }
  }

  // 回退到host匹配
  return target[__host__] || target[hostData[hostKey]];
};
