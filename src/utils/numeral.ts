/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isNumber, isString } from 'lodash';
import numeral from 'numeral';
export function formatNumeral(val: string | number, formatter: string = '0,0.00') {
  if (isNumber(val) || isString(val)) {
    if (val === '') {
      return val;
    }
    return formatter ? numeral(val).format(formatter) : `${numeral(val).value() || ''}`;
  }
  return val;
}
