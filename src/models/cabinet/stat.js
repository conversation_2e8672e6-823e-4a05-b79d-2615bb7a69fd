/* eslint-disable array-callback-return */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { message } from 'antd';
import moment from 'moment';
import { isLegalData } from '@/utils/utils';
import {
  getAreaEcBrandStat,
  getAreaEcInOutStat,
  getAreaEcIncomeStat,
  getAreaEcSendStat,
} from '@/services/cabinet/star';

const noop = () => {};

export default {
  namespace: 'cabinet_stat',

  state: {
    list: null,
    pagination: null,
    brandList: null,
    summary: null,
    summary_detail: null,
    summary_brand: null,
    sumsms_detail: null,
    sumorder_detail: null,
    sumorder_brand: null,
    in_sum: null,
    out_sum: null,
    back_sum: null,
    sms_sum: null,
    order_sum: null,
    order_realnum_sum: null,
    all_total_detail: null,
    canvesList: [],
    orderSummary: [], // 1

    storageSummary: [],

    brandPieList: [],
    inBrandPieList: [],
    outBrandPieList: [],
    orderBrandPieList: [],

    dakOrEcIncome: [], // 111
  },

  effects: {
    // 111
    // 出库折线
    *storageSummary({ payload, then = noop }, { call, put }) {
      const { start, end } = payload;
      const diffDays = moment(end).diff(moment(start), 'day');
      if (start && end && diffDays >= 31) {
        message.error('跨度最多1个月');
        yield put({
          type: 'save',
          payload: {
            canvesList: [],
          },
        });
        then(isLegalData(data, []));
        return;
      }

      const params = {
        ...payload,
        ec_id: payload.cm_id,
      };
      if (start) {
        params.dateRange = [start, end];
      }

      const response = yield call(getAreaEcInOutStat, payload);
      if (!response) return;
      const { code, data, msg } = response;
      if (code == 0 && data) {
        yield put({
          type: 'save',
          payload: {
            canvesList: isLegalData(data, []),
          },
        });
        then(isLegalData(data, []));
      } else {
        message.error(msg);
      }
    },
    // 111
    // 品牌占比
    *brandPie({ payload }, { call, put }) {
      console.info('261=======>');
      const { start, end } = payload;

      const diffDays = moment(end).diff(moment(start), 'day');
      if (start && end && diffDays >= 31) {
        message.error('跨度最多1个月');
        yield put({
          type: 'save',
          payload: {
            inBrandPieList: [],
            outBrandPieList: [],
            orderBrandPieList: [],
          },
        });
        return;
      }
      const response = yield call(getAreaEcBrandStat, payload);
      yield put({
        type: 'save',
        payload: {
          ...response,
        },
      });
    },

    // 订单折线 111
    *orderSummary({ payload, then = noop }, { call, put }) {
      const { start, end } = payload;
      const diffDays = moment(end).diff(moment(start), 'day');
      if (start && end && diffDays >= 31) {
        message.error('跨度最多1个月');
        yield put({
          type: 'save',
          payload: {
            orderSummary: [],
          },
        });
        then([]);
        return;
      }
      const response = yield call(getAreaEcSendStat, payload);
      if (!response) return;
      const { code, data, msg } = response;
      if (code == 0 && data) {
        yield put({
          type: 'save',
          payload: {
            orderSummary: isLegalData(data, []),
          },
        });
        then(isLegalData(data, []));
      } else {
        message.error(msg);
      }
    },

    // 111
    *dakOrEcIncome({ payload, then = noop }, { call, put }) {
      console.info('349=======>');

      if (payload.cm_id == 'all') {
        return;
      }
      const { start, end } = payload;
      const diffDays = moment(end).diff(moment(start), 'day');
      if (start && end && diffDays >= 31) {
        message.error('跨度最多1个月');
        yield put({
          type: 'save',
          payload: {
            dakOrEcIncome: [],
          },
        });
        then([]);
        return;
      }
      const response = yield call(getAreaEcIncomeStat, payload);
      if (!response) return;
      const { code, data, msg } = response;
      if (code == 0 && data && Array.isArray(data)) {
        const dakOrEcIncomeList = data.map(item => ({
          ...item,
          date: item.day,
          cnt: +item.cnt,
        }));
        yield put({
          type: 'save',
          payload: {
            dakOrEcIncome: isLegalData(dakOrEcIncomeList, []),
          },
        });
        then(isLegalData(dakOrEcIncomeList, []));
      } else {
        message.error(msg);
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
