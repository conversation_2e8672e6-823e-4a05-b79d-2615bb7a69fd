/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getSubordinatePostStations, postStationsOnOff, getOpenBrands } from '@/services/dispat';
import { message } from 'antd';
import { isArray } from 'lodash';

export default {
  namespace: 'serviceDispat',

  state: {
    result: null,

    // 服务费下属驿站列表
    postStations: {
      list: null,
      pagination: null,
    },
  },

  effects: {
    // 服务费设置，获取下属驿站列表
    *getSubordinatePostStations({ payload, __dva_resolve, __dva_reject }, { call, put }) {
      const response = yield call(getSubordinatePostStations, { ...payload, cabinet: '1' });
      if (!response) return;
      const { code, data = {}, msg } = response;
      const { list = [], total, page, pageSize } = data;

      if (code == 0) {
        yield put({
          type: 'save',
          payload: {
            postStations: {
              list: isArray(list) ? list : [],
              pagination: {
                total,
                current: page,
                pageSize: 20,
                showQuickJumper: false,
                showSizeChanger: false,
              },
            },
          },
        });
        __dva_resolve(response);
      } else {
        __dva_reject();
        message.error(msg);
      }
    },
    // 服务费设置，下属驿站开启关闭
    *postStationsOnOff({ payload, __dva_resolve, __dva_reject }, { call, put }) {
      const response = yield call(postStationsOnOff, payload);
      if (!response) return;
      const { code, msg } = response;

      if (code == 0) {
        message.success(msg);
        __dva_resolve(response);
      } else {
        __dva_reject();
        message.error(msg);
      }
    },
    // 当前驿站开通的品牌
    *getOpenBrands({ payload, __dva_resolve, __dva_reject }, { call, put }) {
      const response = yield call(getOpenBrands, payload);
      if (!response) return;
      const { code, msg } = response;
      if (code == 0) {
        __dva_resolve(response);
      } else {
        __dva_reject();
        message.error(msg);
      }
    },
  },

  reducers: {
    save(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
