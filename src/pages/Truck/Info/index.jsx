/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { Component } from 'react';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import TruckInfo from '@/components/Truck/info';
import { Col, Icon, Row } from 'antd';
import Guide from '@/components/Truck/components/guide';

export default class Info extends Component {
  constructor(props) {
    super(props);
    this.state = {};
  }

  render() {
    return (
      <PageHeaderLayout
        title={
          <Row type="flex" align="middle">
            <Col>车辆信息</Col>
            <Col>
              <Guide trigger={<Icon type="question-circle" />} />
            </Col>
          </Row>
        }
      >
        <Guide />
        <TruckInfo />
      </PageHeaderLayout>
    );
  }
}
