/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useRequest, useSetState } from 'ahooks';
import { message } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { smsCharging, smsChargingCheck } from '@/services/account';
import { sleepPromise } from '@/components/_pages/Allocation/batchpatch/_utils/useOrderUpload';

export function useFinanceSms(props) {
  const { info, refresh = () => {} } = props;
  const { sms_price = 0, sms_count_info } = info ?? {};
  const [current, setCurrent] = useState(0);

  const free_count = sms_count_info?.free_count ?? 0;

  const shouldShowComponent = sms_price > 0;

  const SMS_COLUMNS = useMemo(
    () => {
      const PRICE_1 = 5000;
      const PRICE_2 = 10000;

      const price1 = (sms_price * PRICE_1).toFixed(2) * 1;
      const price2 = (sms_price * PRICE_2).toFixed(2) * 1;
      const showOrigin = sms_price < 0.05;

      return [
        {
          value: PRICE_1,
          price: price1,
          originPrice: showOrigin ? '250' : '',
        },
        {
          value: PRICE_2,
          price: price2,
          originPrice: showOrigin ? '500' : '',
        },
      ];
    },
    [sms_price],
  );

  const [pageData, updatePageData] = useSetState({
    out_trade_no: '',
    qrcode_url: '',
  });

  const { run: checkSms, cancel: cancelCheck } = useRequest(smsChargingCheck, {
    manual: true,
    pollingInterval: 1000,
    onSuccess: res => {
      const { code, data, msg } = res;
      const isSuccess = String(code) === '0';
      const { status } = data ?? {};
      if (isSuccess) {
        if (status === '1') {
          message.success('支付成功');
          refresh('refresh');
          onCloseModal();
        }
      } else {
        message.error(msg);
        onCloseModal();
      }
    },
  });

  const onGetPayQrcode = async () => {
    try {
      const item = SMS_COLUMNS[current];
      if (!item) {
        message.error('请先选择短信购买条数');
        return;
      }
      const { value, price } = item;
      const { out_trade_no, qrcode_url } = await smsCharging({
        count: value,
      });
      if (out_trade_no && qrcode_url) {
        updatePageData({ qrcode_url, out_trade_no, price });
        await sleepPromise(1000);
        checkSms({ out_trade_no });
      }
    } catch (error) {
      message.error('获取支付信息失败');
      onCloseModal();
    }
  };

  const onCloseModal = () => {
    updatePageData({ qrcode_url: '' });
    cancelCheck?.();
  };

  // 切换短信购买卡片
  const onSwitchSmsCard = c => {
    console.log('onSwitchSmsCard', c);
    setCurrent(c);
  };

  useEffect(() => {
    return () => {
      cancelCheck?.();
      updatePageData({ qrcode_url: '' });
    };
  }, []);

  return {
    current,
    SMS_COLUMNS,
    free_count,
    shouldShowComponent,
    pageData,
    onGetPayQrcode,
    onCloseModal,
    onSwitchSmsCard,
  };
}
