@import '~antd/lib/style/themes/default.less';

.item {
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  border: 1px solid #e6e6e6;
  border-radius: 5px;
  flex-direction: column;
}

.image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  flex-direction: column;
  gap: 10px;
}

.accountCard {
  height: 180px;
  position: relative;
  &Footer {
    position: absolute;
    right: 0;
    bottom: 0;

    &Item {
      height: 100px;
      background-color: @select-item-selected-bg;
      border-radius: @border-radius-base;
      padding: @padding-xs @padding-sm;
      cursor: pointer;
      text-align: center;
      align-items: center;
      border: 1px solid @select-item-selected-bg;
      &:hover {
        border-color: @select-item-active-bg;
      }
      &Active {
        background-color: @select-item-active-bg;
      }
    }
  }
}
