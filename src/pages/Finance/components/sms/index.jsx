/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react/jsx-no-bind */
import { <PERSON><PERSON>, Card, Col, Divider, Modal, Row, Typography } from 'antd';
import React from 'react';
import KbTypographyText from '@/components/KbTypographyText';
import Space from '@/components/Space';
import styles from './index.less';
import { useFinanceSms } from './useFinanceSms';
import { QRCodeCanvas } from 'qrcode.react';
import NumberDelay from '@/components/NumberDelay';
import classNames from 'classnames';

const numberFormat = '0,0';
const { Title, Text } = Typography;


const FinanceSms = props => {
  const {
    SMS_COLUMNS,
    free_count,
    shouldShowComponent,
    pageData,
    current,
    onSwitchSmsCard,
    onGetPayQrcode,
    onCloseModal,
  } = useFinanceSms(props);

  const { qrcode_url, price } = pageData;

  return (
    <>
      {shouldShowComponent && (
        <Card>
          <div className={styles.accountCard}>
            <Space direction="vertical">
              <span>免费短信条数（条）</span>
              <Title level={3} style={{ marginBottom: 0 }}>
                {free_count ? (
                  <NumberDelay format={numberFormat} number={free_count} />
                ) : (
                  '0'
                )}
              </Title>
            </Space>

            <div className={styles.accountCardFooter}>
              <Space direction="vertical" style={{ alignItems: 'flex-end' }}>
                <Space>
                  {
                    SMS_COLUMNS.map((item, index) => (
                      <div
                        onClick={() => onSwitchSmsCard(index)}
                        key={item.value}
                      >
                        <Space
                          direction='vertical'
                          className={
                            classNames(
                              styles.accountCardFooterItem,
                              {
                                [styles.accountCardFooterItemActive]: current === index
                              }
                            )
                          }
                        >
                          <Text>{item.value}条</Text>
                          <Text type='danger' strong> ¥{item.price}</Text>
                          {!!item.originPrice && <Text type='secondary' style={{ fontSize: 12 }} delete>原价{item.originPrice}元</Text>}
                        </Space>
                      </div>
                    ))
                  }
                </Space>
                <Button type="primary" disabled={!SMS_COLUMNS[current]} onClick={onGetPayQrcode}>
                  购买
                </Button>
              </Space>
            </div>
          </div>
        </Card>
      )}

      <Modal
        title="短信充值"
        centered
        visible={!!qrcode_url}
        onCancel={onCloseModal}
        footer={null}
        width={350}
      >
        <div className={styles.image}>
          <KbTypographyText type='secondary'>请使用支付宝支付</KbTypographyText>
          <KbTypographyText size="20" cname={{ color: 'red' }}>
            ¥{price}
          </KbTypographyText>
          <QRCodeCanvas
            value={qrcode_url}
            size={250}
          // bgColor="#ffffff"
          // fgColor="#000000"
          // level="L"
          // includeMargin={false}
          />
        </div>
      </Modal>
    </>
  );
};

export default FinanceSms;
