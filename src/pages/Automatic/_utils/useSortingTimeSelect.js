/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useSetState } from 'ahooks';
import { message } from 'antd';
import moment from 'moment';

// 时间类型常量
const TIME_TYPE = {
  IMMEDIATE: '0', // 即时
  DELAY_MINUTE: '1', // 延时（分钟）
  DELAY_NEXT_DAY: '2', // 延时（次日）
  SCHEDULED: '3', // 定时
};

// 时间格式化工具
const formatTime = (time, format = 'HH:mm') => {
  if (!time) return null;
  return moment.isMoment(time) ? time.format(format) : time;
};

export const SORTING_RADIO_TYPE_OPTIONS = [
  { label: '即时', value: TIME_TYPE.IMMEDIATE },
  { label: '延时', value: TIME_TYPE.DELAY_MINUTE },
  { label: '定时', value: TIME_TYPE.SCHEDULED },
];

export const SORTING_TIME_TYPE_OPTIONS = [
  { label: '分钟', value: TIME_TYPE.DELAY_MINUTE },
  { label: '次日', value: TIME_TYPE.DELAY_NEXT_DAY },
];

// // 设置数据类型
// const value = {
//   type: 3, // 0=>即时 1=>延时(单位为分钟) 2=>延时(单位为次日) 3=>定时
//   time: '10:00', // 10:00 具体时间   type=1时为具体分钟数
//   sTime: '05:00', // type=3 时有值
//   eTime: '08:00', // type=3 时有值
// };

/**
 * 时间选择自定义Hook
 * @param {Object} props - 组件属性
 * @param {TimeConfig} props.value - 当前时间配置
 * @param {boolean} props.disabled - 是否禁用
 * @param {Function} props.onChange - 变更回调
 */
export function useSortingTimeSelect(props) {
  const { value: formValue, disabled, onChange } = props;

  // 页面状态管理
  const [pageData, updatePageData] = useSetState({
    visible: false,
    selectedType: '', // 当前选择的主类型
    delaySubType: '', // 延时子类型（分钟/次日）
    mainTime: null, // 主要时间值
    startTime: null, // 定时开始时间
    endTime: null, // 定时结束时间
  });

  /**
   * 将表单值转换为弹窗状态数据
   * @param {TimeConfig} value - 表单原始值
   * @returns {Object} 弹窗状态数据
   */
  const mapFormToModalState = value => {
    const { type = TIME_TYPE.IMMEDIATE, time = '', sTime = '', eTime = '' } = value || {};
    const baseDate = moment().format('YYYY-MM-DD');

    // 处理延时类型的特殊映射
    const isDelayType = [TIME_TYPE.DELAY_MINUTE, TIME_TYPE.DELAY_NEXT_DAY].includes(type);

    return {
      selectedType: isDelayType ? TIME_TYPE.DELAY_MINUTE : type,
      delaySubType: isDelayType ? type : TIME_TYPE.DELAY_MINUTE,
      mainTime: time ? (['2', '3'].includes(type) ? moment(`${baseDate} ${time}`) : time) : null,
      startTime: sTime ? moment(`${baseDate} ${sTime}`) : null,
      endTime: eTime ? moment(`${baseDate} ${eTime}`) : null,
    };
  };

  /**
   * 将弹窗状态转换为表单数据
   * @returns {TimeConfig} 表单数据
   */
  const mapModalToFormData = () => {
    const { selectedType, delaySubType, mainTime, startTime, endTime } = pageData;

    switch (selectedType) {
      case TIME_TYPE.IMMEDIATE:
        return { type: selectedType, time: null };

      case TIME_TYPE.DELAY_MINUTE:
        return {
          type: delaySubType,
          time: delaySubType === TIME_TYPE.DELAY_MINUTE ? mainTime : formatTime(mainTime),
        };

      case TIME_TYPE.SCHEDULED:
        return {
          type: selectedType,
          time: formatTime(mainTime),
          sTime: formatTime(startTime),
          eTime: formatTime(endTime),
        };

      default:
        return {};
    }
  };

  /**
   * 弹窗显示状态变更处理
   * @param {boolean} visible - 是否显示弹窗
   */
  const handleVisibleChange = visible => {
    if (disabled) return;

    updatePageData({
      visible,
      ...(visible ? mapFormToModalState(formValue) : {}),
    });
  };

  /**
   * 主类型选择变更
   * @param {React.ChangeEvent<HTMLInputElement>} event - 单选按钮事件
   */
  const handleTypeChange = event => {
    const { value } = event.target;
    updatePageData({
      selectedType: value,
      delaySubType: value === TIME_TYPE.DELAY_MINUTE ? TIME_TYPE.DELAY_MINUTE : '',
      mainTime: null,
      startTime: null,
      endTime: null,
    });
  };

  /**
   * 延时子类型变更
   * @param {string} subType - 子类型值
   */
  const handleSubTypeChange = subType => {
    updatePageData({
      delaySubType: subType,
      mainTime: null,
    });
  };

  /**
   * 时间值变更处理
   * @param {string} field - 时间字段名
   * @param {moment.Moment} value - 时间值
   */
  const handleTimeChange = (field, value) => {
    updatePageData({ [field]: value });
  };

  /**
   * 验证定时配置参数
   * @returns {Promise<void>}
   */
  const validateParameters = async () => {
    const { selectedType, delaySubType, mainTime, startTime, endTime } = pageData;
    let errorMessage = '';

    if (selectedType === TIME_TYPE.DELAY_MINUTE) {
      if (!delaySubType) errorMessage = '请选择延时模式';
      else if (!mainTime) errorMessage = '请设置延时时间';
    } else if (selectedType === TIME_TYPE.SCHEDULED) {
      if (!mainTime || !startTime || !endTime) {
        errorMessage = '请完善所有时间设置';
      } else {
        const start = moment(startTime);
        const end = moment(endTime);
        const main = moment(mainTime);

        if (end.isBefore(start)) {
          errorMessage = '结束时间不能早于开始时间';
        } else if (main.isBefore(end)) {
          errorMessage = '执行时间不能早于结束时间';
        }
      }
    }

    if (errorMessage) {
      message.warning(errorMessage);
      throw new Error(errorMessage);
    }
  };

  /**
   * 完成配置处理
   */
  const handleConfirm = async () => {
    try {
      await validateParameters();
      onChange(mapModalToFormData());
      handleVisibleChange(false);
    } catch (err) {
      console.info('======>', err);
    }
  };

  return {
    modalState: pageData,
    onVisibleChange: handleVisibleChange,
    onTypeChange: handleTypeChange,
    onSubTypeChange: handleSubTypeChange,
    onTimeChange: handleTimeChange,
    onConfirm: handleConfirm,
  };
}
