/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { delDefaultOrderUser, getDefaultOrderUser, setDefaultOrderUser } from '@/services/api';
import { useRequest, useSetState } from 'ahooks';
import { message } from 'antd';

const OPTIONS = [
  {
    label: '快递员',
    value: 's',
  },
  {
    label: '快宝驿站',
    value: 'yz',
  },
];

export function useTodoWrapAutoModal() {
  const [pageData, updatePageData] = useSetState({
    visible: false,
    isBind: false,
    user_type: 's',
    phone: '',
  });

  const { refresh } = useRequest(getDefaultOrderUser, {
    onSuccess: data => {
      updatePageData(val => ({
        user_type: data?.user_type || val.user_type,
        phone: data.phone || '',
        isBind: !!data?.user_type,
      }));
    },
  });

  const onChangeModalVisible = () => {
    return new Promise(resolve => {
      updatePageData(val => ({
        visible: !val.visible,
      }));
      resolve();
    });
  };

  const onSelectChange = e => {
    updatePageData({
      user_type: e,
    });
  };

  const onInputChange = e => {
    updatePageData({
      phone: e.target.value,
    });
  };

  const onCheckParams = () => {
    return new Promise((resolve, reject) => {
      const { user_type, phone } = pageData;
      let msg = '';
      if (!user_type) {
        msg = '请选择账号类型';
      }
      if (!phone) {
        msg = '请输入帐号';
      } else {
        const reg = /^1[2-9]{1}[0-9]{1}\d{8}$/;
        if (!reg.test(phone)) {
          msg = '手机号格式不正确';
        }
      }
      if (msg) {
        message.error(msg);
        reject();
        return;
      }
      resolve({
        user_type,
        phone,
      });
    });
  };

  const onCloseAuth = async () => {
    const status = await delDefaultOrderUser();
    if (status) {
      await onChangeModalVisible();
      refresh();
    }
  };

  const onFinish = async () => {
    const params = await onCheckParams();
    const status = await setDefaultOrderUser(params);
    if (status) {
      await onChangeModalVisible();
      refresh();
    }
  };

  return {
    OPTIONS,
    pageData,
    onSelectChange,
    onInputChange,
    onFinish,
    onChangeModalVisible,
    onCloseAuth,
  };
}
