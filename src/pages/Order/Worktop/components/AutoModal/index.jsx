/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, Col, Input, Modal, Row, Select } from 'antd';
import React from 'react';
import { useHistory } from 'dva';
import { useTodoWrapAutoModal } from './useTodoWrapAutoModal';
import KbTypographyText from '@/components/KbTypographyText';

const TodoWrapAutoModal = () => {
  const history = useHistory();

  const {
    OPTIONS,
    pageData,
    onSelectChange,
    onInputChange,
    onFinish,
    onChangeModalVisible,
    onCloseAuth,
  } = useTodoWrapAutoModal();

  const { user_type, phone, isBind, visible } = pageData;

  const input_placeholder = user_type == 's' ? '请输入快递员APP账号' : '请输入快宝驿站账号';

  const onNavigator = async () => {
    await onChangeModalVisible();
    history.push('/system/business/operator');
  };

  return (
    <>
      <a onClick={onChangeModalVisible}> {isBind ? '已开启自动分配' : '自动分配'}</a>
      <Modal
        visible={visible}
        title="自动分配"
        width={600}
        centered
        footer={[
          isBind && (
            <Button key="close" onClick={onCloseAuth}>
              关闭自动分配
            </Button>
          ),
          <Button key="cancel" onClick={onChangeModalVisible}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={onFinish}>
            确定
          </Button>,
        ]}
      >
        <KbTypographyText color="black" size="13" block>
          客户通过(微快递/快宝驿站/定制公众号及小程序)下单到默认下单对象(快递员或快宝驿站账号)，自动按地址三段码(基础数据-业务员处配置)分配给三段码对应的快递员账号处理订单
        </KbTypographyText>

        <Row gutter={10} align="middle" style={{ margin: '20px 0' }}>
          <Col span={5}>默认下单对象：</Col>
          <Col span={6}>
            <Select
              style={{ width: '100%' }}
              placeholder="请选择"
              value={user_type}
              onChange={onSelectChange}
            >
              {OPTIONS.map(item => (
                <Select.Option value={item.value}>{item.label}</Select.Option>
              ))}
            </Select>
          </Col>
          <Col span={10}>
            <Input placeholder={input_placeholder} value={phone} onChange={onInputChange} />
          </Col>
        </Row>
        <div>
          <KbTypographyText color="light" size="12" block>
            注1:请在“基础数据-业务员”处给参与处理订单的快递员配置三段码，
            <a onClick={onNavigator}>点此前往配置</a>
          </KbTypographyText>
          <KbTypographyText color="light" size="12" block>
            注2:未匹配到三段码的订单会分配给默认下单对象{' '}
          </KbTypographyText>

          <KbTypographyText color="light" size="12" block>
            注3:详情可咨询客服微信号：
            <KbTypographyText color="link" copyable>
              kbyz12
            </KbTypographyText>
          </KbTypographyText>
        </div>
      </Modal>
    </>
  );
};

export default TodoWrapAutoModal;
