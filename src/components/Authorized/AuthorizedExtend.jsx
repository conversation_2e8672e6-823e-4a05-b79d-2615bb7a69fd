/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useSelector } from 'dva';
import { checkAuthority } from '@/utils/authority';

export const getAuthorityRoutes = (routes, authority, key) => {
  return routes.reduce((acc, item) => {
    if (checkAuthority(authority, item, key) && !item.hideInMenu) {
      const processedItem = { ...item };
      if (item.children) {
        processedItem.children = getAuthorityRoutes(item.children, authority, key);
        if (processedItem.children.length > 0) {
          acc.push(processedItem);
        }
      } else {
        acc.push(processedItem);
      }
    }
    return acc;
  }, []);
};

const getPermissionIdByRoutes = (routes, currentPath = window.location.pathname) => {
  // eslint-disable-next-line no-restricted-syntax
  for (const item of routes) {
    if (item.path === currentPath) {
      return item.id;
    }
    if (item.children) {
      const childId = getPermissionIdByRoutes(item.children, currentPath);
      if (childId) {
        return childId;
      }
    }
  }
  return '';
};

// 通用的权限检查逻辑
const checkPermission = (authorityList, menuData, authority, options, auth, patchId) => {
  // 如果没有权限列表，默认最高权限
  if (!authorityList || !authorityList.length) {
    return true;
  }

  const routes = getAuthorityRoutes(menuData, authority, options?.key);

  if (patchId) {
    const routId = getPermissionIdByRoutes(routes);
    if (!routId) {
      return true; // 如果获取不到路由ID，说明该路由没有权限控制
    }

    // 检查是否存在以当前路由ID开头的权限
    const hasRoutePermissions = authorityList.some(item => item.startsWith(`${routId}-`));

    // 如果不存在该路由相关权限，则默认有权限
    if (!hasRoutePermissions) {
      return true;
    }
    // 存在该路由相关权限，则需要严格校验
    const permissionId = `${routId}-${auth}`;
    return authorityList.includes(permissionId);
  }

  // 不需要路由ID的情况，直接判断权限
  return authorityList.includes(auth);
};

/**
 * 权限组件
 * @param {*} children
 * @param {string} auth 显示所需id
 * @param {boolean} patchId 是否需要从路由id中补齐
 * @returns
 */
const AuthorizedExtend = ({ children, auth, patchId }) => {
  const { currentUser = {} } = useSelector(state => state.user);
  const { menuData = [], authority } = useSelector(state => state.global);
  const { options = {} } = useSelector(state => state.setting);
  const { authorityList = [] } = currentUser;

  const hasPermission = checkPermission(authorityList, menuData, authority, options, auth, patchId);

  return hasPermission ? children : null;
};

export default AuthorizedExtend;

/**
 * 权限组件
 * @param {object} params
 * @param {string} params.auth 显示所需id
 * @param {boolean} [params.patchId] 是否需要从路由id中补齐
 * @returns
 */
export const checkAuthorized = ({ auth, patchId }) => {
  const store = window.g_app._store.getState();
  const currentUser = store.user.currentUser || {};
  const { menuData = [], authority } = store.global;
  const { options = {} } = store.setting;
  const { authorityList = [] } = currentUser;

  return checkPermission(authorityList, menuData, authority, options, auth, patchId);
};
