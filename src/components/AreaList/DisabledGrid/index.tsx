/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, message } from "antd"
import React from "react"
import styles from './index.less'
import classNames from "classnames"
import { setEcForbidDeliver } from "@/services/api"
import { ModalForm } from "@/components/AdaptForm"
const DisabledGrid = ({ style, className, item, onSuccess, disabled }) => {
  const { ec_forbid_use, forbid_deliver, inn_name, ec_id } = item
  // 1:允许加盟商设置禁投 2:不允许设置禁投
  const isShow = ec_forbid_use == '1'
  // 1禁止2不禁止
  const isDisabled = forbid_deliver == '1'
  if (!isShow) return null
  const trigger = (<span className={isDisabled ? '' : styles.disabledGrid}>
    <Button className={classNames(className, isDisabled ? '' : styles.btn_admin)} style={style} disabled={disabled} >
      {isDisabled ? '允许投递' : '禁止投递'}
    </Button>
  </span>)
  const onFinish = async () => {
    const { code, msg } = await setEcForbidDeliver({ ec_id, status: isDisabled ? '2' : '1' })
    if (code == 0) {
      message.success(msg)
      onSuccess?.()
      return true
    } else {
      message.warning(msg)
      return false
    }
  }

  return <>
    <ModalForm title={inn_name} trigger={trigger} onFinish={onFinish}>
      {isDisabled ? '确定解除禁止投递？' : '确定禁止投递？'}
    </ModalForm>

  </>
}

export default DisabledGrid
