/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useRef, useState } from 'react';
import { Button, message } from 'antd';
import { getScreenLoginUser } from '@/services/api';
import { useDebounceFn } from 'ahooks';
import { patchUrlWithQuery } from '@/utils/navigator';
import { simulateNavigator } from '@/utils/utils';

const CheckVideo = props => {
  const { data, className } = props;

  const [loading, setLoading] = useState(false);
  const ref = useRef({ linkData: null });

  // 查看监控（驿站视频监控）
  const canVideoMonitor = `${data.open_video}` === '1';
  // 点击查看监控

  const { run: runDebounce } = useDebounceFn(
    async () => {
      setLoading(true);
      try {
        const { linkData } = ref.current;
        const { code, msg, data: resData } = linkData
          ? { data: { link_data: linkData } }
          : await getScreenLoginUser({ type: 'video' });
        const { link_data } = resData || {};
        const { link, password } = link_data || {};
        if (link && password) {
          ref.current.linkData = link_data;
          simulateNavigator(patchUrlWithQuery(link, { cm_id: data.cm_id, __password__: password }));
        } else {
          ref.current.linkData = null;
          if (code > 0 && msg) {
            throw new Error(msg);
          }
        }
      } catch (error) {
        message.error(error.message);
      }
      setLoading(false);
    },
    { wait: 300, trailing: false, leading: true },
  );

  const handleClick = async () => runDebounce();

  return (
    <>
      {canVideoMonitor ? (
        <Button loading={loading} className={className} onClick={handleClick}>
          查看监控
        </Button>
      ) : null}
    </>
  );
};

export default CheckVideo;
