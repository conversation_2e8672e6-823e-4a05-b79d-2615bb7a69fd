/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import '~antd/lib/style/themes/default.less';

.main{
  :global{
    .ant-list-item{
      padding: 0;
    }
  }
}
.information_title {
  margin-bottom: 7px;
  .ft_tow {
    display: inline-block;
    color: #333;
    font-size: 20px;
    i {
      margin-right: 10px;
      color: #f80;
      font-style: normal;
    }
  }
  :global {
    div {
      display: inline-block;
      float: right;
    }
    .ant-btn{
      background: #f2fbff !important;
      border: #ceefff 1px solid !important;
    }
  }
}
.information {
  position: relative;
  width: 100%;
  overflow: hidden;
  color: #333;
  font-size: 16px;
  .qrcode{
    position: absolute;
    top: 0;
    right: 0;
    width: 120px;
    height: 120px;
    margin-top: 12px;
    margin-right: 12px;
    img{
      display: inline-block;
      width: 100%;
      height: 100%;
    }
  }
}
.mt_b_10 {
  margin: 10px 0;
}
.mt_b_20 {
  margin: 20px 0;
}
.mr_5 {
  margin-right: 5px;
}
.ml_5 {
  margin-left: 5px;
}
.ml_r_20 {
  margin: 0 20px;
  color:#ccc;
}
.col_f35216 {
  color: #f35216;
}
.col_1890ff {
  color: #1890ff;
}
.bag_bor {
  display: inline-block;
  width: 18px;
  height: 18px;
  margin-right: 10px;
  color: #fff;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  background: #1890ff;
  border-radius: 5px;
}
.bag_bor_storage{
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;;
  color: #1890ff;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
}
.bag_bor_area{
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  color: #1890ff;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
}
.bag_bor_time{
  display: inline-block;
  width: 20px;
  height: 20px;
  margin-right: 10px;
  color: #1890ff;
  font-size: 20px;
  line-height: 20px;
  text-align: center;
}
.btn_admin {
  margin-left: 20px;
  color: #1890ff;
}
.modal_tip {
  padding: 20px 0;
  color: #333;
  font-size: 16px;
}
.down_conent {
  padding: 30px;
}
.join{
  display: inline-block;
}
