/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { message, Typography } from 'antd';
// import type { TextProps } from 'antd/lib/typography/Text';
import copy from 'copy-to-clipboard';
import React from 'react';

const { Text } = Typography;

// type ColorType = 'black' | 'dark' | 'light' | 'them' | 'link';

// interface IndexType extends TextProps {
//   color?: ColorType;
//   size?: string;
//   cname?: React.CSSProperties;
//   block?: boolean;
//   isPointer?: boolean; // 是否需要鼠标样式
//   [name: string]: any;
// }

const KbTypographyText = props => {
  const defaultFontSize = '14';

  const { color = 'dark', size = defaultFontSize, cname = {}, block, isPointer, ...rest } = props;
  const colorType = {
    black: '#333',
    dark: '#666',
    light: '#999',
    them: '#ffa21c',
    link: '#1890ff',
  };

  // 如果有type就使用type类型
  const colorStyle = rest.type
    ? {}
    : {
        color: colorType[color],
      };

  const onClickHandle = () => {
    if (rest.copyable) {
      const str = props.children;
      if (copy(str)) {
        message.success('复制成功');
      }
    }
  };

  return (
    <Text
      style={{
        ...(block ? { display: 'block' } : {}),
        fontSize: size + 'px',
        ...(isPointer ? { cursor: 'pointer' } : {}),
        ...colorStyle,
        ...cname,
      }}
      onClick={onClickHandle}
      {...rest}
    >
      {props.children}
    </Text>
  );
};
export default KbTypographyText;
