/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import '~antd/lib/style/themes/default.less';

.radar {
  .legend {
    margin-top: 16px;
    .legendItem {
      position: relative;
      color: @text-color-secondary;
      line-height: 22px;
      text-align: center;
      cursor: pointer;
      p {
        margin: 0;
      }
      h6 {
        margin-top: 4px;
        margin-bottom: 0;
        padding-left: 16px;
        color: @heading-color;
        font-size: 24px;
        line-height: 32px;
      }
      &::after {
        position: absolute;
        top: 8px;
        right: 0;
        width: 1px;
        height: 40px;
        background-color: @border-color-split;
        content: '';
      }
    }
    > :last-child .legendItem::after {
      display: none;
    }
    .dot {
      position: relative;
      top: -1px;
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 6px;
      border-radius: 6px;
    }
  }
}
