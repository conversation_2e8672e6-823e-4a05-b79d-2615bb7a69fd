/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useRef, useState } from 'react';
import { message } from 'antd';
import { useDebounceFn } from 'ahooks';
import { check } from '../_utils';

export const selectionOptions = [
  { index: '0', name: '单号', key: '1', reg: 'waybillNum' },
  { index: '1', name: '取件码', key: '2', reg: 'bh' },
  { index: '2', name: '手机号', key: '3', reg: 'phone' },
];

export const useSuperSearch = props => {
  const { isPage } = props;
  const [currentIndex, setCurrentIndex] = useState('0');
  const [visible, setVisible] = useState(false);
  const [searchValue, setSearchValue] = useState(null);
  const searchResultRef = useRef(null);

  const handleSelectChange = v => {
    setCurrentIndex(v);
  };

  const { run: handleSearch } = useDebounceFn(
    async value => {
      value = value.trim();
      if (!value.length) return;
      const { key, reg } = selectionOptions[currentIndex];
      const { code, msg } = check({
        form: {
          [key]: {
            reg,
            value,
          },
        },
      });
      if (code > 0) {
        message.error(msg);
        return;
      }
      setVisible(true);
      setTimeout(() => {
        // 由于modal原因，先渲染searchResult
        searchResultRef.current
          ?.getResult({
            type: key,
            keyword: value,
          })
          .catch(() => {
            setVisible(false);
          });
      }, 300);
    },
    {
      wait: 500,
      leading: true,
      trailing: false,
    },
  );

  useEffect(() => {
    if (!isPage) return;
    const query = new URLSearchParams(window.location.search);
    const waybill = query.get('waybill');
    if (waybill) {
      setCurrentIndex('0');
      setSearchValue(waybill);
      handleSearch(waybill);
    }
  }, []);

  return {
    currentIndex,
    handleSelectChange,
    handleSearch,
    visible,
    setVisible,
    searchResultRef,
    type: selectionOptions[currentIndex].key,
    searchValue,
    setSearchValue,
  };
};
