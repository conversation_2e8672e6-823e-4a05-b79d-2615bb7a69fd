/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Button, Col, Input, Modal, Row } from 'antd';
import classNames from 'classnames';
import StandardSelect from '../Select';
import { selectionOptions, useSuperSearch } from './utils';
import SearchResult from './result';
import styles from '@/pages/Post/Search.less';

const SuperSearch = props => {
  const { isPage } = props;
  const {
    loading,
    currentIndex,
    handleSelectChange,
    handleSearch,
    visible,
    setVisible,
    searchResultRef,
    type,
    searchValue,
    setSearchValue,
  } = useSuperSearch(props);

  return (
    <>
      <Row
        type="flex"
        align="middle"
        justify="center"
        className={classNames({
          [styles.searchBar]: isPage,
        })}
      >
        <Col span={24} className={styles.searchBarCol}>
          <Input.Search
            style={{ maxWidth: isPage ? 600 : 450 }}
            allowClear
            maxLength={40}
            placeholder={`请输入${selectionOptions[+currentIndex].name}`}
            addonBefore={
              <StandardSelect
                style={{ width: 80 }}
                onChange={handleSelectChange}
                value={currentIndex}
                kkey="index"
                options={selectionOptions}
              />
            }
            enterButton={
              <Button type="primary" loading={loading} icon="search">
                查询
              </Button>
            }
            onSearch={handleSearch}
            value={searchValue}
            onChange={e => setSearchValue(e.target.value)}
          />
        </Col>
      </Row>
      {isPage ? (
        <SearchResult type={type} ref={searchResultRef} />
      ) : (
        <Modal
          centered
          title="超级搜索"
          width="80vw"
          visible={visible}
          onCancel={() => setVisible(false)}
          onOk={() => setVisible(false)}
          destroyOnClose
          footer={null}
        >
          <div className={styles.searchResultModalContent}>
            <SearchResult type={type} ref={searchResultRef} closeModal={() => setVisible(false)} />
          </div>
        </Modal>
      )}
    </>
  );
};

export default SuperSearch;
