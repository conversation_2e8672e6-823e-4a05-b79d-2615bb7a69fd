/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useMemo, useState } from 'react';
import { Modal, Button } from 'antd';
import styles from './detail.less';

const DetailModal = ({ data = {}, trigger }) => {
  const [visible, setVisible] = useState(false);
  const btn = trigger ? (
    React.cloneElement(trigger, {
      onClick: () => {
        setVisible(true);
      },
    })
  ) : (
    <Button type="link" size="small" onClick={() => setVisible(true)}>
      详情
    </Button>
  );

  const list = useMemo(
    () => {
      return [
        {
          label: '分拣模式',
          key: 'a',
          value: data.a,
          render: (
            <>
              {data.a} | {data.b} | {data.c}
            </>
          ),
        },
        { label: '使用段码', key: 'b', value: data.b },
        { label: '地址关键字', key: 't', value: data.t },
        { label: '分配格口', key: 'c', value: data.c },
        { label: '实际格口', key: 'd', value: data.d },
        { label: '是否准确落格', key: 'e', value: data.e },
        { label: '识别状态', key: 'f', value: data.f },
        { label: '分拣模式', key: 'g', value: data.g },
        { label: '未用段码', key: 's', value: data.s, hide: true },
      ].filter(item => !item.hide);
    },
    [data],
  );

  return (
    <>
      {btn}
      <Modal
        title="详情"
        visible={visible}
        onCancel={() => setVisible(false)}
        footer={[
          <Button key="ok" type="primary" onClick={() => setVisible(false)}>
            知道了
          </Button>,
        ]}
        width={500}
        destroyOnClose
      >
        <div className={styles.detailContent}>
          {list.map(item => (
            <div className={styles.detailItem} key={item.key}>
              <span className={styles.label}>{item.label}:</span>
              <span className={styles.value}>{item.render || item.value}</span>
            </div>
          ))}
        </div>
      </Modal>
    </>
  );
};

export default DetailModal;
