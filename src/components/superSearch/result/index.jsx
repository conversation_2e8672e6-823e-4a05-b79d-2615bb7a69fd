/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { forwardRef } from 'react';
import ExpressModal from '@/components/ExpressModal';
import styles from '@/pages/Post/Search.less';
import { Avatar, Card, Divider, Row, Col, Spin, Table, Empty } from 'antd';
import classNames from 'classnames';
import { QRCodeCanvas } from 'qrcode.react';
import { gpColumns, sortingColumns, yzColumns } from './utils';
import { useSearchResult } from './utils';

const SearchResult = forwardRef((props, ref) => {
  const {
    result,
    loading,
    getting,
    showExpressModal,
    showDetail,
    expressInfo,
    setExpressInfo,
    activeId,
  } = useSearchResult(props, ref);

  const _gpColumns = gpColumns(result?.gp_data_list?.[0]?.brand, { onClose: props.closeModal });

  const { gp_data_list = [], sorting_data_list = [], yz_data_list = [] } = result || {};

  return (
    <>
      <div className={styles.searchContent}>
        <Spin spinning={!!loading}>
          {result ? (
            gp_data_list.length || sorting_data_list.length || yz_data_list.length ? (
              <>
                {Array.isArray(yz_data_list) &&
                  yz_data_list.length > 0 &&
                  yz_data_list.map((item, index) => (
                    <div key={item.id} className={styles.card}>
                      <Card>
                        <Row type="flex" align="middle" justify="space-between" gutter={[12, 12]}>
                          <Col>
                            <Avatar
                              src={`https://img.kuaidihelp.com/brand_logo/icon_${item.brand}.png`}
                            />
                          </Col>
                          <Col style={{ flex: 1 }}>
                            <Row type="flex" align="middle">
                              <Col>
                                <div className={classNames([styles.size16, styles.mb])}>
                                  <span>{item.brand_cn}</span>
                                  <a
                                    style={{ margin: '0 16px' }}
                                    onClick={() => showExpressModal(item)}
                                  >
                                    {item.waybill_no}
                                    {'>>'}
                                  </a>
                                  <span className={styles.colorGrey}>{item.status_cn}</span>
                                </div>
                                <div className={styles.colorGrey}>
                                  <span>客户电话：</span>
                                  {item.phone}
                                  <span style={{ marginLeft: 16 }}>取件码：</span>
                                  {item.pickup_code}
                                </div>
                              </Col>
                              <Col className={styles.divider_left}>
                                <canvas id={`barcode_${item.waybill_no}`} />
                              </Col>
                              <Col className={styles.divider_left}>
                                <QRCodeCanvas
                                  value={item.waybill_no}
                                  size={70}
                                  bgColor="#ffffff"
                                  fgColor="#000000"
                                  level="L"
                                  includeMargin={false}
                                />
                              </Col>
                            </Row>
                          </Col>
                          {!item.operates ? (
                            <Col>
                              <Spin size="small" spinning={!!(getting && activeId === item.id)}>
                                <a onClick={() => showDetail(item, index)}>查看驿站操作记录</a>
                              </Spin>
                            </Col>
                          ) : null}
                        </Row>
                      </Card>
                      {item.operates &&
                        item.operates.length > 0 && (
                          <>
                            <Divider style={{ marginTop: 30 }} orientation="left">
                              驿站操作记录
                            </Divider>
                            <Table
                              columns={yzColumns}
                              dataSource={item.operates}
                              rowKey="_index"
                              pagination={false}
                            />
                          </>
                        )}
                    </div>
                  ))}
                {Array.isArray(gp_data_list) &&
                  gp_data_list.length > 0 && (
                    <>
                      <Divider style={{ marginTop: 30 }} orientation="left">
                        共配操作记录
                      </Divider>
                      <Table
                        columns={_gpColumns}
                        dataSource={result?.gp_data_list}
                        rowKey="_index"
                        pagination={false}
                        scroll={{ x: 1200 }}
                      />
                    </>
                  )}
                {Array.isArray(sorting_data_list) &&
                  sorting_data_list.length > 0 && (
                    <>
                      <Divider style={{ marginTop: 30 }} orientation="left">
                        进港分拣记录
                      </Divider>
                      <Table
                        columns={sortingColumns}
                        dataSource={result?.sorting_data_list}
                        rowKey="_index"
                        pagination={false}
                        scroll={{ x: 2200 }}
                      />
                    </>
                  )}
              </>
            ) : (
              <Empty mode="small" />
            )
          ) : null}
        </Spin>
      </div>
      <ExpressModal cleanData={() => setExpressInfo(null)} data={expressInfo} />
    </>
  );
});

export default SearchResult;
