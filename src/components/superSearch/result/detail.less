/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.detailContent {
  .detailItem {
    display: flex;
    margin-bottom: 8px;
    line-height: 24px;
    font-size: 14px;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #333;
      font-size: 15px;
      min-width: 80px;
      margin-right: 12px;
    }

    .value {
      color: #333;
      flex: 1;
    }
  }
}
