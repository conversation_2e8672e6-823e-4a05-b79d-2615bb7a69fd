/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable import/extensions */
/* eslint-disable no-shadow */
import React, { Component, createRef } from 'react';
import { Button, message } from 'antd';
import Recaptcha from '@/components/recaptcha';
import styles from './index.less';
import { isFunction, noop } from 'lodash';

class Captcha extends Component {
  static defaultProps = {
    getCaptchaButtonText: '获取验证码',
    getCaptchaSecondText: '秒',
  };

  constructor(props) {
    super(props);
    this.state = {
      count: 0,
      loading: false,
    };
    this.recaptcha = createRef();
  }

  componentWillUnmount() {
    clearInterval(this.interval);
  }

  onGetCaptcha = () => {
    const { onGetCaptcha, check = () => Promise.resolve() } = this.props;
    const { googleRobot, hide, show } = this.recaptcha.current;
    if (!googleRobot) return;
    check()
      .then(() => {
        googleRobot({
          onBefore: () => {
            this.setState({
              loading: true,
            });
          },
          callback: async ({ token, v3key, ...rest }, v2Fun) => {
            if (!token) {
              this.setState({
                loading: false,
              });
              return;
            }
            try {
              const { code, msg } = await onGetCaptcha({ token, site_key: v3key, ...rest });
              if (code == 0) {
                this.setState({
                  loading: false,
                });
                this.runGetCaptchaCountDown();
              } else if (code == 4003) {
                show(); // 展示谷歌验证码
                v2Fun(async ({ v2Token, v2key, isTx, ...rest }) => {
                  if (!isTx) {
                    // 不是腾讯验证码，启用谷歌
                    window.grecaptcha.reset();
                  }
                  hide();
                  this.setState({
                    loading: false,
                  });
                  try {
                    const { code, msg } = await onGetCaptcha({
                      token: v2Token,
                      site_key: v2key,
                      ...rest,
                    });
                    if (code == 0) {
                      this.runGetCaptchaCountDown();
                    } else if (msg) {
                      message.error(msg);
                    }
                  } catch (error) {
                    message.error(error.msg || error.message || error);
                  }
                });
              } else {
                this.setState({
                  loading: false,
                });
                message.error(msg);
                if (isFunction(this.props.onGetCaptchaError)) {
                  this.props.onGetCaptchaError({ code });
                }
              }
            } catch (error) {
              this.setState({
                loading: false,
              });
              // message.error(error.message);
            }
          },
        });
      })
      .catch(noop);
  };

  runGetCaptchaCountDown = () => {
    const { countDown } = this.props;
    let count = countDown || 59;
    this.setState({
      count,
    });
    this.interval = window.setInterval(() => {
      count -= 1;
      this.setState({
        count,
      });

      if (count === 0) {
        clearInterval(this.interval);
      }
    }, 1000);
  };

  render() {
    const {
      onGetCaptcha,
      getCaptchaButtonText,
      getCaptchaSecondText,
      disabled = false,
      check,
      ...restProps
    } = this.props;
    const { count, loading } = this.state;
    return (
      <>
        <Button
          disabled={!!count || disabled || loading}
          {...restProps}
          onClick={this.onGetCaptcha}
          className={styles.button}
        >
          {count ? `${count} ${getCaptchaSecondText}` : getCaptchaButtonText}
        </Button>
        <Recaptcha ref={this.recaptcha} />
      </>
    );
  }
}

export default Captcha;
