/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import * as React from 'react';
import AvatarItem from './AvatarItem';

export interface IAvatarListProps {
  size?: 'large' | 'small' | 'mini' | 'default';
  style?: React.CSSProperties;
  children: React.ReactElement<AvatarItem> | Array<React.ReactElement<AvatarItem>>;
}

export default class AvatarList extends React.Component<IAvatarListProps, any> {
  public static Item: typeof AvatarItem;
}
