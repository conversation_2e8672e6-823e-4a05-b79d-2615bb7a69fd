/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.activeChart {
  position: relative;
}
.activeChartGrid {
  p {
    position: absolute;
    top: 80px;
  }
  p:last-child {
    top: 115px;
  }
}
.activeChartLegend {
  position: relative;
  height: 20px;
  margin-top: 8px;
  font-size: 0;
  line-height: 20px;
  span {
    display: inline-block;
    width: 33.33%;
    font-size: 12px;
    text-align: center;
  }
  span:first-child {
    text-align: left;
  }
  span:last-child {
    text-align: right;
  }
}
