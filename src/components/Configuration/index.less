/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import "~antd/lib/style/themes/default.less";

.center {
  text-align: center;
}

:global {
  .editable-cell {
    position: relative;
  }

  .editable-cell-value-wrap {
    padding: 5px 12px;
    cursor: pointer;
  }

  .editable-row:hover .editable-cell-value-wrap {
    padding: 4px 11px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
  }
  .ant-select-dropdown-menu-item-selected {
    font-weight: 500 !important;
  }

  .expand-table .ant-table-body table .ant-table-row {
    height: 65px;
  }

  .expand-table .ant-table-body table {
    border: none;
  }
  .expand-table .ant-table-body table .ant-table-tbody tr td {
    background-color: #fff;
  }
  
  .expand-table .ant-table-body table .ant-table-tbody tr td:nth-child(1),
  .expand-table .ant-table-body table .ant-table-tbody tr td:last-child,
  .expand-table .ant-table-body table .ant-table-tbody tr td:nth-child(5) {
    border-right: none;
  }
}
