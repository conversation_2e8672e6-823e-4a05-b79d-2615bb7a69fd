/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-useless-escape */


const patterns = {
  contact: {
    // 联系方式
    code: 30012,
    pattern:
      /^1\d{10}([-|\+]{1}\d{3,4})?$|^(0\d{2,3}(-|\s?))?[2-9]{1}\d{6,7}((-|\s?)\d{1,4})?$|^(00)?(886)?[0][9]\d{8}$|^(852)?([6|9])\d{7}|^(853)?[6](8|6)\d{6}$/,
    message: '请输入正确的手机或固话',
    tag: '联系方式',
  },
  contact_blurry: {
    code: 300122,
    pattern:
      /^\d{4,11}$|^1[2-9]{1}[0-9]{1}\d{8}$|^(0\d{2,3}(-|\s?))?[2-9]{1}\d{6,7}((-|\s?)\d{1,4})?$/,
    message: '不符合指定规则',
    tag: '模糊的联系方式',
  },
  waybillNum: {
    // 运单号
    code: 3014,
    pattern: /^[0-9a-zA-Z\-]{4,30}$/,
    message: '单号不正确',
    tag: '单号',
  },
  cn: {
    code: 3020,
    pattern: /[a-zA-Z\u4e00-\u9fa5]/gm,
    message: '无中文字符',
    tag: '中文',
  },
  print4G: {
    code: 3021,
    pattern: /^86[0-9]{13}$/,
    message: '非4g打印机',
  },
  bh: {
    // 取件码
    code: 3016,
    pattern: /^(?=(?:[^a-zA-Z]*[a-zA-Z]){0,4}[^a-zA-Z]*$)[\da-zA-Z-]{1,9}\d$|^[\da-zA-Z-]$/,
    message: '取件码只可包含字母、数字、-，长度不超过10位', // 限制字母4位的不提示
    tag: '取件码',
  },
  phone: {
    // 手机号
    code: 3001,
    pattern: /^1[2-9]{1}[0-9]{1}\d{8}$/,
    message: '手机号码输入错误',
    tag: '手机号',
  },
};

const check = (
  key,
  value,
  special= {},
) => {
  const rule = special[key] ||
    patterns[key] || {
      code: 11,
      message: `缺少对应的正则规则：${key}`,
    };
  const { pattern, message, tag } = rule;
  if (!pattern) {
    return rule;
  }
  const isExact = value.match(pattern);
  if (isExact) {
    return {
      pattern,
      tag,
      code: 0,
      message: '验证通过',
    };
  }
  return {
    code: 1001,
    pattern,
    tag,
    message: value ? message : `${tag}不能为空`,
  };
};

export { patterns as default, check };
