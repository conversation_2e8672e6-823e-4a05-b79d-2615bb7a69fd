/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { AutoComplete } from 'antd';
import {useSearchBar } from './useSearchBar';
import { forwardRef } from 'react';

const SearchBar = forwardRef((props,ref) => {
  const { options, value, onSearch, onSelect, onClear } = useSearchBar(props);

  return (
    <AutoComplete
      ref={ref}
      value={value}
      allowClear
      style={{ width: 300 }}
      onSelect={onSelect}
      onSearch={onSearch}
      onClear={onClear}
      placeholder={props.placeholder || '请输入'}
    >
       {options.map((item) => {
    return <AutoComplete.Option value={item.value} key={item.value} data-item={item}>{item.label}</AutoComplete.Option>
  })}
      
    </AutoComplete>
  );
});

export default SearchBar;
