/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { check } from './patterns';
import { isFunction, isString } from 'lodash';
import { useEffect, useMemo, useState } from 'react';

const searchKeys = [
  {
    label: '工号',
    pattern: 'waybillNum' ,
    key: 'search_waybill',
  },
  {
    label: '姓名',
    pattern: 'cn' ,
    key: 'search_note',
  },
];

export function useSearchBar(props) {
  const { onChange, keywords } = props;
  const [value, setValue] = useState('');
  const triggerChange = (v) => {
    if (isFunction(onChange)) {
      onChange(v);
    }
  };

  const options = useMemo(() => {
    return value
      ? searchKeys
          .filter((item) => check(item.pattern, value).code === 0)
          .map((item) => ({
            label: `${value}　${item.label}`,
            value: item.key,
          }))
      : [];
  }, [value, searchKeys]);

  const onSearch = (value) => setValue(value);

  const onSelect = (key) =>
    triggerChange({
      type: key,
      value,
    });

  const onClear = () => {
    triggerChange({ value: '' });
    setValue('');
  };

  useEffect(() => {
    if (isString(keywords)) {
      if (keywords) {
        const cur = searchKeys.find((item) => check(item.pattern, keywords).code === 0);
        if (cur) {
          triggerChange({
            type: cur.key,
            value: keywords,
          });
        }
      } else {
        onClear();
      }
    }
  }, [keywords]);

  return {
    value,
    options,
    onSearch,
    onSelect,
    onClear,
  };
}
