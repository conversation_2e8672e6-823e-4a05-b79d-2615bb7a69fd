/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { getStorageSync, setStorage } from '@/utils/storage';
import { copy } from '@/utils/utils';
import { Modal, Button, Steps, message } from 'antd';
import React, { useState, useEffect } from 'react';

const key = 'truck_guide_seen';
const wx = 'kbyz12';

const Guide = ({ trigger }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [showModal, setShowModal] = useState(false);
  const steps = [
    {
      title: '1、新增车辆',
    },
    {
      title: '2、添加停靠点',
    },
    {
      title: '3、开始使用',
    },
  ];

  // 检查是否第一次访问
  useEffect(
    () => {
      if (!trigger) {
        const { data: hasSeenGuide } = getStorageSync(key);
        setShowModal(!hasSeenGuide);
        setStorage({ key, data: true });
      }
    },
    [trigger],
  );

  // 获取当前步骤的图片URL
  const getCurrentImage = () => {
    return `https://cdn-img.kuaidihelp.com/kb_city_web/truck/step_${currentStep}.png`;
  };

  // 获取当前步骤的按钮文本
  const getButtonText = () => {
    switch (currentStep) {
      case 1:
        return '下一步';
      case 2:
        return '下一步';
      case 3:
        return '知道了';
      default:
        return '知道了';
    }
  };

  // 处理按钮点击
  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    } else {
      setShowModal(false);
      setCurrentStep(1);
    }
  };

  // 处理弹窗关闭
  const handleCancel = () => {
    setShowModal(false);
  };

  const handleCopy = () => {
    copy(wx, copied => {
      if (copied) {
        message.success('复制成功！');
      } else {
        message.warning('复制失败，请手动复制！');
      }
    });
  };

  const triggerDom = trigger
    ? React.cloneElement(trigger, {
        onClick: () => {
          setShowModal(true);
        },
      })
    : null;

  return (
    <>
      {triggerDom}
      <Modal
        title="无人车使用说明"
        visible={showModal}
        onCancel={handleCancel}
        maskClosable={false}
        footer={[
          <Button key="next" type="primary" onClick={handleNext}>
            {getButtonText()}
          </Button>,
        ]}
        bodyStyle={{ paddingTop: 12 }}
        width={780}
      >
        <div style={{ textAlign: 'center' }}>
          <img
            src={getCurrentImage()}
            alt={`使用说明第${currentStep}步`}
            style={{
              width: 'auto',
              height: '300px',
            }}
          />
          <div style={{ padding: 12 }}>
            <Steps current={currentStep - 1} className="truck-step">
              {steps.map(item => (
                <Steps.Step key={item.title} title={item.title} />
              ))}
            </Steps>
          </div>
          <div style={{ marginTop: 12, textAlign: 'left' }}>
            客服微信号： <span>{wx}</span>
            <Button type="link" onClick={handleCopy}>
              复制
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default Guide;
