/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Card, Col, Row, Typography } from 'antd';
import React from 'react';
import Operate from './component/operate';

const { Text } = Typography;

const Setting = () => {
  return (
    <Card bordered={false}>
      <Row type="flex" align="middle" justify="space-between" style={{ height: '70px' }}>
        <Col span={16}>
          <Row justify="start" align="middle" gutter={[10, 10]}>
            <Col span={24}>
              <Text strong>授权设置</Text>
            </Col>
            <Col span={24}>请设置无人车厂商提供的APPID及相关参数</Col>
          </Row>
        </Col>
        <Col offset={1}>
          <Operate />
        </Col>
      </Row>
    </Card>
  );
};

export default Setting;
