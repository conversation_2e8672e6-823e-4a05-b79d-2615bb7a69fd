/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import ProModalFormExtend from '@/components/ProModalFormExtend';
import { addAuth, deleteAuth, editAuth, getAuthList, getTruckType } from '@/services/truck';
import { useRequest } from 'ahooks';
import { Button, Form, Input, Popconfirm, Select } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';

const Operate = props => {
  const { form } = props;
  const { getFieldDecorator } = form;
  const { data: typeList = [] } = useRequest(getTruckType);
  const { data: authList, run: refreshAuthList } = useRequest(getAuthList, { manual: true });

  const authData = authList?.[0] || {};
  const auth_id = useMemo(() => authList?.[0]?.id, [authList]);

  const history = useHistory();

  const onFinish = async () => {
    const values = form.getFieldsValue();
    let success;
    if (auth_id) {
      success = await editAuth({ ...values, auth_id });
    } else {
      success = await addAuth(values);
    }
    success && refreshAuthList();
    return success;
  };

  const handleDelete = async () => {
    const success = await deleteAuth({ auth_id });
    if (success) {
      refreshAuthList();
    }
  };

  const onVisibleChange = visible => {
    if (visible && auth_id) {
      form.setFieldsValue(authData);
    }
  };

  useEffect(
    () => {
      if (history.location.pathname == '/truck/setting') {
        refreshAuthList();
      }
    },
    [history.location.pathname],
  );

  return (
    <>
      <ProModalFormExtend
        trigger={
          <Button type="link" style={{ display: auth_id ? 'none' : 'block' }}>
            {auth_id ? '修改' : '设置'}
          </Button>
        }
        title="授权设置"
        onFinish={onFinish}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        form={form}
        modalProps={{ destroyOnClose: true }}
        onVisibleChange={onVisibleChange}
      >
        <Form.Item label="渠道">
          {getFieldDecorator('channel', {
            rules: [{ required: true, message: '请选择渠道' }],
          })(
            <Select placeholder="请选择渠道">
              {typeList.map(item => (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>,
          )}
        </Form.Item>
        <Form.Item label="APPID">
          {getFieldDecorator('app_id', {
            rules: [{ required: true, message: '请输入APPID' }],
          })(<Input placeholder="请输入APPID" />)}
        </Form.Item>
        <Form.Item label="KEY">
          {getFieldDecorator('app_key', {
            rules: [{ required: true, message: '请输入KEY' }],
          })(<Input placeholder="请输入KEY" />)}
        </Form.Item>
        <Form.Item label="Organization Code">
          {getFieldDecorator('push_key', {
            rules: [{ required: true, message: '请输入组织机构代码' }],
          })(<Input placeholder="请输入组织机构代码" />)}
        </Form.Item>
        <div>注：上述信息均需向无人车厂商获取，填写后，请联系快宝运营人员部署</div>
      </ProModalFormExtend>
      {auth_id && (
        <Popconfirm title="确定删除授权吗？" onConfirm={handleDelete}>
          <Button type="link" style={{ marginLeft: 8 }}>
            删除
          </Button>
        </Popconfirm>
      )}
    </>
  );
};

export default Form.create()(Operate);
