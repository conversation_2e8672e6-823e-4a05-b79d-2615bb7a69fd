/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import { Card, Form } from 'antd';
import Search from './component/search';
import { useTruckInfo } from './utils';
import ProTableExtend from '@/components/ProTableExtend';

const TruckInfo = props => {
  const { form, columns, pagination, actionRef, getList, channelList } = useTruckInfo(props);

  return (
    <Card>
      <Search form={form} actionRef={actionRef} channelList={channelList} />
      <ProTableExtend
        rowKey="id"
        columns={columns}
        request={getList}
        formRef={form}
        actionRef={actionRef}
        pagination={pagination}
        manualRequest
        bordered={false}
      />
    </Card>
  );
};

export default Form.create()(TruckInfo);
