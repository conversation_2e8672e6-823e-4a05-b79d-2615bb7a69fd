/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React, { useEffect, useRef } from 'react';
import { deleteTruck, getTruckList } from '@/services/truck';
import { But<PERSON>, Col, Popconfirm, Row } from 'antd';
import { useHistory } from 'react-router';
import QrCode from './component/qrCode';

export const useTruckInfo = props => {
  const { form } = props;
  const actionRef = useRef();
  const history = useHistory();
  const pagination = {
    showQuickJumper: false,
    size: 'small',
    pageSize: 20,
    hideOnSinglePage: false,
  };
  const columns = [
    {
      title: '车牌号',
      dataIndex: 'car_no',
      key: 'car_no',
      align: 'center',
      width: 200,
    },
    {
      title: '车辆ID',
      dataIndex: 'third_key',
      key: 'third_key',
      align: 'center',
      width: 200,
    },
    {
      title: '品牌',
      dataIndex: 'channel_name',
      key: 'channel_name',
      align: 'center',
      width: 200,
    },
    {
      title: '车况',
      dataIndex: 'status_name',
      key: 'status_name',
      align: 'center',
      render: (_, { status_name = '', status_info = '' }) =>
        `${status_name}${status_info ? `：${status_info}` : ''}`,
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 200,
      align: 'center',
      fixed: 'right',
      render: (_, { id, car_no, auth_id }) => (
        <Row type="flex" justify="end" gutter={[16]}>
          <Col>
            <QrCode data={{ id, car_no }} />
          </Col>
          <Col>
            <Popconfirm
              title="确定删除车辆吗？"
              onConfirm={() =>
                deleteTruck({ car_id: id, auth_id }).then(res => res && actionRef.current.submit())
              }
            >
              <Button type="primary">删除</Button>
            </Popconfirm>
          </Col>
        </Row>
      ),
    },
  ];

  const getList = async params => {
    const { current = 1, pageSize = 20, ...rest } = params || {};
    const res = await getTruckList({
      ...rest,
      page: current,
      size: pageSize,
    });
    const { data: { list = [], total = 0 } = {} } = res;
    return {
      data: Array.isArray(list) ? list : [],
      total,
      current,
    };
  };

  useEffect(
    () => {
      if (history.location.pathname == '/truck/info') {
        actionRef.current.submit();
      }
    },
    [history.location.pathname],
  );

  return {
    form,
    columns,
    pagination,
    actionRef,
    getList,
  };
};
