/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { getTruckType } from '@/services/truck';
import { useRequest } from 'ahooks';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import React from 'react';
import Add from './add';

const Search = props => {
  const { form, actionRef } = props;
  const { getFieldDecorator } = form;
  const { data: typeList = [] } = useRequest(getTruckType);
  const statusList = [
    {
      label: '全部',
      value: '',
    },
    {
      label: '已停靠',
      value: '0',
    },
    {
      label: '行驶中',
      value: '1',
    },
    {
      label: '异常',
      value: '2',
    },
  ];

  const handleSearch = () => {
    actionRef.current.submit();
  };

  return (
    <Row type="flex" align="middle" justify="space-between" style={{ marginBottom: 16 }}>
      <Col>
        <Form layout="inline">
          <Form.Item label="车牌号">
            {getFieldDecorator('car_no')(
              <Input style={{ width: 200 }} placeholder="请输入车牌号" />,
            )}
          </Form.Item>
          <Form.Item label="品牌">
            {getFieldDecorator('channel', {
              initialValue: '',
            })(
              <Select style={{ width: 150 }} placeholder="请选择品牌">
                {[
                  {
                    label: '全部',
                    value: '',
                  },
                  ...typeList,
                ].map(item => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
          <Form.Item label="车况">
            {getFieldDecorator('status', {
              initialValue: '',
            })(
              <Select style={{ width: 150 }} placeholder="请选择车况">
                {statusList.map(item => (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>,
            )}
          </Form.Item>
        </Form>
      </Col>
      <Col>
        <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
          查询
        </Button>
        <Add typeList={typeList} actionRef={actionRef} />
      </Col>
    </Row>
  );
};

export default Search;
