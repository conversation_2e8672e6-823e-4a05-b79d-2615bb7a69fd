/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import ProModalFormExtend from '@/components/ProModalFormExtend';
import { addTruck, getAuthList } from '@/services/truck';
import { Button, Form, Input, Modal, Select } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { useHistory } from 'react-router';
import { useRequest } from 'ahooks';

const Add = props => {
  const { form, typeList, actionRef } = props;
  const { getFieldDecorator } = form;
  const history = useHistory();
  const { data: authList, run: refreshAuthList } = useRequest(getAuthList, { manual: true });

  const auth_id = useMemo(() => authList?.[0]?.id, [authList]);

  const onFinish = async () => {
    const values = form.getFieldsValue();

    const _auth_id = authList.find(item => item.channel === values.auth_id)?.auth_id;
    if (!auth_id) {
      Modal.info({
        title: '温馨提示',
        content: '请在“设置”项，授权设置处填写对应的APPID',
      });
      return false;
    }
    const success = await addTruck({ ...values, auth_id: _auth_id });
    success && actionRef.current.submit();
    return success;
  };

  const beforeOpen = async () => {
    if (!auth_id) {
      Modal.info({
        title: '温馨提示',
        content: '请在“设置”项，授权设置处填写对应的APPID',
      });
      return false;
    }
    return true;
  };

  useEffect(
    () => {
      if (history.location.pathname == '/truck/info') {
        refreshAuthList();
      }
    },
    [history.location.pathname],
  );

  return (
    <ProModalFormExtend
      trigger={<Button type="primary">新增</Button>}
      title="新增车辆"
      onFinish={onFinish}
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      form={form}
      modalProps={{ destroyOnClose: true }}
      beforeOpen={beforeOpen}
    >
      <Form.Item label="车辆ID">
        {getFieldDecorator('third_key', {
          rules: [{ required: true, message: '请输入车辆ID' }],
        })(<Input placeholder="请输入车辆ID" />)}
      </Form.Item>
      <Form.Item label="品牌">
        {getFieldDecorator('auth_id', {
          rules: [{ required: true, message: '请选择品牌' }],
        })(
          <Select placeholder="请选择品牌">
            {typeList.map(item => (
              <Select.Option key={item.value} value={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>,
        )}
      </Form.Item>
      {/* <Form.Item label="车辆ID">
        {getFieldDecorator('status', {
          rules: [{ required: true, message: '请选择车辆ID' }],
        })(<Input placeholder="请输入车辆ID" />)}
      </Form.Item> */}
    </ProModalFormExtend>
  );
};

export default Form.create()(Add);
