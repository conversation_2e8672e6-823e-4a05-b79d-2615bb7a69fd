/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import ProModalFormExtend from '@/components/ProModalFormExtend';
import { Button, Col, Row } from 'antd';
import { QRCodeCanvas } from 'qrcode.react';
import React from 'react';

const QrCode = props => {
  const { data: { car_no, id } = {} } = props;
  const value = `https://m.kuaidihelp.com?autoCarId=${id}`;

  const downloadQrCode = () => {
    const canvas = document.querySelector(`#qr-code-${id}`);
    const link = document.createElement('a');
    link.href = canvas.toDataURL();
    link.download = `${car_no}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <ProModalFormExtend
      trigger={<Button type="primary">二维码</Button>}
      title={
        <div>
          <span
            style={{
              color: '#1785ff',
            }}
          >
            {car_no}
          </span>
          二维码
        </div>
      }
      modalProps={{ destroyOnClose: true, footer: null }}
    >
      <Row type="flex" justify="center">
        <Col>
          <QRCodeCanvas
            id={`qr-code-${id}`}
            value={value}
            size={250}
            bgColor="#ffffff"
            fgColor="#000000"
            level="L"
            includeMargin={false}
          />
        </Col>
      </Row>
      <div style={{ padding: 16 }}>
        旗下快递员APP及快宝驿站APP扫码可进行发车/控车/集包及包裹交付等操作，建议张贴二维码于车身，方便操作
      </div>
      <Row type="flex" justify="end">
        <Col>
          <Button type="primary" onClick={downloadQrCode}>
            复制下载链接
          </Button>
        </Col>
      </Row>
    </ProModalFormExtend>
  );
};

export default QrCode;
