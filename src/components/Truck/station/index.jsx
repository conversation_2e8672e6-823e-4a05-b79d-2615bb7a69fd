/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import { Card, Form } from 'antd';
import Search from './component/search';
import { useTruckStation } from './utils';
import ProTableExtend from '@/components/ProTableExtend';

const TruckStation = props => {
  const { form, columns, pagination, actionRef, getList, auth_id } = useTruckStation(props);

  return (
    <Card>
      <Search form={form} actionRef={actionRef} auth_id={auth_id} />
      <ProTableExtend
        rowKey="id"
        columns={columns}
        request={getList}
        formRef={form}
        actionRef={actionRef}
        pagination={pagination}
        bordered={false}
        manualRequest
      />
    </Card>
  );
};

export default Form.create()(TruckStation);
