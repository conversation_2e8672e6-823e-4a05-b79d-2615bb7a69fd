/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, Col, Form, Input, Row } from 'antd';
import React from 'react';
import Add from './add';

const Search = props => {
  const { form, actionRef, auth_id } = props;
  const { getFieldDecorator } = form;

  const handleSearch = () => {
    actionRef.current.submit();
  };

  return (
    <Row type="flex" align="middle" justify="space-between" style={{ marginBottom: 16 }}>
      <Col>
        <Form layout="inline">
          <Form.Item label="停靠点名称">
            {getFieldDecorator('name')(
              <Input style={{ width: 200 }} placeholder="请输入停靠点名称" />,
            )}
          </Form.Item>
        </Form>
      </Col>
      <Col>
        <Button type="primary" style={{ marginRight: 16 }} onClick={handleSearch}>
          查询
        </Button>
        <Add tableRef={actionRef} auth_id={auth_id} />
      </Col>
    </Row>
  );
};

export default Search;
