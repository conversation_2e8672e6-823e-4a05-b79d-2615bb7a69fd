/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Checkbox, Divider, List } from 'antd';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.less';
import { debounce } from 'lodash';
import { randomCode } from '@/utils/utils';
import { getCityInfoByCity } from '@/components/Map/_utils';
import { getAllStation } from '@/services/truck';

const MapAddressSearch = props => {
  const {
    data,
    open = true,
    ready = true,
    value = null,
    auth_id,
    onChange,
    onReady,
    mapRef,
  } = props;
  const searchId = `mapAddressSearch_${randomCode(8)}`;
  const actionRef = useRef({
    page: 1,
    keywords: '',
    list: [],
  });
  const lastData = useRef({});
  const baseList = useRef([]);
  const [hasMore, setHasMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState([]);
  const listLength = list.length;
  const hasList = listLength > 0;

  // 加载数据，有防抖
  const loadData = useCallback(
    debounce(
      async (page, d) => {
        actionRef.current.page = page;
        const { keywords: k, city = '' } = d || {};
        const { aMap } = mapRef.current;
        setLoading(true);

        if (!k && baseList.current?.length && lastData.current?.city == d?.city) {
          setLoading(false);
          setHasMore(false);
          setList(baseList.current);
          return;
        }
        lastData.current = d;

        const cityLocation = await getCityInfoByCity(city);
        if (!cityLocation) {
          setList([]);
          baseList.current = [];
          setLoading(false);
          setHasMore(false);
          if (d?.location && k) {
            aMap.setCenter([d.location.lng, d.location.lat]);
          }
          return;
        }

        const { lng, lat } = cityLocation;

        getAllStation({
          auth_id,
          lat,
          lon: lng,
        })
          .then(res => {
            const { list: curList } = actionRef.current;
            const { list: pois } = res || {};
            const curPois = pois || [];
            const newList = page === 1 ? curPois : [...curList, ...curPois];
            baseList.current = newList;
            const showList = newList.filter(item => JSON.stringify(item).indexOf(k) > -1);
            setList(showList);
            setLoading(false);
            setHasMore(false);
            const firstPoi = showList[0];
            if (firstPoi) {
              aMap.setCenter([firstPoi.lon, firstPoi.lat]);
            }
          })
          .catch(() => {
            setLoading(false);
            setHasMore(false);
          });
      },
      300,
      { leading: false, trailing: true },
    ),
    [auth_id],
  );

  // 翻页
  const loadMoreData = () => {
    if (loading) {
      return;
    }
    loadData(1 + actionRef.current.page, data);
  };

  // 选择
  const handleClick = item => {
    if (item.relation == 1) return;
    onChange({
      ...item,
      lng: item.lng || item.lon || item.longitude,
      lat: item.lat || item.latitude,
    });
    // formatPoiItem('amap', item).then(res => {
    //   console.log(res);
    //   onChange(res);
    // });
  };

  useEffect(
    () => {
      if (!ready) return;
      loadData(1, data);
    },
    [open, ready, data],
  );

  useEffect(
    () => {
      actionRef.current.list = list;
      onReady &&
        onReady(
          list.map(item => ({
            ...item,
            lng: item.lng || item.lon || item.longitude,
            lat: item.lat || item.latitude,
          })),
        );
    },
    [list],
  );

  return (
    <div>
      <div id={searchId} className={styles.searchWrapper}>
        <InfiniteScroll
          dataLength={listLength}
          next={loadMoreData}
          hasMore={hasMore}
          endMessage={
            hasList ? (
              <Divider>
                <div>已全部加载</div>
              </Divider>
            ) : null
          }
          scrollableTarget={searchId}
        >
          <List
            loading={loading && actionRef.current.page === 1}
            dataSource={list}
            renderItem={item => (
              <List.Item
                key={item.id}
                onClick={() => handleClick(item)}
                className={styles.searchItem}
              >
                <List.Item.Meta title={item.third_name} description={item.address} />
                <Checkbox
                  checked={!!value?.find(v => v.id === item.id)}
                  disabled={item.relation == 1}
                />
              </List.Item>
            )}
          />
        </InfiniteScroll>
      </div>
    </div>
  );
};

export default MapAddressSearch;
