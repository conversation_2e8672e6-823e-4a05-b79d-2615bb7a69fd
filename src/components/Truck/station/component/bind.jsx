/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import ProModalFormExtend from '@/components/ProModalFormExtend';
import { getYzList } from '@/services/report';
import { bindTruckStation } from '@/services/truck';
import { useRequest } from 'ahooks';
import { Button, Input, List, Radio } from 'antd';
import React, { useState } from 'react';

const Bind = props => {
  const { tableRef, stop_id, auth_id } = props;
  const [list, setList] = useState([]);
  const [selected, setSelected] = useState(null);

  const { run } = useRequest(getYzList, {
    manual: true,
    onSuccess: (res, req) => {
      const data = Array.isArray(res?.data) ? res.data : [];
      setList(
        data.filter(
          item =>
            (item?.company_name || '').indexOf(req[0]?.search ?? '') > -1 ||
            (item?.phone || '').indexOf(req[0]?.search ?? '') > -1,
        ),
      );
    },
  });

  const handleSubmit = async () => {
    const success = await bindTruckStation({ inn_id: selected, stop_id, auth_id });
    if (success) {
      tableRef.current.submit();
      return true;
    }
    return false;
  };

  const handleSearch = v => {
    run({ search: v });
  };

  const onVisibleChange = v => {
    if (v) {
      setSelected(null);
      setList([]);
    }
  };

  return (
    <ProModalFormExtend
      title="绑定驿站"
      trigger={<Button type="primary">绑定驿站</Button>}
      okButtonProps={{ disabled: !selected }}
      modalProps={{ destroyOnClose: true }}
      onOk={handleSubmit}
      onVisibleChange={onVisibleChange}
    >
      <div>可为停靠点绑定对应的驿站，绑定后停靠点名称将更新为驿站名称</div>
      <Input.Search
        style={{ padding: '8px 0' }}
        placeholder="请输入驿站的注册手机号码或驿站名称查找"
        onSearch={handleSearch}
        enterButton="搜索"
      />
      <List
        style={{ maxHeight: 388, overflow: 'auto' }}
        dataSource={list}
        renderItem={item => (
          <List.Item>
            <Radio checked={selected === item.cm_id} onChange={() => setSelected(item.cm_id)}>
              {item.company_name}
            </Radio>
          </List.Item>
        )}
      />
      <div style={{ paddingTop: 8 }}>请选择驿站进行绑定</div>
    </ProModalFormExtend>
  );
};

export default Bind;
