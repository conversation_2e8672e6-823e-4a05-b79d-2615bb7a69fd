/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.location {
  width: 40px;
  height: 40px;
  color: #fff;
  font-size: 20px;
  line-height: 40px;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  cursor: pointer;
  position: absolute;
  right: 16px;
  top: 16px;
}
.search {
  padding: 12px;
  :global {
    .ant-input-suffix {
      background-color: #fff;
    }
    .ant-input-affix-wrapper.ant-input-affix-wrapper-input-with-clear-btn
      .ant-input:not(:last-child) {
      padding-right: 60px;
    }
  }
}
.searchBtn {
  display: inline-block;
  color: #1890ff;
  cursor: pointer;
  margin-left: 8px;
  &:active {
    opacity: 0.8;
  }
}

.right {
  display: flex;
  height: 100%;
  flex-direction: column;
  overflow: hidden;
}
.list {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  box-sizing: border-box;
  & > div {
    width: 100%;
  }
}
.footer {
  box-sizing: border-box;
  margin-top: 2px;
  padding: 8px 12px;
  box-shadow: 0 0 4px 2px rgba(22, 20, 20, 0.06);
}
.alert {
  padding: 12px;
  padding-top: 0;
}
