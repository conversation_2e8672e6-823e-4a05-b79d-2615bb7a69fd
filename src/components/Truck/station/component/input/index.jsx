/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Input } from 'antd';
import React, { useEffect, useMemo, useRef } from 'react';
import KbGeographicView from '@/components/KbGeographicView/';
import { getCityInfoAndRecord } from '@/components/Map/_utils';

const MapAddressInput = props => {
  const actionRef = useRef({});
  const {
    ready,
    suffix,
    placeholder = '输入地址关键词',
    value,
    onChange,
    onOpenDropdown,
    disableSearchWhenClick,
  } = props;
  const { province = '', city = '', keywords = '' } = value || {};
  const cityValue = useMemo(() => [province, city], [province, city]);

  const triggerChangeAndOpen = (v, t) => {
    onChange(v, t);
    if (onOpenDropdown) {
      onOpenDropdown(v, t);
    }
  };

  // 城市选择
  const handleChangeCity = v => {
    const [p, c] = v;
    onChange({ ...value, province: p, city: c });
  };

  // 关键词输入
  const handleChangeKeywords = e =>
    triggerChangeAndOpen({ ...value, keywords: e.target.value }, 'change');
  const handleClickKeywords = () => {
    if (disableSearchWhenClick) return;
    triggerChangeAndOpen(value, 'click');
  };

  // 地图准备就绪
  useEffect(
    () => {
      // 地图准备就绪，且未选择城市时，使用自动定位的数据
      actionRef.current.value = value;
      const { city: c1 } = actionRef.current.value || {};
      if (!ready || c1) return;
      getCityInfoAndRecord().then(cInfo => {
        const { city: c2 } = actionRef.current.value || {};
        if (c2) return;
        if (cInfo) {
          const { province: p, city: c } = cInfo;
          handleChangeCity([p, c]);
        }
      });
    },
    [ready, value],
  );

  return (
    <Input.Group compact>
      <KbGeographicView
        type="city"
        placeholder="省市区"
        style={{ width: '30%' }}
        allowClear={false}
        value={cityValue}
        onChange={handleChangeCity}
      />
      <Input
        style={{ width: '70%' }}
        suffix={suffix}
        placeholder={placeholder}
        onChange={handleChangeKeywords}
        value={keywords}
        allowClear
        onClick={handleClickKeywords}
      />
    </Input.Group>
  );
};

export default MapAddressInput;
