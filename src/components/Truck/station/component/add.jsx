/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import IconFont from '@/components/IconFont';
import KBMap, { KBMarker, KBMarkers } from '@/components/Map';
import { geocoderAddress, getCurrentPosition } from '@/components/Map/_utils';
import MapAddressModalPoi from '@/components/Map/poi';
import { Button, Col, Modal, Row } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import MapAddressInput from './input';
import MapAddressSearch from './searchList';
import { addTruckStation } from '@/services/truck';

const Add = props => {
  const { tableRef, auth_id } = props;
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [ready, setReady] = useState(false);
  const [inputValue, setInputValue] = useState(null);
  const [searchValue, setSearchValue] = useState(null);
  const actionRef = useRef({});
  const [poiPosition, setPoiPosition] = useState();
  const [poiData, setPoiData] = useState();
  const [checked, setChecked] = useState([]);
  const [stationList, setStationList] = useState([]);

  // 触发设置inputValue
  const triggerSetInputValue = v => {
    setInputValue({ ...actionRef.current.inputValue, ...v });
  };

  // 设置关键词与城市
  const setKeywordsAndCityInfo = v => {
    const value = { ...v, keywords: v.name };
    triggerSetInputValue(value);
    handleSearch(value);
  };

  // 根据定位信息设置关键词与城市
  const setKeywordsAndCityInfoByLocation = v => {
    // 未设置关键词时，设置城市与关键词；
    const { inputValue: i } = actionRef.current;
    const { keywords: k } = i || {};
    if (v && !k) {
      setKeywordsAndCityInfo(v);
    }
  };

  // 设置marker点
  const setMarker = v => {
    const { location, name } = v || {};
    if (location) {
      const { lat: latitude, lng: longitude } = location;
      setPoiPosition({
        longitude,
        latitude,
      });
      if (name) {
        setPoiData(v);
      } else {
        // 无名称，或者名称为默认当前位置，按照定位获取地址信息；
        geocoderAddress(location).then(curV => {
          setPoiData(curV);
          setKeywordsAndCityInfo(curV);
          // setKeywordsAndCityInfoByLocation(curV);
        });
      }
    }
  };

  // 地图事件
  const mapEvents = {
    created: aMap => {
      actionRef.current.aMap = aMap;
      setReady(true);
    },
    dragend: () => {},
    dragging: () => {},
    click: e => {
      const { lng, lat } = e.lnglat;
      setMarker({
        ...poiData,
        name: '',
        location: { lat, lng },
      });
      // geocoderAddress({ lat, lng }).then(v => {
      //   if (!v) return;
      //   setPoiData(v);
      //   setKeywordsAndCityInfo(v);
      // });
    },
  };

  // 确定地址
  // const handleConfirm = () => {
  //   if (onChange) {
  //     if (!value) {
  //       message.error('请先勾选地址');
  //       return;
  //     }
  //     onChange(value);
  //   }
  // };

  // 搜索表单输入
  const handleChange = v => triggerSetInputValue(v);
  const handleSearch = (v = inputValue) => {
    setChecked([]);
    setSearchValue(v);
  };

  // 选择搜索出的poi
  const handleChangeMapAddress = v => {
    if (checked.find(item => item.id === v.id)) {
      setChecked(checked.filter(item => item.id !== v.id));
    } else {
      setChecked([...checked, v]);
    }
  };

  // 点击地图按钮
  const handleClickToolBar = item => {
    const { key, action } = item;
    switch (key) {
      case 'location':
        // 精准定位
        if (loading) return;
        setLoading(true);
        getCurrentPosition().then(v => {
          console.log('location', v);
          setMarker(v);
          setKeywordsAndCityInfoByLocation({ ...v, action });
          setLoading(false);
          const { aMap } = actionRef.current;
          if (aMap) {
            const { lng, lat } = v.location;
            aMap.setCenter([lng, lat]);
          }
        });
        break;

      default:
        break;
    }
  };

  const handleOpen = () => {
    if (!auth_id) {
      Modal.info({
        title: '温馨提示',
        content: '请在“设置”项，授权设置处填写对应的APPID',
      });
      return;
    }
    setOpen(true);
  };

  const handleSubmit = () => {
    if (!checked.length) return;
    addTruckStation({
      auth_id,
      stop_ids: checked.map(item => item.id),
    }).then(res => {
      if (res) {
        tableRef.current.submit();
        setOpen(false);
      }
    });
  };

  const onGetList = v => {
    setStationList(v);
  };

  // 监听弹窗开启
  useEffect(
    () => {
      if (ready && open) {
        handleClickToolBar({ key: 'location' });
      }
    },
    [open, ready],
  );

  return (
    <>
      <Button type="primary" onClick={handleOpen}>
        新增
      </Button>
      <Modal
        visible={open}
        destroyOnClose
        bodyStyle={{ padding: 0, borderRadius: 4, overflow: 'hidden' }}
        footer={null}
        closable={false}
        width={1000}
        maskClosable={false}
        onCancel={() => setOpen(false)}
      >
        <Row type="flex" style={{ height: 500 }}>
          <Col span={14} style={{ position: 'relative' }}>
            <KBMap zoom={20} events={mapEvents}>
              {poiPosition &&
                poiData && (
                  <KBMarker
                    position={poiPosition}
                    extData={{ id: 'poi' }}
                    render={() => <MapAddressModalPoi data={poiData} />}
                  />
                )}

              <KBMarkers
                useCluster
                markers={stationList.map(item => ({
                  position: {
                    latitude: +item.lat,
                    longitude: +item.lng,
                  },
                  name: item.third_name,
                  ...item,
                }))}
                render={data => <MapAddressModalPoi data={data} type="stop" />}
              />
            </KBMap>
            <div
              className={styles.location}
              onClick={() => handleClickToolBar({ key: 'location', action: 'reset' })}
            >
              <IconFont type="location" />
            </div>
          </Col>
          <Col span={10} className={styles.right}>
            <div className={styles.search}>
              <MapAddressInput
                onChange={handleChange}
                value={inputValue}
                suffix={
                  <span className={styles.searchBtn} onClick={() => handleSearch()}>
                    搜索
                  </span>
                }
                placeholder="输入停靠点关键词"
                disableSearchWhenClick
              />
            </div>
            <Row type="flex" justify="space-between" gutter={[12, 12]} className={styles.alert}>
              <Col>请批量选择停靠点</Col>
              <Col>
                已选
                <span>{checked.length}</span>个
              </Col>
            </Row>
            <div className={styles.list}>
              <MapAddressSearch
                open={open}
                ready={ready}
                data={searchValue}
                onChange={handleChangeMapAddress}
                value={checked}
                auth_id={auth_id}
                onReady={onGetList}
                mapRef={actionRef}
              />
            </div>
            <Row type="flex" justify="end" className={styles.footer} gutter={[12]}>
              <Col>
                <Button type="primary" onClick={() => setOpen(false)}>
                  取消
                </Button>
              </Col>
              <Col>
                <Button type="primary" disabled={checked.length === 0} onClick={handleSubmit}>
                  确定
                </Button>
              </Col>
            </Row>
          </Col>
        </Row>
      </Modal>
    </>
  );
};

export default Add;
