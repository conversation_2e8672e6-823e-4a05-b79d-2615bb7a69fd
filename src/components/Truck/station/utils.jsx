/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React, { useEffect, useMemo, useRef } from 'react';
import {
  deleteTruckStation,
  getAuthList,
  getTruckStationList,
  unbindTruckStation,
} from '@/services/truck';
import { Button, Col, Modal, Popconfirm, Row } from 'antd';
import { useRequest } from 'ahooks';
import { useHistory } from 'react-router';
import Bind from './component/bind';

export const useTruckStation = props => {
  const { form } = props;
  const actionRef = useRef();
  const history = useHistory();
  const { data: authList, run: refreshAuthList } = useRequest(getAuthList, { manual: true });

  const auth_id = useMemo(() => authList?.[0]?.id, [authList]);

  const pagination = {
    showQuickJumper: false,
    size: 'small',
    pageSize: 20,
    hideOnSinglePage: false,
  };
  const columns = [
    {
      title: '停靠点名称',
      dataIndex: 'third_name',
      key: 'third_name',
      align: 'center',
      width: 250,
      render: (_, { inn_name, third_name }) => inn_name || third_name,
    },
    {
      title: '详细地址',
      dataIndex: 'province',
      key: 'province',
      align: 'center',
      render: (_, { province, city, district, address }) => {
        return `${province == city ? province : `${province}${city}`}${district}${address}`;
      },
      width: 330,
    },
    {
      title: '绑定驿站',
      dataIndex: 'inn_name',
      key: 'inn_name',
      align: 'center',
      width: 220,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 220,
      align: 'center',
      fixed: 'right',
      render: (_, { inn_id, id }) => (
        <Row type="flex" justify="end" gutter={[16]}>
          <Col>
            {inn_id ? (
              <Popconfirm
                title="确定解绑停靠点对应的驿站吗？"
                onConfirm={() =>
                  unbindTruckStation({ stop_id: id, inn_id, auth_id }).then(
                    res => res && actionRef.current.submit(),
                  )
                }
              >
                <Button type="primary">解绑驿站</Button>
              </Popconfirm>
            ) : (
              <Bind tableRef={actionRef} stop_id={id} auth_id={auth_id} />
            )}
          </Col>
          <Col>
            <Popconfirm
              title="确定删除停靠点吗？"
              onConfirm={() => {
                if (!auth_id) {
                  Modal.info({
                    title: '温馨提示',
                    content: '请在“设置”项，授权设置处填写对应的APPID',
                  });
                  return;
                }
                deleteTruckStation({ stop_ids: [id], auth_id }).then(
                  res => res && actionRef.current.submit(),
                );
              }}
            >
              <Button type="primary">删除</Button>
            </Popconfirm>
          </Col>
        </Row>
      ),
    },
  ];

  const getList = async params => {
    const { current = 1, pageSize = 20, ...rest } = params || {};
    const res = await getTruckStationList({
      ...rest,
      page: current,
      size: pageSize,
    });
    const { data: { list = [], total = 0 } = {} } = res;
    return {
      data: Array.isArray(list) ? list : [],
      total,
      current,
    };
  };

  // const params = useMemo(() => (auth_id ? { auth_id } : null), [auth_id]);

  useEffect(
    () => {
      if (history.location.pathname == '/truck/station') {
        refreshAuthList().then(res => {
          actionRef.current.submit({
            current: 1,
            auth_id: res?.[0]?.id,
          });
        });
      }
    },
    [history.location.pathname],
  );

  return {
    form,
    columns,
    pagination,
    actionRef,
    getList,
    auth_id,
  };
};
