/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import { Modal, Form, Input, Row, Col, Button, message, Typography } from 'antd';
import Captcha from '@/components/Captcha';
import { gunInformationLogin, sendLoginSms } from '@/services/configuration';

const { Text } = Typography;

const YtVerifyModal = ({ visible, data, form, cm_phone, courier_name, setVisible, setAccount }) => {
  const { getFieldDecorator, validateFields } = form;
  const {
    device_imei,
    device_model,
    branch_code,
    gun_pwd,
    gun_account_v2 = '',
    gun_pwd_v2 = '',
    gun_account = '',
  } = data || {};
  const title = gun_account ? `圆通${gun_account}，请验证` : '圆通验证';

  const onGetCaptcha = () => {
    return sendLoginSms({
      brand: 'yt',
      type: 2,
      cm_phone,
      device_imei,
      device_model,
      gun_account,
      branch_code,
      gun_pwd,
      gun_pwd_v2,
      gun_account_v2,
    });
  };

  const handleSubmit = () => {
    validateFields((err, values) => {
      if (err) return;
      gunInformationLogin({
        brand: 'yt',
        type: 2,
        cm_phone,
        device_imei,
        device_model,
        gun_account,
        branch_code,
        gun_pwd,
        gun_pwd_v2,
        gun_account_v2: values.gun_account_v2,
        sms_code: values.code,
      }).then(res => {
        if (res.code == 0) {
          setAccount({
            gun_account_v2: values.gun_account_v2,
          });
          message.success('验证成功');
          setVisible(false);
        } else {
          message.error(res.msg);
        }
      });
    });
  };

  const handleCancel = () => {
    // setAccount({
    //   device_imei: undefined,
    //   device_model: undefined,
    // });
    setVisible(false);
  };

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={handleCancel}
      destroyOnClose
      maskClosable={false}
      width={400}
      footer={
        <Row type="flex" justify="end">
          <Col>
            <Button onClick={handleCancel}>忽略</Button>
          </Col>
          <Col>
            <Button type="primary" onClick={handleSubmit}>
              确定提交
            </Button>
          </Col>
        </Row>
      }
    >
      {courier_name && (
        <Text>
          业务员：
          {courier_name}
          {cm_phone}
        </Text>
      )}
      <Form>
        <Form.Item>
          <Row gutter={[8]} type="flex" align="middle">
            <Col span={16}>
              {getFieldDecorator('gun_account_v2', {
                initialValue: gun_account_v2,
                rules: [
                  {
                    required: true,
                    message: '请输入手机号',
                  },
                ],
              })(<Input type="tel" placeholder="请输入手机号" />)}
            </Col>
            <Col span={8}>
              <Captcha
                type="primary"
                ghost
                onGetCaptcha={onGetCaptcha}
                disabled={!/^1[2-9]{1}[0-9]{1}\d{8}$/.test(gun_account_v2)}
              >
                获取验证码
              </Captcha>
            </Col>
          </Row>
        </Form.Item>
        <Form.Item>
          {getFieldDecorator('code', {
            rules: [
              {
                required: true,
                message: '请输入验证码',
              },
            ],
          })(<Input type="number" placeholder="请输入验证码" />)}
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default Form.create()(YtVerifyModal);
