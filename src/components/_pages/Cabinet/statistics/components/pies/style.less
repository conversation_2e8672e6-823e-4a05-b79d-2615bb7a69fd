/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

@import '~antd/lib/style/themes/default.less';

:global {
  .ant-card {
    margin: 20px 0;
  }
}

.col_f35216 {
  color: #f35216;
}
.data {
  :global {
    .ant-col-sm-16 {
      width: 100%;
    }
  }
}
.post_type_selected {
  display: inline-block;
  margin: 0 10px;
  padding: 5px 10px;
  color: #fff;
  background: #090;
  border: 1px solid #dedede;
  cursor: pointer;
}
.post_type_unselected {
  display: inline-block;
  margin: 0 10px;
  padding: 5px 10px;
  color: #333;
  border: 1px solid #dedede;
  cursor: pointer;
}
.select_date {
  display: inline-block;
  margin: 0 5px;
}
.example {
  margin: 20px 0;
  margin-bottom: 20px;
  padding: 30px 50px;
  text-align: center;
  border-radius: 4px;
}
.title {
  padding-top: 20px;
  padding-bottom: 20px;
  font-weight: 600;
  font-size: 18px;
  text-align: center;
}

.search-form {
  margin-top: 24px;
}

.type {
  height: 100%;
  color: @disabled-color;
  cursor: pointer;
  &:hover {
    color: @text-color;
    font-size: 16px;
    transition: all 0.2s ease-in-out;
  }
}

.checked {
  position: relative;
  color: @text-color;
  font-weight: bold;
  font-size: 16px;
  &::before {
    position: absolute;
    top: 25px;
    bottom: 0;
    width: 100%;
    height: 3px;
    background-color: @text-color;
    transform: scaleY(0.5);
    content: '';
  }
}

.total {
  font-weight: bold;
  font-size: 20px;
  text-align: right;
}
