/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Spin } from 'antd';
import { Chart, Geom, Axis, Tooltip, Coord, Label } from 'bizcharts';
import DataSet from '@antv/data-set';
import styles from './style.less';

const Pies = props => {
  const { piesList, numType, loading = false, title } = props;

  const { DataView } = DataSet;
  const dv = new DataView();
  dv.source(piesList).transform({
    type: 'percent',
    field: `${numType}`,
    dimension: 'brand',
    as: 'percent',
  });
  const cols = {
    percent: {
      formatter: vals => {
        vals = `${Math.round(vals * 1000) / 10}%`;
        return vals;
      },
    },
  };
  return (
    <Spin spinning={loading}>
      {title && <div className={styles.title}>{title}</div>}
      {piesList != '' ? (
        <Chart height={400} data={dv} scale={cols} forceFit padding={[50, 0, 50, 0]}>
          <Coord type="theta" innerRadius={0.75} radius={0.75} />
          <Axis name="percent" />
          <Tooltip
            showTitle={false}
            itemTpl="<li><span style=&quot;background-color:{color};&quot; class=&quot;g2-tooltip-marker&quot;></span>{name}: {value}</li>"
          />
          <Geom
            type="intervalStack"
            position="percent"
            color="brand"
            tooltip={[
              'brand*percent',
              (brand, percent) => {
                percent = `${Math.round(percent * 1000) / 10}%`;
                return {
                  name: brand,
                  value: percent,
                };
              },
            ]}
            style={{
              lineWidth: 1,
              stroke: '#fff',
            }}
          >
            <Label
              content={[
                'brand*percent',
                (brand, percent) => {
                  percent = `${Math.round(percent * 1000) / 10}%`;
                  return `${brand} ${percent}`;
                },
              ]}
              offset={20}
              textStyle={{
                rotate: 0,
                shadowBlur: 1,
                shadowColor: 'rgba(0, 0, 0, .45)',
              }}
            />
          </Geom>
        </Chart>
      ) : (
        <div className={styles.example}>暂无数据</div>
      )}
    </Spin>
  );
};

export default Pies;
