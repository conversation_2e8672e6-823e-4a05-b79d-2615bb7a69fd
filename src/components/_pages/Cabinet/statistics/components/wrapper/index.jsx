/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable consistent-return */
/* eslint-disable array-callback-return */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable no-use-before-define */
/* eslint-disable react/no-array-index-key */
import React, { useState, useCallback, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { Card, Row, Col, Radio, DatePicker } from 'antd';
import { useInViewport } from 'ahooks';
import { connect } from 'dva';
import DataSet from '@antv/data-set';
import moment from 'moment';
import { Brands, Yzs } from '@/components/Select';
import { isLegalData } from '@/utils/utils';
import Pies from '../pies';
import Charts from '../charts';
import KbTypographyText from '@/components/KbTypographyText';
import styles from './index.less';
import { debounce, noop } from 'lodash';

const RadioButton = Radio.Button;
const RadioGroup = Radio.Group;
const legends = {
  in_num: '入库数',
  out_rate: '出库率',
  out_num: '出库数',
  back_num: '退回数',
  sms_num: '短信数',
  complaintRate: '每周投诉率（万分比）',
  inStorageRate: '入库通知率（%）',
  outStorageRate: '3日出库率（%）',
  num: '订单',
  real_num: '实名订单',
  new_add: '新增粉丝',
  total: '累计粉丝',
  cnt: '收入',
};
const nameMapToState = {
  yesterdaData: ['yesterdaData'], // 昨日数据总览
  storageSummary: ['canvesList'], // 出入库统计
  brandPie: ['inBrandPieList', 'outBrandPieList', 'orderBrandPieList'], // 品牌占比
  regionalRanking: ['inNumRank', 'outRateRank'], // 区域排名 | 驿站排名
  storageRate: ['inStorageRateList', 'outStorageRateList'], // 入库通知率统计
  complaintRate: ['complaintRateList'], // 投诉率统计
  smsSummary: ['smsSummary'], // 短信统计
  orderSummary: ['orderSummary'], // 订单统计
  fansSummary: ['fansSummary'], // 微信粉丝统计
  dakOrEcIncome: ['dakOrEcIncome'], // 收入统计
};
const ds = new DataSet();

const { RangePicker } = DatePicker;
const disabledDate = current =>
  current &&
  current >
  moment()
    .endOf('day')
    .subtract(1, 'days');

// 从链接带入 cm_id
const checkHasCmIdFromQuery = (cm_id) => {
  return cm_id && cm_id !== 'all'
}

const Index = props => {
  const {
    title,
    name,
    numType,
    dispatch,
    isZyAccount,
    branchId: initBranchId = [],
    branch: initBranch = [],
    cols,
    chartType,
    transform,
    _length,
    chartTitle,
    showBrandSelect,
    showYzSelect,
    showDaySelect,
    cmIdFromQuery,
    showLegendsValue,
    showTotal,
    hideLegend,
    showDakAll,
    preloadDak = checkHasCmIdFromQuery(cmIdFromQuery),
    useFirstDak,
    numeralFormatter,
    area_id
  } = props;

  const [day, setDay] = useState('7');
  const [brand, setBrand] = useState('all');
  const [cm_id, setCmId] = useState(useFirstDak ? null : 'all');
  const [branchId, setBranchId] = useState(initBranchId);
  const [branch, setBranch] = useState(initBranch);
  const [code, setCode] = useState('');
  const [initCode, setInitCode] = useState('');
  const [reqYzPayload, setYzPayload] = useState(null);
  const [chartList, setChartList] = useState({});
  const [inView, setInView] = useState(false);
  const [date, setDate] = useState([]);

  const ref = useRef();
  const inViewPort = useInViewport(ref);

  const dakList = useRef({ list: [], trigger: noop });
  const requestRef = useRef({});

  // 收入统计：需要获取首个快递柜
  const getFirstOrFindYz = (dakItem) => {
    const IS_CABINET = reqYzPayload.is_ec == '1';
    return dakItem?.[IS_CABINET ? 'ec_id' : 'cm_id'];
  }

  // 拉取数据
  const getData = useCallback(
    debounce(
      (payload = {}) => {
        payload.code = code;
        if (name !== 'storageSummary') {
          delete payload.brand;
        }

        if (name === 'brandPie' || name === 'outPic') {
          payload.brand = 'all';
        }

        if (name == 'dakOrEcIncome') {
          delete payload.brand;
          delete payload.branchId;
          delete payload.code;
        }

        const payloadMerged = {
          ...requestRef.current.payload,
          ...payload
        };

        requestRef.current.payload = payloadMerged;

        dispatch({
          type: `cabinet_stat/${name}`,
          payload: payloadMerged,
        });
      },
      300,
      {
        leading: false,
        trailing: true
      }
    ),
    []
  );

  useEffect(
    () => {
      inViewPort && setInView(true);
    },
    [inViewPort],
  );

  useEffect(
    () => {
      const reqData = {
        day: 7,
        brand: 'all',
        cm_id: cmIdFromQuery,
        code: initCode,
      };
      setCmId(cmIdFromQuery);
      if (initCode) {
        if (name == 'regionalRanking') {
          reqData.day = 1;
          setDay('1');
        }
        inView && getData(reqData);
      }
    },
    [initCode, name, inView, cmIdFromQuery],
  );

  useEffect(
    () => {
      // 片区切换
      inView && getData({ area_id });
    },
    [area_id, inView],
  );

  useEffect(
    () => {
      let _code = '';
      if (!isZyAccount) {
        _code = [...branch].map(i => i.code && i.code).pop();
      } else {
        _code = [...branchId].pop();
      }
      setCode(_code);
    },
    [branch, branchId, isZyAccount],
  );

  useEffect(
    () => {
      // 通过监听来自state里面的列表的长度来更新图表中的数据
      const obj = {};
      nameMapToState[name].forEach((val, index) => {
        if (chartType === 'chart') {
          obj[val] = ds
            .createView()
            .source(isLegalData(props[val], []))
            .transform(transform[index]);
        } else {
          obj[val] = props[val];
        }
      });
      setChartList(obj);
    },
    [_length, chartType, name, transform, props],
  );

  useEffect(
    () => {
      let initCode = '';
      let yzPayload = null;
      if (isZyAccount) {
        initCode = [...initBranchId].pop();
        yzPayload = {
          branch_id: initCode,
          is_ec: '1',
        };
      } else {
        initCode = [...initBranch].map(i => i.code && i.code).pop();
        yzPayload = {
          code: initCode,
          area_id,
          is_ec: '1',
        }

      }
      setInitCode(initCode);
      setYzPayload(yzPayload);
      setBranchId(initBranchId);
      setBranch(initBranch);
    },
    [initBranchId, initBranch, isZyAccount, area_id],
  );

  // 快捷日期选择
  const onDayChange = useCallback(
    mode => {
      const selectDay = mode.target.value;
      setDay(selectDay);
      setDate([]);
      getData({
        day: selectDay,
        start: void (0),
        end: void (0)
      });
    },
    [],
  );

  // 自定义日期选择
  const onDateChange = useCallback(
    value => {
      setDay(null);
      setDate(value);
      getData({
        start: moment(value[0]).format('YYYY-MM-DD'),
        end: moment(value[1]).format('YYYY-MM-DD'),
        day: void (0)
      });
    },
    [],
  );

  // 品牌选择
  const onBrandChange = useCallback(
    value => {
      setBrand(value);
      getData({
        brand: value,
      });
    },
    [],
  );

  // 快递柜选择
  const onYzSelect = useCallback(
    options => {
      setCmId(options);
      getData({
        cm_id: options,
      });
    },
    [],
  );

  const onDakLoaded = l => {
    // 注意这个onLoad，只有第一次加载会触发，再次展开下拉框不会再触发；
    dakList.current.list = l;
    dakList.current.trigger();
    dakList.current.trigger = noop;
  };

  useEffect(
    () => {
      if (!inView || !reqYzPayload) return;
      const run = () => {
        if (checkHasCmIdFromQuery(cmIdFromQuery)) {
          // 外部带入的cm_id 需要更换为 ec_id;
          const item = dakList.current.list.find(item => item.cm_id === cmIdFromQuery);
          if (item) {
            onYzSelect(getFirstOrFindYz(item));
          }
          return;
        }
        if (useFirstDak) {
          onYzSelect(getFirstOrFindYz(dakList.current.list[0]));
        }
      }

      if (dakList.current.list.length === 0) {
        // 数据还未加载成功
        dakList.current.trigger = () => run();
      } else {
        run();
      }
    },
    [useFirstDak, inView, cmIdFromQuery],
  );

  return (
    <div ref={ref}>
      <Card style={{ minHeight: 400 }}>
        <KbTypographyText
          size="20"
          strong
          color="black"
          block
          cname={{ textAlign: 'center', marginBottom: 20 }}
        >
          {title}
        </KbTypographyText>
        <div className={styles.from}>
          {showDaySelect && (
            <Row type="flex" align="middle">
              <Col>
                <span>选择周期：</span>
              </Col>
              <Col>
                <RadioGroup onChange={onDayChange} value={day} name={name}>
                  {name === 'regionalRanking' && <RadioButton value="1">昨天</RadioButton>}
                  <RadioButton value="7">最近7天</RadioButton>
                  <RadioButton value="30">最近30天</RadioButton>
                </RadioGroup>
              </Col>
            </Row>
          )}

          {showDaySelect && (
            <Row type="flex" align="middle">
              <Col>
                <span>自定义时间：</span>
              </Col>
              <Col span={17}>
                <RangePicker
                  value={date}
                  placeholder={['开始日期', '结束日期']}
                  disabledDate={disabledDate}
                  onChange={onDateChange}
                />
              </Col>
            </Row>
          )}
          {showBrandSelect && (
            <Row type="flex" align="middle" style={{ width: '300px' }}>
              <Col>
                <span>选择品牌：</span>
              </Col>
              <Col span={18}>
                <Brands
                  onChange={onBrandChange}
                  value={brand}
                  showSearch
                  placeholder="请选择品牌"
                  style={{ width: '100%' }}
                />
              </Col>
            </Row>
          )}
          {showYzSelect && (
            <Row type="flex" align="middle" style={{ width: '300px' }}>
              <Col span={8}>
                <span>选择快递柜：</span>
              </Col>
              <Col span={16}>
                {
                  reqYzPayload && (
                    <Yzs
                      style={{ width: '100%' }}
                      reqPayload={reqYzPayload}
                      repeatRequest
                      value={cm_id}
                      onSelect={onYzSelect}
                      showAll={showDakAll}
                      preloadDak={preloadDak}
                      onLoaded={onDakLoaded}
                    />
                  )
                }
              </Col>
            </Row>
          )}
        </div>
        <Row type="flex">
          {nameMapToState[name].map((val, index, arr) => {
            if (chartList[val]) {
              switch (chartType) {
                case 'chart':
                  return (
                    <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24 / arr.length} key={index}>
                      <Charts
                        loading={props.loading}
                        cols={cols}
                        canvesList={chartList[val]}
                        legends={legends}
                        title={chartTitle[index]}
                        showLegendsValue={showLegendsValue}
                        showTotal={showTotal}
                        hideLegend={hideLegend}
                        numeralFormatter={numeralFormatter}
                      />
                    </Col>
                  );
                case 'pies':
                  return (
                    <Col xs={24} sm={24} md={24} lg={24} xl={24} xxl={24 / arr.length} key={index}>
                      <Pies
                        piesList={chartList[val]}
                        numType={numType}
                        loading={props.loading}
                        title={chartTitle[index]}
                      />
                    </Col>
                  );

                default:
                  break;
              }
            }
            return null;
          })}
        </Row>
      </Card>
    </div>
  );
};

Index.propTypes = {
  title: PropTypes.string, // 标题
  // eslint-disable-next-line max-len
  name: PropTypes.string.isRequired, // 该属性与状态管理中的effects名称相对应，storageSummary,brandPie,outPic,smsSummary,orderSummary,complaintRate,storageRate, fansSummary
  chartType: PropTypes.string.isRequired, // 图表类型，chart | pies | data
  isZyAccount: PropTypes.bool.isRequired, // 是否是中邮的账号
  branchId: PropTypes.array.isRequired, // 地区初始的id
  branch: PropTypes.array.isRequired, // 地区初始的下拉options
  cols: PropTypes.object, // 图表的col属性
  transform: PropTypes.array, // 图表数据处理的格式
  numType: PropTypes.string, // pies图表需要的字段
  chartTitle: PropTypes.array, // 图表里面的标题
  showDaySelect: PropTypes.bool,
  showBrandSelect: PropTypes.bool,
  showYzSelect: PropTypes.bool,
  cmIdFromQuery: PropTypes.string, // 驿站站点管理跳过来所带的驿站id
};

Index.defaultProps = {
  title: '',
  cols: {},
  transform: [],
  numType: 'num',
  chartTitle: [],
  showDaySelect: true,
  showBrandSelect: true,
  showYzSelect: true,
  cmIdFromQuery: '',
};

const CabinetCharts = connect(({ cabinet_stat, loading }, { name }) => {
  const listName = nameMapToState[name];
  const obj = {};
  // 按需添加对应的redux数据
  for (let i = 0; i < listName.length; i += 1) {
    const element = listName[i];
    obj._length = cabinet_stat[element];
    obj[element] = cabinet_stat[element];
    obj.loading = loading.effects[`cabinet_stat/${name}`];
  }
  return obj;
})(React.memo(Index));

export default CabinetCharts;
