/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Card, Col, Row } from 'antd';
import React, { useEffect } from 'react';
import { useRequest } from 'ahooks';
import KbTypographyText from '@/components/KbTypographyText';
import { useSelector } from 'dva';

import styles from './index.less';
import { getAreaEcCurrentDayStat } from '@/services/cabinet/star';

const CabinetDashBoard = (props) => {
  const { area_id } = props;
  const { screenToken: token } = useSelector(state => state.global);

  const { data = {}, run } = useRequest((req) => getAreaEcCurrentDayStat({ ...req, token }), { manual: true });

  const columns1 = [
    {
      label: '今日投件数',
      key: 'in_number',
    },
    {
      label: '今日取件数',
      key: 'pickUp',
    },
    {
      label: '今日寄件数',
      key: 'send_number',
    },
    {
      label: '今日总收入',
      key: 'profit',
    },
  ];
  const columns2 = [
    {
      label: '滞留件',
      key: 'delay',
    },
    {
      label: '今日通知失败',
      key: 'notice',
    },
    {
      label: '今日上传失败',
      key: 'upload',
    },
    {
      label: '禁用格口',
      key: 'shield',
    },
  ];

  useEffect(() => {
    if (!area_id) return;
    run({ area_id });
  }, [area_id])

  return (
    <Card>
      <KbTypographyText size="20" strong color="black" block cname={{ textAlign: 'center' }}>
        今日数据总览
      </KbTypographyText>
      <Row
        warp="nowrap"
        style={{ width: '100%', marginTop: 30 }}
        className={styles.row}
        justify="space-between"
      >
        <Col span={12}>
          <Row>
            {columns1.map(item => (
              <Col className={styles.item} span={6} key={item.key}>
                <KbTypographyText color="black" size="17">
                  {data[item.key] || 0}
                </KbTypographyText>
                <KbTypographyText color="black" size="14" storage>
                  {item.label}
                </KbTypographyText>
              </Col>
            ))}
          </Row>
        </Col>

        <Col span={12}>
          <Row>
            {columns2.map(item => (
              <Col className={styles.item} span={6} key={item.key}>
                <KbTypographyText color="link" size="17">
                  {data[item.key] || 0}
                </KbTypographyText>
                <KbTypographyText color="black" size="14" storage>
                  {item.label}
                </KbTypographyText>
              </Col>
            ))}
          </Row>
        </Col>
      </Row>
    </Card>
  );
};

export default CabinetDashBoard;
