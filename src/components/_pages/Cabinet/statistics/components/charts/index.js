/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { Component } from 'react';
import { Spin } from 'antd';
import { Chart, Geom, Axis, Tooltip, Legend } from 'bizcharts';
import { isBoolean } from 'lodash';
import styles from './style.less';
import { formatNumeral } from '@/utils/numeral';
import Space from '@/components/Space';

export default class Charts extends Component {
  render() {
    const {
      cols,
      canvesList,
      legends,
      title,
      loading = false,
      showLegendsValue,
      showTotal,
      hideLegend,
      numeralFormatter = '0,0',
    } = this.props;

    const total = canvesList.rows.reduce((pre, cur) => {
      return pre + (+cur.temperature || 0);
    }, 0);

    return (
      <Spin spinning={loading}>
        {title && <div className={styles.title}>{title}</div>}
        {!!showTotal && (
          <div className={styles.total}>
            {!isBoolean(showTotal) ? showTotal : '总数'} {formatNumeral(total, numeralFormatter)}
          </div>
        )}
        {canvesList.origin.length != 0 ? (
          <Chart
            onTooltipChange={ev => {
              ev.items.forEach(data => {
                data.name = legends ? legends[data.name] : data.name;
              });
            }}
            color="name"
            height={450}
            data={canvesList}
            scale={cols}
            forceFit
            padding={['auto', 'auto', 'auto', 'auto']}
          >
            {!hideLegend && (
              <Legend
                name="city"
                itemFormatter={text => {
                  const baseText = legends ? legends[text] : text;
                  if (!showLegendsValue) {
                    return baseText;
                  }
                  const data = canvesList.origin || [];
                  const _total = data.reduce((pre, cur) => {
                    return pre + +cur[text] || 0;
                  }, 0);
                  return baseText + ' ' + _total;
                }}
                textStyle={{
                  textAlign: 'middle', // 文本对齐方向，可取值为： start middle end
                  fill: '#404040', // 文本的颜色
                  fontSize: '18', // 文本大小
                  fontWeight: 'bold', // 文本粗细
                }}
                position="top-center"
              />
            )}
            <Axis name="date" />
            <Axis
              name="temperature"
              label={{
                formatter: val => `${val}`,
              }}
            />
            <Tooltip
              crosshairs={{
                type: 'y',
              }}
            />
            <Geom type="line" position="date*temperature" color="city" size={2} />
            <Geom
              type="point"
              position="date*temperature"
              size={4}
              shape="circle"
              color="city"
              style={{
                stroke: '#fff',
                lineWidth: 1,
              }}
            />
          </Chart>
        ) : (
          <div className={styles.example}>暂无数据</div>
        )}
      </Spin>
    );
  }
}
