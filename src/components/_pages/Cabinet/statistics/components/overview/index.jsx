/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useMemo } from 'react';
import { connect } from 'dva';
import CabinetCharts from '../wrapper';
import CabinetDashBoard from '../dashboard';
import { Spin } from 'antd';

const Index = props => {
  const { router, userInfo, area_id } = props;

  const initPage = (val) => {
    const { branch = [], branchLevel } = userInfo;
    const initBranchId = branch.filter(val => !!val).map(i => i.id);
    let kbCode = []; // 快宝驿站下拉需要的code参数
    let { init_branch } = val;
    let { init_branchId } = val;

    // 设置所属区域数据
    // 总公司
    if (branchLevel == 0 && branch.length == 0) {
      init_branch = [
        {
          id: '0',
          name: '中国邮政集团有限公司',
          level: '0',
          pid: '-2', // 自定义pid用来区分是否是单独属于总公司账号
        },
      ];
      init_branchId = ['0', '334']; // 中邮总司暂时默认选中浙江省
    } else {
      //  非总公司
      kbCode = [...branch].map(i => i.code && i.code).pop();
      init_branch = branch;
      init_branchId = initBranchId;
    }

    return {
      init_branch,
      init_branchId,
      cm_id: router.location.query.cm_id || 'all',
    };
  };

  const { init_branch, init_branchId, cm_id } = useMemo(() => initPage({
    cm_id: 'all',
    init_branchId: [], // 初始所属区域id
    init_branch: [], // 初始所属区域列表
  }), []);

  return (
    area_id
      ? <div>
        <CabinetDashBoard area_id={area_id} />
        <CabinetCharts
          title="出入库统计"
          name="storageSummary"
          chartType="chart"
          isZyAccount={false}
          branch={init_branch}
          branchId={init_branchId}
          cmIdFromQuery={cm_id}
          transform={[
            {
              type: 'fold',
              fields: ['in_num', 'out_num', 'back_num'],
              key: 'city',
              value: 'temperature',
            },
          ]}
          showLegendsValue
          showTotal
          area_id={area_id}
        />
        <CabinetCharts
          title="品牌占比"
          chartTitle={['入库品牌占比', '出库品牌占比', '寄件数品牌占比']}
          rankType={['in', 'out', 'order']}
          numType="num"
          name="brandPie"
          chartType="pies"
          showBrandSelect={false}
          isZyAccount={false}
          branch={init_branch}
          branchId={init_branchId}
          cmIdFromQuery={cm_id}
          area_id={area_id}
        />

        <CabinetCharts
          title="收入统计"
          name="dakOrEcIncome"
          chartType="chart"
          isZyAccount={false}
          branch={init_branch}
          branchId={init_branchId}
          cmIdFromQuery={cm_id}
          transform={[
            {
              type: 'fold',
              fields: ['cnt'],
              key: 'city',
              value: 'temperature',
            },
          ]}
          showLegendsValue
          showTotal="总收入"
          hideLegend
          showBrandSelect={false}
          showDakAll={false}
          useFirstDak
          preloadDak
          numeralFormatter='0,0.00'
          area_id={area_id}
        />

        <CabinetCharts
          title="订单统计"
          name="orderSummary"
          chartType="chart"
          showBrandSelect={false}
          isZyAccount={false}
          branch={init_branch}
          branchId={init_branchId}
          cmIdFromQuery={cm_id}
          transform={[
            {
              type: 'fold',
              fields: ['num'],
              key: 'city',
              value: 'temperature',
            },
          ]}
          area_id={area_id}
        />
      </div>
      : <Spin spinning />
  );
};

const StatisticsOverView = connect(({ loading, router, user, setting }) => ({
  userType: setting.options.key,
  router,
  shop_id: user.currentUser.shop_id,
  userInfo: user.currentUser.user_info,
  operatoring: loading.effects['list/getBrandsList'],
  loading: loading.effects['list/getPostList'],
}))(React.memo(Index));

export default StatisticsOverView;
