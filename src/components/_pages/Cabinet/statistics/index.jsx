/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { connect } from 'dva';
import { Col, Row } from 'antd';
import { Card } from 'antd';
import React, { useState } from 'react';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import DispatchPianqu from '@/components/_pages/Post/Area/DispatchPianqu';
import StatisticsOverView from './components/overview';

const Index = () => {
  const [areaId, setAreaId] = useState('');
  const handlePianquChange = val => setAreaId(val);

  return (
    <PageHeaderLayout
      title={
        <Row type="flex" justify="space-between">
          <Col>下属快递柜统计数据</Col>
          <Col>
            <DispatchPianqu type="inn" onChange={handlePianquChange} />
          </Col>
        </Row>
      }
    >
      <Card>
        <StatisticsOverView area_id={areaId} />
      </Card>
    </PageHeaderLayout>
  );
};

const CabinetStatistics = connect(({ user, list, setting }) => ({
  list,
  userInfo: user.currentUser.user_info,
  ...setting,
}))(React.memo(Index));

export default CabinetStatistics;
