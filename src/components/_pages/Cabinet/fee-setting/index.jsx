/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useState } from 'react';
import { connect } from 'dva';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import StandardTab from '@/components/StandardTab';
import styles from './style.less';
import CabinetSetTake from './components/take';
import CabinetSetOver from './components/over';
import CabinetSetInStore from './components/in_store';
import { checkAuthorized } from '@/components/Authorized/AuthorizedExtend';

const Index = props => {
  const [activeKey, setActiveKey] = useState('serviceIn');

  const panes = [
    {
      tab: '快递柜保管费设置',
      component: <CabinetSetTake {...props} />,
      key: 'serviceSettings',
    },
    {
      tab: '超时收费设置',
      component: <CabinetSetOver {...props} />,
      key: 'serviceRecord',
    },
    {
      tab: '入库服务费',
      component: <CabinetSetInStore />,
      key: 'serviceIn',
      hidden: checkAuthorized({ auth: 'kdg_inStorage-0' }),
    },
  ].filter(item => !item.hidden);

  return (
    <PageHeaderLayout title="区域内快递柜">
      <div className={styles.main}>
        <div style={{ paddingBottom: '24px', position: 'relative' }}>
          <StandardTab
            style={{ marginBottom: 24 }}
            onChange={setActiveKey}
            activeKey={activeKey}
            name="dispat"
            panes={panes}
            destroyInactiveTabPane
          />
        </div>
      </div>
    </PageHeaderLayout>
  );
};

const CabinetSet = connect(({ user }) => ({
  currentUser: user.currentUser,
}))(Index);

export default CabinetSet;
