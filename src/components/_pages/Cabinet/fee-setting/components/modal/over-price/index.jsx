/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, Checkbox, Col, Form, Input, Row, Switch } from 'antd';
import React from 'react';
import { useOverPriceForm } from './useOverPriceForm';

const { Item: FormItem } = Form;

const OverPriceForm = Form.create({
  onValuesChange: () => {},
})(props => {
  const { form } = props;
  const { getFieldDecorator } = form || {};

  const { columns, onSubmit, showWeekendType, setShowWeekendType } = useOverPriceForm(props);

  return (
    <Form form={form} labelAlign="right">
      <Row>
        <Col span={12}>
          {columns.map(item => (
            <FormItem labelCol={{ span: 5 }} key={item.name} label={item.label} name={item.name}>
              {getFieldDecorator(item.name, {
                rules: [
                  {
                    required: true,
                    message: '请输入',
                  },
                  {
                    validator: (_, value) => {
                      if (value) {
                        if (!/^(?:[1-9]\d{0,1}|0)(?:\.\d{1,2})?$/.test(value)) {
                          return Promise.reject(new Error('请输入有效数字,最多保留两位小数'));
                        }
                        const num = parseFloat(value);
                        if (num < 0) return Promise.reject(new Error('最小值为0'));
                        if (num > 99) return Promise.reject(new Error('最大值不能超过99'));
                      }
                      return Promise.resolve();
                    },
                  },
                ],
              })(
                <Input
                  style={{ width: 200 }}
                  min={0}
                  max={99}
                  placeholder="请输入"
                  addonAfter={item.after}
                />,
              )}
            </FormItem>
          ))}

          <FormItem
            labelCol={{ span: 13 }}
            labelAlign="left"
            label="节假日不累计超时时间"
            name="weekend"
            help="开启表示节假日不会累计超时时间，节假日为周六、周日和法定节假日"
          >
            {getFieldDecorator('weekend', { valuePropName: 'checked' })(
              <Switch onChange={setShowWeekendType} />,
            )}
          </FormItem>
          {showWeekendType && (
            <FormItem labelAlign="left" name="weekend_type">
              {getFieldDecorator('weekend_type', {
                rules: [
                  {
                    required: true,
                    message: '请选择类型',
                  },
                ],
              })(
                <Checkbox.Group style={{ width: '100%' }}>
                  <Checkbox value="weekend_free">周六、周日</Checkbox>
                  <Checkbox value="holiday_free">法定节假日</Checkbox>
                </Checkbox.Group>,
              )}
            </FormItem>
          )}
        </Col>
      </Row>

      <Row gutter={[8, 8]} align="middle" justify="center">
        <Col span={10} style={{ display: 'flex', justifyContent: 'center' }}>
          <Button type="primary" onClick={onSubmit}>
            保存
          </Button>
        </Col>
      </Row>
    </Form>
  );
});

export default OverPriceForm;
