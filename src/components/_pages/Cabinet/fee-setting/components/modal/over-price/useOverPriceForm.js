/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useState, useCallback } from 'react';
import { getCabinetOutTimeFee, setCabinetOutTimeFee } from '@/services/cabinet/fee';
import {
  WEEKEND_TYPES,
  formatOverPriceFormRequest,
  OVER_PRICE_FORM_ITEMS,
} from '../../_utils/utils';

export function useOverPriceForm(props) {
  const { form, pageData, onCloseModal } = props;

  const { cm_id, isMultiple, onReload } = pageData;

  const [showWeekendType, setShowWeekendType] = useState(false);

  // 使用useCallback包装稳定回调
  const getFormValues = useCallback(
    () => {
      return new Promise(resolve => {
        if (!form) {
          resolve(null);
          return;
        }

        form.validateFields((err, val) => {
          if (err) return;
          const { weekend, weekend_type = [], ...rest } = val;
          resolve({
            rate_details: rest,
            holiday_free: weekend_type.includes(WEEKEND_TYPES.HOLIDAY) ? '1' : '0',
            weekend_free: weekend_type.includes(WEEKEND_TYPES.WEEKEND) ? '1' : '0',
          });
        });
      });
    },
    [form],
  );

  const onSubmit = useCallback(
    async () => {
      try {
        const params = await getFormValues();
        const status = await setCabinetOutTimeFee({
          cm_ids: cm_id,
          ...params,
        });
        if (status) {
          onReload();
          onCloseModal();
        }
      } catch (error) {
        console.error('Submit failed:', error);
      }
    },
    [getFormValues],
  );

  const initForm = useCallback(
    async () => {
      try {
        const options = await getCabinetOutTimeFee({ cm_id });
        const { weekend, ...rest } = formatOverPriceFormRequest(options);
        setShowWeekendType(weekend);
        form.setFieldsValue({
          weekend,
          ...rest,
        });
      } catch (error) {
        console.error('Initialize form failed:', error);
      }
    },
    [form, OVER_PRICE_FORM_ITEMS],
  );

  useEffect(
    () => {
      if (cm_id && !isMultiple) {
        initForm();
      }
    },
    [cm_id, isMultiple],
  );

  return {
    columns: OVER_PRICE_FORM_ITEMS,
    onSubmit,
    showWeekendType,
    setShowWeekendType,
  };
}
