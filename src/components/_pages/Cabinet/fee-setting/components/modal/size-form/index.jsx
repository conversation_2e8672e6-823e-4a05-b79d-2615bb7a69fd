/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Button, Col, Form, InputNumber, Row } from 'antd';
import React from 'react';
import { useSizePriceForm } from './useSizePriceForm';
import KbTypographyText from '@/components/KbTypographyText';

const { Item: FormItem } = Form;

const SizePriceForm = Form.create({
  onValuesChange: () => {},
})(props => {
  const { form, pageData } = props;
  const { getFieldDecorator } = form || {};

  const { is_view } = pageData;

  const { priceType, gridSizeOptions, onSubmit } = useSizePriceForm(props);

  return (
    <Form form={form} labelAlign="left">
      <Row>
        {priceType.map(priceKey => (
          <Col key={priceKey} span={10}>
            {priceKey == 'amount' && (
              <div style={{ marginBottom: 20 }}>
                <KbTypographyText color="black" size="17">
                  直接投递时收费标准
                </KbTypographyText>
              </div>
            )}

            {priceKey == 'deposit' && (
              <div style={{ marginBottom: 20 }}>
                <KbTypographyText color="black" size="17">
                  预约收费标准
                </KbTypographyText>
                <KbTypographyText color="light" size="13">
                  （预约为提前两小时）
                </KbTypographyText>
              </div>
            )}

            {gridSizeOptions.map(item => (
              <FormItem
                labelCol={{ span: 9 }}
                key={`${priceKey}_${item.value}`}
                label={item.label + '（元/个）'}
                name={`${priceKey}_${item.value}`}
              >
                {getFieldDecorator(`${priceKey}_${item.value}`, {
                  rules: [
                    {
                      required: true,
                      message: '请输入价格',
                    },
                  ],
                })(
                  <InputNumber
                    style={{ width: 200 }}
                    min={0}
                    max={10}
                    placeholder="请输入"
                    addonAfter="元/个"
                    precision={2}
                    disabled={is_view}
                  />,
                )}
              </FormItem>
            ))}
          </Col>
        ))}
      </Row>
      {!is_view && (
        <Row gutter={[8, 8]} align="middle" justify="center">
          <Col span={20} style={{ display: 'flex', justifyContent: 'center' }}>
            <Button type="primary" onClick={onSubmit}>
              提交
            </Button>
          </Col>
        </Row>
      )}
    </Form>
  );
});

export default SizePriceForm;
