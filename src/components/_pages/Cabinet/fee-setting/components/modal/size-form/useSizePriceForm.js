/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useMemo, useCallback } from 'react';
import { getCabinetSizePrice, setCabinetSizePrice } from '@/services/cabinet/fee';

export function useSizePriceForm(props) {
  const { form, pageData, onCloseModal } = props;

  const { cm_id, isMultiple, onReload } = pageData;

  // 缓存静态配置数据
  const gridSizeOptions = useMemo(
    () => [
      { label: '最小格口', value: '7' },
      { label: '微格口', value: '1' },
      { label: '小格口', value: '2' },
      { label: '中格口', value: '3' },
      { label: '大格口', value: '4' },
      { label: '超大格口', value: '5' },
      { label: '最大格口', value: '6' },
    ],
    [],
  );

  const priceType = useMemo(() => ['amount', 'deposit'], []);

  const getFormValues = useCallback(
    () => {
      return new Promise((resolve, reject) => {
        if (!form?.validateFields) {
          reject(new Error('Form instance missing'));
          return;
        }

        form.validateFields((err, val) => {
          if (err) {
            reject(err);
            return;
          }

          const obj = gridSizeOptions.reduce((acc, { value }) => {
            acc[value] = priceType.reduce((typeAcc, key) => {
              typeAcc[key] = `${val[`${key}_${value}`]}`;
              return typeAcc;
            }, {});
            return acc;
          }, {});

          resolve(obj);
        });
      });
    },
    [form, gridSizeOptions, priceType],
  );

  const onSubmit = async () => {
    const params = await getFormValues();
    const status = await setCabinetSizePrice({
      cm_ids: cm_id,
      price: params,
    });
    if (status) {
      onReload();
      onCloseModal();
    }
  };

  const initForm = async () => {
    const options = await getCabinetSizePrice({ cm_id });
    const values = gridSizeOptions.reduce((acc, { value }) => {
      const item = options.find(i => i.size === value) || {};
      priceType.forEach(key => {
        acc[`${key}_${value}`] = item[key];
      });
      return acc;
    }, {});

    form.setFieldsValue(values);
  };

  useEffect(
    () => {
      if (cm_id && !isMultiple) {
        initForm();
      }
    },
    [cm_id, isMultiple],
  );

  return {
    gridSizeOptions,
    priceType,
    onSubmit,
  };
}
