/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Tabs } from 'antd';
import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { useSetState } from 'ahooks';
import { ModalForm } from '@/components/AdaptForm';
import SizePriceForm from './size-form';
import BrandPriceForm from './brand-form';
import OverPriceForm from './over-price';
import OverBrandPriceForm from './over-brand-form';
import RakePriceForm from './rake-form';
import KbTypographyText from '@/components/KbTypographyText';

// 定义常量避免魔法字符串
const COM_KEY_TAKE = 'take';
const COM_KEY_OVER_PRICE = 'over_price';
const COM_KEY_RAKE = 'rake';

const DEFAULT_PAGE_DATA = {
  cm_id: '',
  isMultiple: false,
  isShow: false,
  onReload: () => {},
  title: '',
  comKey: '', // take | over_price | rake
  is_view: false,
};

const CabinetSettingModal = forwardRef((props, ref) => {
  const [pageData, updatePageData] = useSetState(DEFAULT_PAGE_DATA);

  const { isShow, title, comKey, cabinet_name, isMultiple } = pageData;

  useImperativeHandle(ref, () => ({
    updatePageData,
  }));
  const onCloseModal = () => {
    updatePageData(DEFAULT_PAGE_DATA);
  };

  // 使用 useMemo 缓存 Tabs 配置
  const tabConfig = useMemo(
    () => {
      const config = {
        [COM_KEY_TAKE]: [
          {
            key: '1',
            tab: '默认收费',
            component: <SizePriceForm {...props} pageData={pageData} onCloseModal={onCloseModal} />,
          },
          {
            key: '2',
            tab: '按快递品牌折扣设置',
            component: <BrandPriceForm pageData={pageData} onCloseModal={onCloseModal} />,
          },
        ],
        [COM_KEY_OVER_PRICE]: [
          {
            key: '3',
            tab: '默认收费',
            component: <OverPriceForm pageData={pageData} onCloseModal={onCloseModal} />,
          },
          {
            key: '4',
            tab: '按快递品牌超时费设置',
            component: <OverBrandPriceForm pageData={pageData} onCloseModal={onCloseModal} />,
          },
        ],
      };

      return config[comKey] || [];
    },
    [comKey, pageData, onCloseModal],
  );

  return (
    <ModalForm
      visible={isShow}
      title={title}
      modalProps={{
        destroyOnClose: true,
        centered: true,
        footer: null,
        onCancel: onCloseModal,
      }}
      layout="horizontal"
      submitter={{ render: () => null }}
      width={1000}
      style={{ padding: 0 }}
    >
      <div style={{ marginBottom: 30 }}>
        <KbTypographyText color="black" size="17">
          快递柜：
          {isMultiple ? '全部快递柜' : cabinet_name}
        </KbTypographyText>
      </div>
      {comKey === COM_KEY_RAKE ? (
        <RakePriceForm pageData={pageData} onCloseModal={onCloseModal} />
      ) : tabConfig.length > 0 ? (
        <Tabs type="card">
          {tabConfig.map(({ key, tab, component }) => (
            <Tabs.TabPane key={key} tab={tab}>
              {component}
            </Tabs.TabPane>
          ))}
        </Tabs>
      ) : null}
    </ModalForm>
  );
});

export default CabinetSettingModal;
