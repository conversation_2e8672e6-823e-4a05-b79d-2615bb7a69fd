/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Checkbox, Switch } from 'antd';
import { Col, InputNumber, Row } from 'antd';
import React from 'react';
import Space from '@/components/Space';
import { OVER_PRICE_FORM_ITEMS } from '../../_utils/utils';

const OverBrandFormPrice = props => {
  const { onChange, itemKey, data = {} } = props;

  const onFormChange = (val, key) => {
    onChange(
      {
        ...data,
        [key]: val,
      },
      itemKey,
    );
  };

  return (
    <>
      <Row gutter={[12, 12]}>
        {OVER_PRICE_FORM_ITEMS.map(item => (
          <Col span={12} key={item.name}>
            <Row align="middle" type="flex">
              <Col span={8}>{item.label}</Col>
              <Col span={10}>
                <InputNumber
                  placeholder="请输入"
                  value={data[item.name]}
                  onChange={val => onFormChange(val, item.name)}
                  min={0}
                  max={99}
                />
              </Col>
              <Col offset={1}>{item.after}</Col>
            </Row>
          </Col>
        ))}
      </Row>
      <Row gutter={[12, 12]}>
        <Col span={12}>
          <Space align="left">
            <span>节假日不累计超时时间</span>
            <Switch checked={data.weekend} onChange={val => onFormChange(val, 'weekend')} />
          </Space>
        </Col>
        <Col span={12}>
          {data.weekend && (
            <Checkbox.Group
              style={{ width: '100%' }}
              value={data.weekend_type}
              onChange={val => onFormChange(val, 'weekend_type')}
            >
              <Checkbox value="weekend_free">周六、周日</Checkbox>
              <Checkbox value="holiday_free">法定节假日</Checkbox>
            </Checkbox.Group>
          )}
        </Col>
      </Row>
    </>
  );
};

export default OverBrandFormPrice;
