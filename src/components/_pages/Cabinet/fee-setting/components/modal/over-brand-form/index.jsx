/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react/jsx-no-bind */
import { Button, Col, Row, Select, Switch, Table } from 'antd';
import React from 'react';
import { Popconfirm } from 'antd';
// import { InputNumber } from 'antd';
import Space from '@/components/Space';
import { useOverBrandPriceForm } from './useOverBrandPriceForm';
import OverBrandFormPrice from './price';

const OverBrandPriceForm = props => {
  const {
    dataList,
    brandOptions,
    onMinus,
    addColumns,
    onRateChange,
    onBrandChange,
    onSubmit,
    open,
    setOpen,
  } = useOverBrandPriceForm(props);

  const columns = [
    {
      title: '指定品牌',
      dataIndex: 'brand',
      width: 300,
      align: 'center',
      render: (value, item) => {
        const options = brandOptions.filter(i => (i.key ? i.key == item.key : true));
        return (
          <Select
            value={value}
            onChange={val => onBrandChange(val, item.key)}
            style={{ width: '90%' }}
            mode="multiple"
            placeholder="请选择品牌"
          >
            {options.map(brand => (
              <Select.Option value={brand.value}>{brand.label}</Select.Option>
            ))}
          </Select>
        );
      },
    },
    {
      title: '收费方式',
      dataIndex: 'rate',
      width: 500,
      align: 'center',
      render: (_, item = {}) => (
        <OverBrandFormPrice data={item.rate} itemKey={item.key} onChange={onRateChange} />
      ),
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'price',
      width: 150,
      render: (_, item) => {
        return (
          <Space>
            <Popconfirm title="确定删除该配置？" onConfirm={onMinus.bind(null, item.key)}>
              <a>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Row type="flex" justify="space-between" align="middle">
        <Col>
          <Space>
            <span>按快递品牌折扣设置</span>
            <Switch checked={open} onChange={setOpen} />
          </Space>
        </Col>
        <Col>
          {open && (
            <Button key="add" type="primary" onClick={addColumns}>
              添加一条新折扣
            </Button>
          )}
        </Col>
      </Row>
      {open && (
        <Table
          rowKey="key"
          style={{ margin: '20px 0' }}
          columns={columns}
          dataSource={dataList}
          pagination={false}
        />
      )}
      <Row type="flex" justify="center">
        <Col>
          <Button key="add" type="primary" onClick={onSubmit}>
            保存配置
          </Button>
        </Col>
      </Row>
    </div>
  );
};

export default OverBrandPriceForm;
