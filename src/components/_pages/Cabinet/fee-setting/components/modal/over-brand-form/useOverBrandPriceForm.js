/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-restricted-syntax */
import { useState, useEffect, useMemo } from 'react';
import { message } from 'antd';
import {
  getCabinetBrandList,
  getCabinetBrandOutTimeFee,
  setCabinetBrandOutTimeFee,
} from '@/services/cabinet/fee';
import { randomCode } from '@/utils/utils';
import { getBrandsList } from '@/services/api';
import {
  OVER_PRICE_FORM_ITEMS,
  WEEKEND_TYPES,
  formatOverPriceFormRequest,
} from '../../_utils/utils';

export function useOverBrandPriceForm(props) {
  const { pageData, onCloseModal } = props;

  const { cm_id, isMultiple, onReload } = pageData;

  const [open, setOpen] = useState(false);
  const [dataList, setDataList] = useState([]);
  const [brandList, setBrandList] = useState([]);

  // 通用更新函数
  const createUpdateHandler = field => (val, key) => {
    setDataList(values =>
      values.map(item => ({
        ...item,
        [field]: item.key === key ? val || (field === 'rate' ? 0 : []) : item[field],
      })),
    );
  };

  const onMinus = async key => {
    setDataList(val => val.filter(i => i.key !== key));
  };

  const addColumns = () => setDataList(val => [...val, { key: randomCode() }]);

  const onRateChange = createUpdateHandler('rate');
  const onBrandChange = createUpdateHandler('brand');

  const initPage = async () => {
    try {
      const data = await getCabinetBrandOutTimeFee({ cm_id });
      const { brand_list: list_ } = data;
      const list = Array.isArray(list_) ? list_ : [];
      const formatList = list.map(item => {
        const { brand_list, id } = item;
        return {
          id,
          key: randomCode(),
          brand: brand_list || [],
          rate: formatOverPriceFormRequest(item),
        };
      });
      setOpen(data.status == '1');
      setDataList(formatList);
    } catch (error) {
      console.error('初始化页面数据失败:', error);
    }
  };

  const initBrandList = async () => {
    try {
      const { data: brands = [] } = await getBrandsList();
      const cabinet_brand = await getCabinetBrandList({ cm_id });
      const validBrands = Array.isArray(cabinet_brand) ? cabinet_brand : [];

      const formatList = brands.filter(i => validBrands.includes(i.brand_en)).map(item => ({
        label: item.brand,
        value: item.brand_en,
      }));

      setBrandList(formatList);
    } catch (error) {
      console.error('初始化品牌列表失败:', error);
    }
  };

  // 初始化副作用：加载品牌数据和折扣配置
  useEffect(
    () => {
      if (cm_id && !isMultiple) {
        initPage(); // 单柜机模式加载折扣配置
      }
      initBrandList(); // 始终加载品牌列表
    },
    [cm_id, isMultiple],
  );

  const brandOptions = useMemo(
    () => {
      const brandMap = new Map(dataList.flatMap(item => item.brand?.map(b => [b, item.key]) || []));

      return brandList.map(item => ({
        ...item,
        key: brandMap.get(item.value) || '',
      }));
    },
    [brandList, dataList],
  );

  const checkData = () => {
    const errors = [];
    dataList.forEach((item, index) => {
      const prefix = `第${index + 1}条`;
      const { brand = [], rate = {} } = item;

      if (!brand.length) {
        errors.push(`${prefix} - 请选择折扣品牌`);
      }

      OVER_PRICE_FORM_ITEMS.forEach(({ name, label }) => {
        if (rate[name] == null) {
          errors.push(`${prefix} ${label}不能为空`);
        }
      });
    });

    if (errors.length > 0) {
      message.error(errors.join('\n'));
      return Promise.reject(new Error('校验失败'));
    }
    return Promise.resolve();
  };

  const formatRequestParams = () => {
    const list = dataList.map(item => {
      const { brand, rate } = item;
      const { weekend, weekend_type = [], ...rest } = rate || {};
      return {
        brand_list: brand,
        rate_details: rest,
        holiday_status: weekend,
        holiday_free: weekend && weekend_type.includes(WEEKEND_TYPES.HOLIDAY) ? '1' : '0',
        weekend_free: weekend && weekend_type.includes(WEEKEND_TYPES.WEEKEND) ? '1' : '0',
      };
    });

    return {
      status: open ? '1' : '0',
      list,
    };
  };

  const onSubmit = async () => {
    try {
      await checkData();
      const params = formatRequestParams();
      const status = await setCabinetBrandOutTimeFee({
        cm_ids: cm_id,
        ...params,
      });
      if (status) {
        onReload();
        onCloseModal();
      }
    } catch (error) {
      // 错误已由 checkData 处理
    }
  };

  return {
    dataList,
    brandOptions,
    onMinus,
    addColumns,
    onRateChange,
    onBrandChange,
    onSubmit,
    open,
    setOpen,
  };
}
