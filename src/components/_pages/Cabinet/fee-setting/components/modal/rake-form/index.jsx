/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Alert, Button, Col, DatePicker, Input, InputNumber, Radio, Row } from 'antd';
import React from 'react';
import KbTypographyText from '@/components/KbTypographyText';
import { useRakePriceForm } from './useRakePriceForm';
import { connect } from 'dva';
import moment from 'moment';

// 常量提取到组件外部
const COL_SPAN = 13;
const ITEM_LABEL = 4;
const ITEM_CONTENT = 18;
const AMOUNT_PRECISION = 2; // 金额保留2位小数
const PERCENT_MAX = 100; // 百分比最大值100

// 表单字段组件
const FormField = ({ label, children }) => (
  <Row gutter={[20, 20]} type="flex" align="middle">
    <Col span={ITEM_LABEL}>
      <KbTypographyText color="black" size="14">
        {label}
      </KbTypographyText>
    </Col>
    <Col span={ITEM_CONTENT}>{children}</Col>
  </Row>
);

const Index = props => {
  const { currentUser } = props;
  const { user_info } = currentUser;
  const { cabinet_timeout_fee_type } = user_info || {};
  const { pageData, updatePageData, onSubmit } = useRakePriceForm(props);

  // 动态显示逻辑
  const showRake = pageData.timeout_user == '1';
  const showAmount = pageData.type === '1';
  const showRatio = pageData.type === '2';

  const disabledDate = current =>
    current &&
    current <
    moment()
      .endOf('day')
      .subtract(0, 'days');

  return (
    <div style={{ height: 320 }}>
      <Alert
        style={{ margin: '10px 0' }}
        message={
          <>
            {
              !showRake
                ? (
                  <>
                    <div>1、保存后超时费直接支付到加盟商可提现账户，驿站管理员不会看到超时费流水</div>
                    <div>2、更换超时费收款账户后实时生效</div>
                  </>
                )
                : (
                  <>
                   <div>超时费分成次日打款到加盟商可提现账户</div>
                  </>
                )
            }
          </>
        }
        type="warning"
      />
      <Row gutter={[20, 20]} style={{ marginTop: 20 }}>
        {/* 收款对象 */}
        <Col span={COL_SPAN}>
          <FormField label="收款对象">
            <Row type="flex" align="middle" gutter={[20, 20]}>
              <Col span={12}>
                <Radio.Group
                  value={`${pageData.timeout_user || ''}`}
                  onChange={e => updatePageData({ timeout_user: e.target.value })}
                >
                  <Radio value="1">驿站</Radio>
                  {cabinet_timeout_fee_type == '1' && <Radio value="2">服务商</Radio>}
                </Radio.Group>
              </Col>
            </Row>
          </FormField>
        </Col>
        {showRake && (
          <>
            {/* 抽成模式 */}
            <Col span={COL_SPAN}>
              <FormField label="抽成模式">
                <Radio.Group
                  value={pageData.type}
                  onChange={e => updatePageData({ type: e.target.value })}
                >
                  <Radio value="0">不抽成</Radio>
                  <Radio value="1">固定金额</Radio>
                  <Radio value="2">按比例</Radio>
                </Radio.Group>
              </FormField>
            </Col>

            {/* 动态显示金额/比例输入 */}
            {showAmount && (
              <Col span={COL_SPAN}>
                <FormField label="抽成金额">
                  <Row type="flex" align="middle">
                    <Col span={8}>
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入金额"
                        min={0}
                        max={10}
                        precision={AMOUNT_PRECISION}
                        addonAfter="元"
                        value={pageData.price}
                        onChange={value => updatePageData({ price: value })}
                      />
                    </Col>
                    <Col offset={1}>元</Col>
                  </Row>
                </FormField>
              </Col>
            )}

            {showRatio && (
              <Col span={COL_SPAN}>
                <FormField label="抽成比例">
                  <Row type="flex" align="middle">
                    <Col span={8}>
                      <InputNumber
                        style={{ width: '100%' }}
                        placeholder="请输入比例"
                        min={0}
                        max={PERCENT_MAX}
                        precision={2}
                        addonAfter="%"
                        value={pageData.radio}
                        onChange={value => updatePageData({ radio: value })}
                      />
                    </Col>
                    <Col offset={1}>%</Col>
                  </Row>
                </FormField>
              </Col>
            )}

            {/* 抽成期限 */}
            {(showAmount || showRatio) && (
              <Col span={COL_SPAN}>
                <FormField label="抽成期限">
                  <Row gutter={[10, 10]} type="flex" align="middle">
                    <Col span={8}>
                      <Input value="指定时间段" disabled />
                    </Col>
                    <Col span={16}>
                      <DatePicker.RangePicker
                        disabledDate={disabledDate}
                        value={pageData.date}
                        onChange={dates => updatePageData({ date: dates })}
                      />
                    </Col>
                  </Row>
                </FormField>
              </Col>
            )}
          </>
        )}
      </Row>

      {/* 提交按钮 */}
      <Row align="middle" justify="center">
        <Col span={13}>
          <Col offset={4} style={{ paddingTop: 12, paddingLeft: 2 }}>
            <Button type="primary" onClick={onSubmit}>
              保存配置
            </Button>
          </Col>
        </Col>
      </Row>
    </div>
  );
};

const RakePriceForm = connect(({ user }) => ({
  currentUser: user.currentUser,
}))(Index);

export default RakePriceForm;
