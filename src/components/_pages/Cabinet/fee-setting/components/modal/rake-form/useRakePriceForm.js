/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-restricted-globals */
import { useEffect } from 'react';
import { useSetState } from 'ahooks';
import moment from 'moment';
import { message } from 'antd';
import { getCabinetOutTimeRake, setCabinetOutTimeRake } from '@/services/cabinet/fee';

export function useRakePriceForm(props) {
  const { pageData: p_pageData, onCloseModal } = props;

  const { cm_id, isMultiple, onReload } = p_pageData;

  const CONFIG_TYPES = {
    NONE: '0',
    FIXED: '1',
    PERCENTAGE: '2',
  };

  const [pageData, updatePageData] = useSetState({
    timeout_user: '1',
    type: CONFIG_TYPES.NONE,
    price: null,
    radio: null,
    date: [],
  });

  const initPage = async () => {
    const options = await getCabinetOutTimeRake({ cm_id });
    const { timeout_user = '1', timeout_config } = options;
    const { type, data, start, end } = timeout_config || {};

    const date =
      start && end
        ? [
            moment(start).isValid() ? moment(start) : null,
            moment(end).isValid() ? moment(end) : null,
          ].filter(Boolean)
        : [];

    updatePageData({
      timeout_user: `${timeout_user}`,
      type: type || CONFIG_TYPES.NONE,
      price: type === CONFIG_TYPES.FIXED ? Number(data) : null,
      radio: type === CONFIG_TYPES.PERCENTAGE ? Number(data) : null,
      date,
    });
  };

  useEffect(
    () => {
      if (cm_id && !isMultiple) {
        initPage();
      }
    },
    [cm_id, isMultiple],
  );

  const validateParams = () => {
    const errors = [];
    const { type, price, radio, date = [] } = pageData;

    if (type !== CONFIG_TYPES.NONE) {
      if (type === CONFIG_TYPES.FIXED && (isNaN(price) || price <= 0)) {
        errors.push('请输入有效金额');
      }
      if (type === CONFIG_TYPES.PERCENTAGE && (isNaN(radio) || radio < 0 || radio > 100)) {
        errors.push('请输入0-100之间的比例');
      }
      if (date.length !== 2 || !date.every(m => m?.isValid())) {
        errors.push('请选择有效时间范围');
      }
    }

    if (errors.length > 0) {
      message.error(errors.join('，'));
      return false;
    }
    return true;
  };

  const formatDate = date => {
    return date?.isValid() ? date.format('YYYY-MM-DD') : '';
  };

  const onFormatRequestParams = async () => {
    if (!validateParams()) return Promise.reject();

    const { type, price, radio, date = [], timeout_user } = pageData;
    const dataValue = {
      [CONFIG_TYPES.FIXED]: price,
      [CONFIG_TYPES.PERCENTAGE]: radio,
      [CONFIG_TYPES.NONE]: '',
    }[type];

    return {
      cm_ids: cm_id,
      timeout_user,
      timeout_config: {
        type,
        data: dataValue,
        start: formatDate(date[0]),
        end: formatDate(date[1]),
      },
    };
  };

  const onSubmit = async () => {
    try {
      const params = await onFormatRequestParams();
      const status = await setCabinetOutTimeRake(params);
      if (status) {
        onReload();
        onCloseModal();
      }
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  return {
    pageData,
    updatePageData,
    onSubmit,
  };
}
