/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react/jsx-no-bind */
import { Button, Col, Icon, Row, Select, Switch, Table, Tooltip } from 'antd';
import React from 'react';
import { Popconfirm } from 'antd';
import { InputNumber } from 'antd';
import Space from '@/components/Space';
import { useBrandPriceForm } from './useBrandPriceForm';

const BrandPriceForm = props => {
  const {
    is_view,
    dataList,
    brandOptions,
    onMinus,
    addColumns,
    onDiscountChange,
    onBrandChange,
    onSubmit,
    open,
    setOpen,
  } = useBrandPriceForm(props);

  const columns = [
    {
      title: '指定品牌',
      dataIndex: 'brand',
      width: 300,
      align: 'center',
      render: (value, item) => {
        const options = brandOptions.filter(i => (i.id ? i.id == item.id : true));
        return (
          <Select
            value={value}
            onChange={val => onBrandChange(val, item.id)}
            style={{ width: '90%' }}
            mode="multiple"
            placeholder="请选择品牌"
            disabled={is_view}
          >
            {options.map(brand => (
              <Select.Option value={brand.value}>{brand.label}</Select.Option>
            ))}
          </Select>
        );
      },
    },
    {
      title: (
        <Space>
          <span>享受折扣</span>
          <Tooltip
            title={
              <div>
                <div>输入9，预约投递、快递员直接投递、管理员帮投时享受9折</div>
                <div>输入0，代表免费，不收费</div>
              </div>
            }
          >
            <Icon type="question-circle" />
          </Tooltip>
        </Space>
      ),
      dataIndex: 'discount',
      width: 300,
      align: 'center',
      render: (value, item) => (
        <InputNumber
          value={value}
          min={0}
          max={10}
          onChange={val => onDiscountChange(val, item.id)}
          style={{ width: '70%' }}
          precision={1}
          disabled={is_view}
        />
      ),
    },
    {
      title: '操作',
      align: 'center',
      dataIndex: 'price',
      width: 150,
      render: (_, item) => {
        return (
          <Space>
            {!is_view && (
              <Popconfirm title="确定删除该配置？" onConfirm={onMinus.bind(null, item.id)}>
                <a>删除</a>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Row type="flex" justify="space-between" align="middle">
        <Col>
          <Space>
            <span>按快递品牌折扣设置</span>
            <Switch checked={open} onChange={setOpen} disabled={is_view} />
          </Space>
        </Col>
        <Col>
          {open &&
            !is_view && (
              <Button key="add" type="primary" onClick={addColumns}>
                添加一条新折扣
              </Button>
            )}
        </Col>
      </Row>
      {open && (
        <Table
          rowKey="id"
          style={{ margin: '20px 0', minHeight: '250px' }}
          columns={columns}
          dataSource={dataList}
          pagination={false}
        />
      )}
      {!is_view && (
        <Row type="flex" justify="center">
          <Col>
            <Button key="add" type="primary" onClick={onSubmit}>
              保存配置
            </Button>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default BrandPriceForm;
