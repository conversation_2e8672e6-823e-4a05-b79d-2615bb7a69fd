/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-restricted-syntax */
import { useState, useEffect, useMemo } from 'react';
import { message } from 'antd';
import {
  getCabinetBrandDiscount,
  getCabinetBrandList,
  setCabinetBrandDiscount,
} from '@/services/cabinet/fee';
import { randomCode } from '@/utils/utils';
import { getBrandsList } from '@/services/api';

/**
 * 自定义Hook：管理品牌折扣表单逻辑
 *
 * @param {Object} props - 组件属性
 * @param {Object} props.pageData - 页面数据对象
 * @param {string} props.pageData.cm_id - 柜机ID
 * @param {boolean} props.pageData.isMultiple - 是否多选模式
 * @param {Function} props.pageData.onReload - 数据重载回调
 * @param {Function} props.onCloseModal - 关闭模态框回调
 *
 * @returns {Object} 返回表单状态和方法
 *   @property {Array} dataList - 品牌折扣数据列表
 *   @property {Array} brandOptions - 品牌选项列表（带关联行ID）
 *   @property {Function} onMinus - 删除行处理函数
 *   @property {Function} addColumns - 添加行处理函数
 *   @property {Function} onDiscountChange - 折扣输入变更处理函数
 *   @property {Function} onBrandChange - 品牌选择变更处理函数
 *   @property {Function} onSubmit - 表单提交处理函数
 *   @property {boolean} open - 是否启用折扣开关状态
 *   @property {Function} setOpen - 设置启用折扣状态函数
 */
export function useBrandPriceForm(props) {
  const { pageData, onCloseModal } = props;

  const { cm_id, isMultiple, onReload, is_view } = pageData;

  // 表单状态
  const [open, setOpen] = useState(false); // 是否启用折扣
  const [dataList, setDataList] = useState([]); // 品牌折扣数据行
  const [brandList, setBrandList] = useState([]); // 原始品牌列表

  /**
   * 创建字段更新处理器（高阶函数）
   *
   * @param {string} field - 要更新的字段名
   * @returns {Function} 字段更新函数
   *   @param {any} val - 新字段值
   *   @param {string} id - 要更新的行ID
   */
  const createUpdateHandler = field => (val, id) => {
    setDataList(values =>
      values.map(item => ({
        ...item,
        [field]: item.id === id ? val || (field === 'discount' ? 0 : []) : item[field],
      })),
    );
  };

  /** 删除指定行 */
  const onMinus = async key => {
    setDataList(val => val.filter(i => i.id !== key));
  };

  /** 添加新行 */
  const addColumns = () => setDataList(val => [...val, { id: randomCode() }]);

  // 创建特定字段更新处理器
  const onDiscountChange = createUpdateHandler('discount');
  const onBrandChange = createUpdateHandler('brand');

  /** 初始化页面数据：获取柜机品牌折扣配置 */
  const initPage = async () => {
    try {
      const data = await getCabinetBrandDiscount({ cm_id });
      const { discount = {} } = data || {};

      // 格式化折扣数据为行数据
      const formatList = Object.keys(discount).map(item => ({
        discount: item,
        brand: discount[item],
        id: randomCode(),
      }));

      setDataList(formatList);
      setOpen(!!(data.open == '1')); // 转换开关状态
    } catch (error) {
      console.error('初始化页面数据失败:', error);
    }
  };

  /** 初始化品牌列表：获取有效品牌选项 */
  const initBrandList = async () => {
    try {
      const { data: brands = [] } = await getBrandsList();
      const cabinet_brand = await getCabinetBrandList({ cm_id });
      const validBrands = Array.isArray(cabinet_brand) ? cabinet_brand : [];

      // 过滤并格式化品牌选项
      const formatList = brands.filter(i => validBrands.includes(i.brand_en)).map(item => ({
        label: item.brand,
        value: item.brand_en,
      }));

      setBrandList(formatList);
    } catch (error) {
      console.error('初始化品牌列表失败:', error);
    }
  };

  // 初始化副作用：加载品牌数据和折扣配置
  useEffect(
    () => {
      if (cm_id && !isMultiple) {
        initPage(); // 单柜机模式加载折扣配置
      }
      initBrandList(); // 始终加载品牌列表
    },
    [cm_id, isMultiple],
  );

  /**
   * 计算品牌选项（带关联行ID）
   * 用于标识选项所属的数据行
   */
  const brandOptions = useMemo(
    () => {
      // 创建品牌值到行ID的映射
      const brandMap = new Map(dataList.flatMap(item => item.brand?.map(b => [b, item.id]) || []));

      // 为每个品牌选项附加所属行ID
      return brandList.map(item => ({
        ...item,
        id: brandMap.get(item.value) || '',
      }));
    },
    [brandList, dataList],
  );

  /** 数据校验：确保每行折扣和品牌有效 */
  const checkData = () => {
    return new Promise((resolve, reject) => {
      for (const item of dataList) {
        if (item.discount == null) {
          message.error('请填写折扣价格');
          return reject();
        }
        if (!item.brand?.length) {
          message.error('请选择折扣品牌');
          return reject();
        }
      }
      resolve();
    });
  };

  /** 格式化请求参数：将行数据转换为API所需格式 */
  const formatRequestParams = () => {
    const obj = {};
    // 构建折扣->品牌映射对象
    dataList.forEach(({ brand, discount }) => {
      if (discount != null) {
        obj[String(discount)] = brand;
      }
    });
    return {
      open: open ? '1' : '0', // 开关状态转字符串
      discount: obj,
    };
  };

  /** 表单提交处理：校验数据->格式化->发送请求 */
  const onSubmit = async () => {
    try {
      await checkData(); // 前置校验
      const params = formatRequestParams();

      // 提交品牌折扣配置
      const status = await setCabinetBrandDiscount({
        cm_ids: cm_id,
        brand_conf: params,
      });

      if (status) {
        onReload(); // 刷新父组件数据
        onCloseModal(); // 关闭模态框
      }
    } catch (error) {
      // 错误已由 checkData 处理
    }
  };

  // 暴露状态和方法给组件使用
  return {
    dataList,
    brandOptions,
    onMinus,
    addColumns,
    onDiscountChange,
    onBrandChange,
    onSubmit,
    open,
    setOpen,
    is_view,
  };
}
