/* eslint-disable no-confusing-arrow */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Alert, Button, Col, Row } from 'antd';
import ProTable from '@/components/AdaptTable';
import { AdaptFormWrapperFn } from '@/components/AdaptForm';
import { getCabinetTakeList } from '@/services/cabinet/fee';
import Space from '@/components/Space';
import KbTypographyText from '@/components/KbTypographyText';
import CabinetSettingModal from '../modal';
import { useCabinetSetTakeHandle } from './useCabinetSetTakeHandle';
import AuthorizedExtend from '@/components/Authorized/AuthorizedExtend';

const CabinetSetTake = props => {
  const {
    IS_CAN_EDIT,
    columns,
    selected,
    actionRef,
    modalRef,
    rowSelection,
    onMultipleEdit,
    onClearRows,
  } = useCabinetSetTakeHandle(props);

  const { selectedRowKeys } = selected;

  return (
    <div>
      <Alert
        message={
          <span>
            1、投递保管费是快递员或者驿站帮快递员投柜时，根据不同格口尺寸设置的价格从快递员账户扣取投递费给到快递柜管理员账户（即主驿站收款账户）.
            <br />
            2、系统默认按照各快递柜驿站app管理端设置的价格为准，加盟商可以在此处进行统一管理设置，设置后，实时生效，可对下属快递柜管理员的修改价格的权限设置.
          </span>
        }
        type="warning"
      />
      <ProTable
        actionRef={actionRef}
        rowSelection={IS_CAN_EDIT ? rowSelection : undefined}
        params={{ cabinet: '1' }}
        request={getCabinetTakeList}
        columns={columns}
        pagination={{
          showQuickJumper: false,
          showSizeChanger: false,
        }}
        rowKey="cm_id"
        footer={() =>
          IS_CAN_EDIT ? (
            <Row type="flex" align="middle">
              <Col>
                <Space>
                  <div>
                    <KbTypographyText>已选择</KbTypographyText>
                    <KbTypographyText color="link">{selectedRowKeys.length || 0}</KbTypographyText>
                    <KbTypographyText>项</KbTypographyText>
                  </div>
                  {selectedRowKeys.length > 0 && (
                    <KbTypographyText color="link" isPointer onClick={onClearRows}>
                      清空
                    </KbTypographyText>
                  )}
                </Space>
              </Col>
              <AuthorizedExtend auth="2" patchId>
                {selectedRowKeys.length > 0 && (
                  <Col offset={1}>
                    <Button type="primary" onClick={onMultipleEdit}>
                      批量修改
                    </Button>
                  </Col>
                )}
              </AuthorizedExtend>
            </Row>
          ) : null
        }
        scroll={{ x: 1100 }}
      />
      <CabinetSettingModal ref={modalRef} />
    </div>
  );
};

export default AdaptFormWrapperFn(CabinetSetTake);
