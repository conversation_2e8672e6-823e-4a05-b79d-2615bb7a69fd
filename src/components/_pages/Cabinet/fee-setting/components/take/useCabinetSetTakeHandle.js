/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Col, Popconfirm, Row } from 'antd';
import React, { useState, useRef } from 'react';
import { checkAuthorized } from '@/components/Authorized/AuthorizedExtend';
import { prohibitSetFee } from '@/services/cabinet/fee';

export function useCabinetSetTakeHandle(props) {
  const { currentUser } = props;
  const { user_info } = currentUser || {};
  const { cabinet_set_fee } = user_info || {};

  const IS_CAN_EDIT = `${cabinet_set_fee}` === '1';

  const actionRef = useRef();
  const modalRef = useRef();

  const [selected, setSelected] = useState({
    selectedRowKeys: [],
    selectedRows: [],
  });

  const { selectedRowKeys, selectedRows } = selected;
  const rowSelection = {
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: (keys, rows) => {
      setSelected({
        selectedRowKeys: [...keys],
        selectedRows: [...rows],
      });
    },
  };

  const onSingleEdit = item => {
    const { company_name, cm_id } = item;
    modalRef.current.updatePageData({
      isMultiple: false,
      cm_id,
      title: '编辑保管费',
      comKey: 'take',
      cabinet_name: company_name,
      isShow: true,
      onReload: () => actionRef.current.reload(),
    });
  };

  const onMultipleEdit = () => {
    const isSign = selectedRows.length == 1;
    if (isSign) {
      onSingleEdit(selectedRows[0]);
      return;
    }
    modalRef.current.updatePageData({
      isMultiple: true,
      cm_id: selectedRowKeys.toString(),
      title: '编辑保管费',
      comKey: 'take',
      cabinet_name: '',
      isShow: true,
      onReload: () => actionRef.current.reload(),
    });
  };

  const onViewTake = item => {
    const { company_name, cm_id } = item;
    modalRef.current.updatePageData({
      is_view: true,
      isMultiple: false,
      cm_id,
      title: '查看保管费',
      comKey: 'take',
      cabinet_name: company_name,
      isShow: true,
    });
  };

  const onClearRows = () => {
    setSelected({
      selectedRowKeys: [],
      selectedRows: [],
    });
  };

  const onChangeStatus = async item => {
    const { cm_id, prohibit_set_fee } = item;
    const status = await prohibitSetFee({
      cm_id,
      status: prohibit_set_fee == '1' ? '0' : '1',
    });
    if (status) {
      actionRef.current.reload();
    }
  };

  const canAccess = checkAuthorized({ auth: '2', patchId: true });

  const columns = [
    {
      title: '',
      key: 'cm_id',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入快递柜名称、或注册账号查询',
        colon: false,
        span: 10,
      },
    },
    {
      title: '快递柜名称',
      key: 'cm_id',
      dataIndex: 'company_name',
      search: false,
    },
    {
      title: '快递柜账号',
      dataIndex: 'phone',
      search: false,
    },
    {
      title: '保管费收费标准',
      dataIndex: 'address',
      search: false,
      render: (_, item) => <a onClick={() => onViewTake(item)}>查看</a>,
    },
    {
      title: '修改时间',
      dataIndex: 'update_at',
      search: false,
    },
    {
      title: '当前快递柜管理员修改权限状态',
      width: 150,
      search: false,
      render: (text, item) => <span>{`${item.prohibit_set_fee}` === '0' ? '允许' : '禁止'}</span>,
    },
    {
      title: '操作',
      dataIndex: 'status',
      search: false,
      width: 150,
      hidden: !canAccess,
      fixed: 'right',
      render: (_, item) => {
        const label = `${`${item.prohibit_set_fee}` === '0' ? '禁止' : '允许'}管理员修改`;
        return (
          <Row gutter={[10, 10]}>
            {IS_CAN_EDIT && (
              <Col span={24}>
                <a onClick={() => onSingleEdit(item)}> 编辑</a>
              </Col>
            )}
            {IS_CAN_EDIT && (
              <Col span={24}>
                <Popconfirm title={`确认${label}？`} onConfirm={() => onChangeStatus(item)}>
                  <a>{label}</a>
                </Popconfirm>
              </Col>
            )}
          </Row>
        );
      },
    },
  ].filter(item => !item.hidden);

  return {
    IS_CAN_EDIT,
    columns,
    selected,
    actionRef,
    modalRef,
    rowSelection,
    onMultipleEdit,
    onClearRows,
  };
}
