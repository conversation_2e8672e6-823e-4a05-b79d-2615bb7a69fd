/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const OVER_PRICE_FORM_ITEMS = [
  { label: '免费时长', name: 'free_hours', after: '小时' },
  { label: '超时起步价', name: 'initial_charge', after: '元' },
  { label: '每多', name: 'additional_hours', after: '小时' },
  { label: '加收', name: 'additional_charge', after: '元' },
  { label: '封顶收费', name: 'max_charge', after: '元' },
];

// 常量提取到模块作用域
export const WEEKEND_TYPES = {
  HOLIDAY: 'holiday_free',
  WEEKEND: 'weekend_free',
};

// 超时费整理方法，把接口数据整合成表单数据回填
export function formatOverPriceFormRequest(item) {
  const { rate_details = {}, holiday_free, weekend_free } = item || {};

  const weekend_type = [
    holiday_free === '1' ? WEEKEND_TYPES.HOLIDAY : null,
    weekend_free === '1' ? WEEKEND_TYPES.WEEKEND : null,
  ].filter(Boolean);

  const weekend = weekend_type.length > 0;

  return {
    weekend,
    weekend_type,
    ...Object.fromEntries(
      Object.keys(OVER_PRICE_FORM_ITEMS.reduce((acc, { name }) => ({ ...acc, [name]: 1 }), {})).map(
        key => [key, rate_details[key]],
      ),
    ),
  };
}
