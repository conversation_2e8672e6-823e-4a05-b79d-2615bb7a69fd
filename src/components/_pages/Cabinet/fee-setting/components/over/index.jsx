/* eslint-disable no-confusing-arrow */
/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Alert, Button, Col, Row } from 'antd';
import ProTable from '@/components/AdaptTable';
import { AdaptFormWrapperFn } from '@/components/AdaptForm';
import { getCabinetTakeList } from '@/services/cabinet/fee';
import Space from '@/components/Space';
import KbTypographyText from '@/components/KbTypographyText';
import CabinetSettingModal from '../modal';
import { useCabinetSetOverPrice } from './useCabinetSetOverPrice';
import AuthorizedExtend from '@/components/Authorized/AuthorizedExtend';

const CabinetSetOverPrice = props => {
  const {
    IS_CAN_EDIT,
    columns,
    selected,
    actionRef,
    modalRef,
    rowSelection,
    onMultipleEdit,
    onMultipleRake,
    onClearRows,
  } = useCabinetSetOverPrice(props);

  const { selectedRowKeys } = selected;
  const hasSelected = selectedRowKeys.length > 0;

  return (
    <div>
      <Alert
        message={
          <span>
            1、系统默认按照各快递柜驿站app管理端设置的价格为准，加盟商可以在此处进行统一管理设置，设置后，实时生效，可对下属快递柜管理员的修改价格的权限设置。
            <br />
            2、超时费分成需联系快宝运营开启权限。
          </span>
        }
        type="warning"
      />
      <ProTable
        actionRef={actionRef}
        rowSelection={IS_CAN_EDIT ? rowSelection : undefined}
        params={{ cabinet: '1', out_time_fee: '1' }}
        request={getCabinetTakeList}
        columns={columns}
        pagination={{
          showQuickJumper: false,
          showSizeChanger: false,
        }}
        rowKey="cm_id"
        footer={() =>
          IS_CAN_EDIT ? (
            <Row type="flex" align="middle">
              <Col>
                <Space>
                  <div>
                    <KbTypographyText>已选择</KbTypographyText>
                    <KbTypographyText color="link">{selectedRowKeys.length || 0}</KbTypographyText>
                    <KbTypographyText>项</KbTypographyText>
                  </div>
                  {hasSelected && (
                    <KbTypographyText color="link" isPointer onClick={onClearRows}>
                      清空
                    </KbTypographyText>
                  )}
                </Space>
              </Col>
              <AuthorizedExtend auth="2" patchId>
                <Col offset={1}>
                  <Space>
                    <Button type="primary" disabled={!hasSelected} onClick={() => onMultipleEdit()}>
                      批量修改
                    </Button>
                    <Button type="primary" disabled={!hasSelected} onClick={() => onMultipleRake()}>
                      批量编辑分佣
                    </Button>
                  </Space>
                </Col>
              </AuthorizedExtend>
            </Row>
          ) : null
        }
        scroll={{ x: 1100 }}
      />
      <CabinetSettingModal ref={modalRef} />
    </div>
  );
};

export default AdaptFormWrapperFn(CabinetSetOverPrice);
