/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Col, Popconfirm, Row } from 'antd';
import React, { useState, useRef } from 'react';
import { prohibitSetFee } from '@/services/cabinet/fee';
import { OVER_PRICE_FORM_ITEMS } from '../_utils/utils';
import { checkAuthorized } from '@/components/Authorized/AuthorizedExtend';
import { modalAlert } from '@/components/_pages/Allocation/batchpatch/_utils/usePatchForm';

export function useCabinetSetOverPrice(props) {
  const { currentUser } = props;
  const { user_info } = currentUser || {};
  const { cabinet_set_fee, cabinet_timeout_fee_rake } = user_info || {};

  const IS_CAN_EDIT = `${cabinet_set_fee}` === '1';

  const actionRef = useRef();
  const modalRef = useRef();

  const [selected, setSelected] = useState({
    selectedRowKeys: [],
    selectedRows: [],
  });

  const { selectedRowKeys, selectedRows } = selected;
  const rowSelection = {
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: (keys, rows) => {
      setSelected({
        selectedRowKeys: [...keys],
        selectedRows: [...rows],
      });
    },
  };

  // 常量提取
  const EDIT_OVER_PRICE = '编辑超时费';
  const EDIT_RAKE = '编辑超时费分佣';

  // 统一处理操作逻辑
  const handleMultipleAction = (item, comKey) => {
    const hasSelection = selectedRows.length > 0;
    const isSingleSelection = selectedRows.length === 1;

    const title = comKey == 'over_price' ? EDIT_OVER_PRICE : EDIT_RAKE;

    // 安全访问检查
    if (!modalRef.current) return;

    // 单个操作处理 (当前行或单选情况)
    if (item || isSingleSelection) {
      const target = item ?? selectedRows[0];
      modalRef.current.updatePageData({
        isMultiple: false,
        cm_id: target?.cm_id ?? '',
        title,
        comKey,
        cabinet_name: target?.company_name ?? '',
        isShow: true,
        onReload: () => actionRef.current?.reload?.(),
      });
      return;
    }

    // 批量操作处理
    if (hasSelection) {
      modalRef.current.updatePageData({
        isMultiple: true,
        cm_id: selectedRowKeys.join(','), // 更安全的ID拼接方式
        title,
        comKey,
        cabinet_name: '',
        isShow: true,
        onReload: () => actionRef.current?.reload?.(),
      });
    }
  };

  const onMultipleEdit = item => handleMultipleAction(item, 'over_price');

  const onMultipleRake = item => {
    const list = new Set(selectedRows.map(i => i.timeout_user));
    if (Array.from(list).length > 1) {
      modalAlert({
        hideCancel: true,
        content: '选择的收款对象不一致，请重新选择后修改',
      });
      return;
    }

    handleMultipleAction(item, 'rake');
  };

  const onClearRows = () => {
    setSelected({
      selectedRowKeys: [],
      selectedRows: [],
    });
  };

  const onChangeStatus = async item => {
    const { cm_id, prohibit_set_fee } = item;
    const status = await prohibitSetFee({
      cm_id,
      status: prohibit_set_fee == '1' ? '0' : '1',
    });
    if (status) {
      actionRef.current.reload();
    }
  };

  const canAccess = checkAuthorized({ auth: '2', patchId: true });
  const columns = [
    {
      title: '',
      key: 'cm_id',
      dataIndex: 'keyword',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入快递柜名称、或注册账号查询',
        colon: false,
        span: 8,
      },
    },
    {
      title: '快递柜名称',
      key: 'cm_id',
      dataIndex: 'company_name',
      search: false,
    },
    {
      title: '快递柜账号',
      dataIndex: 'phone',
      search: false,
    },
    {
      title: '超时费收费标准',
      dataIndex: 'address',
      search: false,
      width: 300,
      render: (_, item) => {
        const { rate_details } = item;

        let srt = '';
        if (rate_details) {
          OVER_PRICE_FORM_ITEMS.forEach(i => {
            srt += rate_details[i.name] ? `${i.label}${rate_details[i.name]}${i.after},` : '';
          });
        }
        return <span>{srt || '未设置'}</span>;
      },
    },
    {
      title: '收款对象',
      dataIndex: 'timeout_user',
      search: false,
      render: val => (val == '1' ? '驿站' : '加盟商'),
    },
    {
      title: '修改时间',
      dataIndex: 'update_time',
      search: false,
    },
    {
      title: '当前快递柜管理员修改权限状态',
      width: 150,
      search: false,
      render: (text, item) => <span>{`${item.prohibit_set_fee}` === '0' ? '允许' : '禁止'}</span>,
    },
    {
      title: '操作',
      dataIndex: 'status',
      search: false,
      width: 150,
      hidden: !canAccess,
      fixed: 'right',
      render: (_, item) => {
        const label = `${`${item.prohibit_set_fee}` === '0' ? '禁止' : '允许'}管理员修改`;
        return (
          <Row gutter={[10, 10]}>
            {IS_CAN_EDIT && (
              <Col span={24}>
                <a onClick={() => onMultipleEdit(item)}>编辑</a>
              </Col>
            )}
            {IS_CAN_EDIT && (
              <Col span={24}>
                <Popconfirm title={`确认${label}？`} onConfirm={() => onChangeStatus(item)}>
                  <a>{label}</a>
                </Popconfirm>
              </Col>
            )}
            {`${cabinet_timeout_fee_rake}` == '1' && (
              <Col span={24}>
                <a onClick={() => onMultipleRake(item)}>超时费分佣</a>
              </Col>
            )}
          </Row>
        );
      },
    },
  ].filter(item => !item.hidden);

  return {
    IS_CAN_EDIT,
    columns,
    selected,
    actionRef,
    modalRef,
    rowSelection,
    onMultipleEdit,
    onMultipleRake,
    onClearRows,
  };
}
