/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { Pagination } from 'antd';
import styles from './style.less';

const CabinetPaginations = props => {
  const { current, count, pageSize, onChange } = props;

  const handleTableChange = pagination => {
    if (onChange) {
      onChange(pagination);
    }
  };

  return (
    <div className={styles.flex}>
      <Pagination
        current={current}
        total={count}
        onChange={handleTableChange}
        pageSize={pageSize}
      />
    </div>
  );
};

export default CabinetPaginations;
