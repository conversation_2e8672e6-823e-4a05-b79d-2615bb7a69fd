/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable react/jsx-no-bind */
import React from 'react';
import { connect } from 'dva';
import { useDebounceFn, useSetState } from 'ahooks';
import AreaList from '@/components/AreaList';
import InfoCheck from '@/components/InfoCheck';
import CabinetSearchForm from './searchForm';
import CabinetPaginations from './paginations';

const Index = props => {
  const { dispatch, area, loading } = props;

  const [pageData, updatePageData] = useSetState({
    current: 1,
    count: 1,
    pageSize: 10,
  });

  const { current, count, pageSize } = pageData;
  const { subordinates } = area;
  const { paginations } = subordinates;
  const currentPage = paginations ? paginations.page : current;
  const totalNum = paginations ? paginations.count : count;
  const pageSizeNum = paginations ? paginations.pageSize : pageSize;

  const {
    subordinates: { keyword },
  } = area;

  const { run: onChange } = useDebounceFn(
    page => {
      getAreaList(page);
      updatePageData({
        current: page,
      });
    },
    200,
    {
      leading: true,
      trailing: false,
    },
  );

  const getAreaList = page => {
    dispatch({
      type: 'area/subList',
      payload: {
        is_district: '0',
        key: '1',
        keyword: keyword || '',
        pageNum: page,
        cabinet: '1',
      },
    });
  };

  const handleInfoReady = data => {
    if (!data.name) return;
    getAreaList(1);
  };

  return (
    <div>
      <InfoCheck onReady={handleInfoReady.bind(this)} />
      <CabinetSearchForm searchType="1" current={currentPage} source="cabinet" />
      <AreaList
        loading={loading}
        data={subordinates}
        post="1"
        getList={() => getAreaList(current)}
      />
      <CabinetPaginations
        current={currentPage * 1}
        count={totalNum}
        onChange={onChange}
        pageSize={pageSizeNum}
      />
    </div>
  );
};

const CabinetManageList = connect(({ loading, area, user, setting }) => ({
  user,
  area,
  loading: loading.effects['area/subList'],
  ...setting,
}))(Index);

export default CabinetManageList;
