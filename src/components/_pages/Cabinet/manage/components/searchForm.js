/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useState, useEffect, useCallback } from 'react';
import { connect } from 'dva';
import { Form, Button, Input } from 'antd';
import { debounce } from 'lodash';
import styles from './style.less';

const FormItem = Form.Item;

const Index = props => {
  const {
    dispatch,
    form,
    loading,
    applyLoading,
    searchType,
    user,
    zyAuditLoading,
    zySubPostStationsLoading,
    current,
    options: { key: platformKey, postName = '' },
    source,
  } = props;
  const { getFieldDecorator, validateFields, resetFields } = form;
  const { currentUser = {} } = user;
  const { user_info = {} } = currentUser;
  const { branchId } = user_info;
  const [all, setAll] = useState(false);
  const [iniBranchId, setBranchId] = useState(null); // 初始的用户branchId
  const isZyAccount = platformKey === 'post';

  const IS_CABINET = source === 'cabinet';

  useEffect(
    () => {
      if (branchId) {
        setBranchId(branchId);
      }
    },
    [branchId],
  );

  const getFormValues = then => {
    validateFields((err, fieldsValue) => {
      const { codeArr } = fieldsValue;
      if (err) return;
      let values = {};
      // branch_id取级联的最后一位
      const branch_id =
        fieldsValue.branch_id && fieldsValue.branch_id.length > 0
          ? [...fieldsValue.branch_id].reverse()[0]
          : iniBranchId;
      const forMatCodeArr = codeArr && codeArr.length > 0 ? [...codeArr][codeArr.length - 1] : '';
      if (searchType == '1') {
        isZyAccount
          ? (values = {
              // 中邮，下属驿站请求参数
              status: 1,
              keyword: fieldsValue.keyword || '',
              branch_id,
              page: 1,
              pageSize: 20,
              area_type: fieldsValue.area_type,
            })
          : (values = {
              is_district: '0',
              key: '1',
              keyword: fieldsValue.keyword || '',
              code: forMatCodeArr,
            });
      } else if (searchType == '2') {
        values = {
          is_district: '1',
          key: '2',
          keyword: fieldsValue.keyword || '',
          code: forMatCodeArr,
        };
      } else if (searchType == '3') {
        isZyAccount // 中邮，待审核请求参数
          ? (values = {
              keyword: fieldsValue.keyword || '',
              branch_id,
              status: 0,
              page: 1,
              pageSize: 20,
            })
          : (values = {
              is_district: '2',
              key: '3',
              keyword: fieldsValue.keyword || '',
              code: forMatCodeArr,
            });
      } else if (searchType == '4') {
        values = {
          is_district: '3',
          key: '4',
          keyword: fieldsValue.keyword || '',
          code: forMatCodeArr,
        };
      } else if (searchType == '5') {
        // 中邮禁用驿站
        values = {
          keyword: fieldsValue.keyword || '',
          branch_id,
          status: 2,
          pageSize: 20,
          page: 1,
        };
      }
      then(values);
    });
  };

  const handleSearchForthe = useCallback(
    debounce(
      e => {
        e?.preventDefault?.();
        getFormValues(values => {
          dispatch({
            type: 'area/subList',
            payload: {
              ...values,
              cabinet: IS_CABINET ? '1' : '',
            },
          });
          if (values.keyword != '' && !isZyAccount) {
            setAll(true);
          }
        });
      },
      500,
      {
        leading: true,
        trailing: false,
      },
    ),
    [iniBranchId, searchType],
  );

  const allSearch = () => {
    getFormValues(values => {
      const type = values.is_district == '3' ? 'area/getApplyList' : 'area/subList';
      dispatch({
        type,
        payload: {
          is_district: values.is_district,
          keyword: '',
          pageNum: current,
          cabinet: IS_CABINET ? '1' : '',
        },
        then: () => {},
      });
      resetFields();
      setAll(false);
    });
  };

  useEffect(() => {
    dispatch({
      type: 'area/setHandleSearch',
      payload: {
        handleSearch: handleSearchForthe,
      },
    });
  }, []);

  return (
    <div className={styles.mainSearch}>
      <Form onSubmit={handleSearchForthe} className={styles['search-form']} layout="inline">
        <FormItem>
          {getFieldDecorator('keyword')(
            <Input
              className={styles.searchInput}
              placeholder={
                searchType == '4'
                  ? '请输入手机号码或姓名称查找'
                  : IS_CABINET
                    ? '请输入快递柜名称、账号查询'
                    : `请输入${postName}驿站的注册手机号码、或驿站名称查找`
              }
            />,
          )}
        </FormItem>
        <FormItem>
          <span className={styles.submitButtons}>
            <Button
              type="primary"
              htmlType="submit"
              key="search"
              loading={loading || applyLoading || zySubPostStationsLoading || zyAuditLoading}
              icon="search"
            >
              查询
            </Button>
          </span>
          {all ? (
            <div
              style={{
                marginLeft: '16px',
                display: 'inline-block',
              }}
            >
              <a onClick={allSearch}>返回全部</a>
            </div>
          ) : (
            ''
          )}
        </FormItem>
      </Form>
    </div>
  );
};

const CabinetSearchForm = connect(({ loading, area, user, setting }) => ({
  area,
  user,
  loading: loading.effects['area/subList'],
  applyLoading: loading.effects['area/getApplyList'],
  zySubPostStationsLoading: loading.effects['area/zyGetSubPostStations'],
  zyAuditLoading: loading.effects['area/zyGetSubPostStationsToBeChecked'],
  ...setting,
}))(Form.create()(Index));

export default CabinetSearchForm;
