/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* stylelint-disable order/properties-order */
@import '~antd/lib/style/themes/default.less';

.main {
  width: 100%;
  height: 100%;
  padding: 32px;
  background-color: @body-background;
  h2 {
    font-weight: 600;
  }
  tr {
    text-align: center !important;
    th {
      text-align: center !important;
    }
  }
}

.search-form {
  :global {
    .ant-form-item {
      display: flex;
      margin-right: 0;
      margin-bottom: 24px;
      > .ant-form-item-label {
        line-height: 32px;
      }
      .ant-form-item-control {
        line-height: 32px;
      }
    }
  }
}

.search {
  :global {
    position: absolute;
    right: 0;
    z-index: 8;
    //width: 400px;
    .ant-form-item {
      display: flex;
      margin-right: 0;
      margin-bottom: 24px;
      > .ant-form-item-label {
        line-height: 32px;
      }
      .ant-form-item-control {
        line-height: 32px;
      }
      .ant-form-explain {
        position: relative;
        top: -55px;
      }
    }
  }
}

.operator-table {
  margin-top: 16px;
  padding: 16px;
  background-color: #fff;
}

.operator-button {
  button {
    margin-right: 8px;
  }
}

.data {
  :global {
    .ant-col-sm-16 {
      width: 100%;
    }
  }
}

.address {
  margin: 35px 0 35px 10px;
  color: #424040ab;
  font-weight: 600;
}

.help {
  font-size: 14px;
}

.ml_70 {
  margin-left: 70px;
}

.auth_form {
  height: 150px;
  :global {
    .ant-form-item-control {
      > .auth_input {
        margin-top: 110px !important;
      }
    }
    .ant-form-explain {
      margin-left: 80px;
    }
  }
}

:global {
  .ant-col-8 {
    > div {
      > .ant-select {
        width: 55%;
      }
    }
  }
}

.mr_10 {
  margin-right: 10px;
}

.open{
  color: #1890FF
}

.blacklistModal {
  :global{
    .title{
        color:#666;
        font-size: 16px;
    }
    
    .submit{
        text-align: right;
        .cancel{
            margin-left:10px;
        }
    }
    
  }
}

.braftEditor {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}