/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

/* eslint-disable no-shadow */
import React from 'react';
import { connect } from 'dva';
import { Row, Col } from 'antd';
import { useUnmount } from 'ahooks';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import AreaAddModal from '@/components/_pages/Post/Area/AreaAddModal';
import DispatchPianqu from '@/components/_pages/Post/Area/DispatchPianqu';
import styles from './style.less';
import CabinetManageList from './components/manageList';

const CabinetManage = props => {
  const { dispatch } = props;

  useUnmount(() => {
    dispatch({
      type: 'area/setHandleSearch',
      payload: {
        handleSearch: () => {},
      },
    });
  });

  return (
    <PageHeaderLayout
      title={
        <Row type="flex" justify="space-between">
          <Col>下属快递柜</Col>
          <Col>
            <Row type="flex" align="middle" gutter={8}>
              <Col>
                <AreaAddModal source="cabinet" />
              </Col>
              <Col>
                <DispatchPianqu
                  type="inn"
                  onChange={area_id =>
                    dispatch({
                      type: 'area/changeArea',
                      payload: { area_id },
                    })
                  }
                />
              </Col>
            </Row>
          </Col>
        </Row>
      }
    >
      <div className={styles.main}>
        <CabinetManageList />
      </div>
    </PageHeaderLayout>
  );
};

export default connect(({ user, setting }) => ({
  user,
  ...setting,
}))(CabinetManage);
