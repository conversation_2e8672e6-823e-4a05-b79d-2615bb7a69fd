/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { ProColumns } from '@/components/AdaptTable';
import { IListRecordItem } from '../types';
import { useOptions } from '../_utils';
import { useRangePicker } from '@/utils/hooks/rangePicker';
import moment from 'moment';

export const useColumns = () => {
  const { recordPayMethodValueEnum, recordStatusValueEnum } = useOptions();
  const p = useRangePicker({});
  const columns: ProColumns<IListRecordItem>[] = [
    {
      title: '',
      dataIndex: 'time',
      valueType: 'dateRange',
      initialValue: p.initialValue,
      hideInTable: true,
      fieldProps: {
        ...p.fieldProps,
        span:12
      },
      search: {
        transform: (v) => ({ start_date: v?.[0], end_date: v?.[1] }),
      },
    },
    {
      title: '扣款时间',
      dataIndex: 'create_time',
      align: 'center',
      search: false,
    },
    {
      title: '抽成金额',
      dataIndex: 'money',
      align: 'center',
      search: false,
    },
    {
      title: '期数',
      dataIndex: 'date',
      align: 'center',
      search: false,
      render: (_, { date }) => {
        return date && date.includes('-') ? `${moment(date).format('YYMMDD')}期` : date;
      },
    },
    {
      title: '扣款渠道',
      dataIndex: 'pay_method',
      align: 'center',
      valueEnum: recordPayMethodValueEnum,
      search: false,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      valueEnum: recordStatusValueEnum,
      search: false,
    },
  ];
  return {
    columns,
  };
};
