/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { FC } from 'react';
import { useColumns } from './useColunms';
import { IListItem } from '../types';
import { getCabinetSharePayList } from '@/services/kdgFee';
import styles from './index.less';
import AdaptWrapperFn from '@/components/AdaptForm/form/AdaptWrapper';
import { ModalForm } from '@/components/AdaptForm';
import ProTable from '@/components/AdaptTable';
interface IProps {
  record: IListItem;
}
const OverCommission: FC<IProps> = (props) => {
  const { record } = props;
  const { columns } = useColumns();

  return (
    <>
      <ModalForm
        title='超时费抽成'
        trigger={<a>查看</a>}
        submitter={{
          submitButtonProps: { style: { display: 'none' } },
          searchConfig: { resetText: '关闭' },
          resetButtonProps: { type: 'primary' },
        }}
        modalProps={{ destroyOnClose: true }}
        width={800}
        style={{ maxHeight: 600, overflow: 'scroll' }}
        mountBody={false}
      >
        <ProTable
          request={async (params) =>
            await getCabinetSharePayList({ ...params, finance_id: record.id, kb_id: record.kb_id })
          }
          className={styles.overCommission}
          columns={columns}
          search={{
            optionRender: (_, p, dom) => {
              return [
                ...dom,
              ];
            },
          }}
        />
      </ModalForm>
    </>
  );
};

export default AdaptWrapperFn(OverCommission);;
