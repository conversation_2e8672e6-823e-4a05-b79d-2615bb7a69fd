/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import { useColumns } from './useColunms';
import { getCabinetCreditList } from '@/services/kdgFee';
import ProTable from '@/components/AdaptTable';
import AdaptWrapperFn from '@/components/AdaptForm/form/AdaptWrapper';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
const PageHeader = PageHeaderLayout as any;
const CabinetPayment = () => {
  const { columns } = useColumns();

  return (
    <PageHeader title="快递柜分期付款">
      <ProTable
        request={getCabinetCreditList}
        columns={columns}
        search={{
          optionRender: (search, _, dom) => [...dom],
        }}
      />
    </PageHeader>
  );
};

export default AdaptWrapperFn(CabinetPayment);
