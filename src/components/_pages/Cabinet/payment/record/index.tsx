/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { FC } from 'react';
import { useColumns } from './useColunms';
import { IListItem } from '../types';
import { getCabinetPayList } from '@/services/kdgFee';
import { ModalForm } from '@/components/AdaptForm';
import ProTable from '@/components/AdaptTable';
import AdaptWrapperFn from '@/components/AdaptForm/form/AdaptWrapper';
interface IProps {
  record: IListItem;
}
const FeeRecord: FC<IProps> = (props) => {
  const { record } = props;
  const { credit_money, paid_money, surplus_credit_money } = record || {};
  const { columns } = useColumns();

  return (
    <>
      <ModalForm
        title='扣款记录'
        trigger={<a>记录</a>}
        submitter={{
          submitButtonProps: { style: { display: 'none' } },
          searchConfig: { resetText: '关闭' },
          resetButtonProps: { type: 'primary' },
        }}
        modalProps={{ destroyOnClose: true }}
        width={600}
        style={{ maxHeight: 600, overflow: 'scroll' }}
        mountBody={false}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            marginBottom: 12,
            gap:10
          }}
        >
            <div>贷款金额：{credit_money}</div>
            <div>已扣金额：{paid_money}</div>
            <div>剩余金额：{surplus_credit_money}</div>
        </div>
        <ProTable
          request={async (params) =>
            await getCabinetPayList({ ...params, finance_id: record.id, kb_id: record.kb_id })
          }
          columns={columns}
          search={false}
          pagination={false}
        />
      </ModalForm>
    </>
  );
};

export default AdaptWrapperFn(FeeRecord);
