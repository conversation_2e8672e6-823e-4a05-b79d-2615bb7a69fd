/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { IListRecordItem } from '../types';
import { useOptions } from '../_utils';
import { ProColumns } from '@/components/AdaptTable';
import moment from 'moment';

export const useColumns = () => {
  const { recordPayMethodValueEnum, recordStatusValueEnum } = useOptions();
  const columns: ProColumns<IListRecordItem>[] = [
    {
      title: '扣款时间',
      dataIndex: 'create_time',
      align: 'center',
    },
    {
      title: '扣款金额',
      dataIndex: 'money',
      align: 'center',
    },
    {
      title: '期数',
      dataIndex: 'date',
      align: 'center',
      search: false,
      render: (_, { date }) => {
        return date && date.includes('-') ? `${moment(date).format('YYMMDD')}期` : date;
      },
    },
    {
      title: '扣款渠道',
      dataIndex: 'pay_method',
      align: 'center',
      valueEnum: recordPayMethodValueEnum,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      valueEnum: recordStatusValueEnum,
    },
  ];
  return {
    columns,
  };
};
