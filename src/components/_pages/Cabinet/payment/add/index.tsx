/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import ProForm, {
  ModalForm,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
} from '@/components/AdaptForm';
import React, { FC, useEffect, useState } from 'react';
import { IListItem } from '../types';
import {
  getCabinetCreditAdd,
  getCabinetCreditEdit,
} from '@/services/kdgFee';
import { Divider } from 'antd';
import { useOptions } from '../_utils';
import { AddWrapper } from './styles';

interface IProps {
  record?: IListItem;
  trigger?: React.ReactElement;
  onSuccess?: () => void;
}

const AddContract: FC<IProps> = (props) => {
  const { record, trigger, onSuccess } = props;
  const [open, setOpen] = useState(false);
  const [form] = ProForm.useForm();
  const { platformValueEnum } = useOptions();
  const readonly = !!record?.id;
  const title = readonly ? '合同详情' : '新增合同';

  const onFinish = async (val: any) => {
    let bool;
    if (readonly) {
      const { single_deduct_money } = val;
      bool = await getCabinetCreditEdit({
        single_deduct_money,
        finance_id: record.id,
        kb_id: record.kb_id,
      });
    } else {
      const { credit_money, ...rest } = val;
      bool = await getCabinetCreditAdd(rest);
    }
    if (bool) {
      onSuccess?.();
    }
    return bool;
  };
  const watchTotalMoney = ProForm.useWatch('total_money', form);
  const watchFirstPaidMoney = ProForm.useWatch('first_paid_money', form);
  useEffect(() => {
    if (!readonly) {
      const money = (watchTotalMoney || 0) - (watchFirstPaidMoney || 0);
      form.setFieldsValue({
        credit_money: money == 0 ? '-' : money,
      });
    }
  }, [watchTotalMoney, watchFirstPaidMoney]);
  useEffect(() => {
    if (open && record) {
      form.setFieldsValue({
        ...record,
        inn_name: record?.cabinet_name
        ? `${record.inn_name}-${record.cabinet_name}`
        : record.inn_name,
        date: record?.deduct_standard == 'deliver_number'
          ? record?.start_date
          : `${record?.start_date} 至 ${record?.end_date}`,
        share_ratio: record?.share_ratio ? record?.share_ratio * 100 : '',
        share_type: !record.share_payee ? 'no' : record.share_type,
        share_date: `${record.share_start_date || ''} ${record.share_end_date ? '至' + record.share_end_date : ''}`,
      });
    }
  }, [open, record]);
  return (
    <>
      <ModalForm
        visible={open}
        onVisibleChange={setOpen}
        title={title}
        trigger={trigger}
        modalProps={{ destroyOnClose: true }}
        layout='horizontal'
        width={700}
        onFinish={onFinish}
        form={form}
        submitter={{
          submitButtonProps: { style: { display: 'none' } },
          searchConfig: { resetText: '关闭' },
          resetButtonProps: { type: 'primary' },
        }}
        mountBody={false}
      ><AddWrapper>
          <ProFormText
            width='md'
            name='contract_number'
            label='合同编号'
            placeholder='输入合同编号'
            readonly={readonly}
          />
          <div style={{ display: 'flex', marginLeft: 94 }}>
            <ProFormText
              width={160}
              name='inn_phone'
              label='驿站账号'
              placeholder='输入主驿站手机号'
              readonly={readonly}
            />
            <ProFormText name='inn_name' readonly />
          </div>
          <ProFormText
            width='md'
            name='total_money'
            label={<>&nbsp;&nbsp;&nbsp;&nbsp;总金额</>}
            placeholder='输入总金额'
            readonly={readonly}
          />
          <ProFormText
            width='md'
            name='first_paid_money'
            label='首付金额'
            placeholder='输入实际首付金额'
            readonly={readonly}
          />

          <ProFormText
            width='md'
            name='credit_money'
            label='贷款金额'
            placeholder='输入贷款金额'
            readonly
          />
          <Divider orientation='left'>购机贷款</Divider>
          <div style={{ display: 'flex', marginLeft: 94, gap: 10 }}>
            <span className='selectCls'>
              <ProFormSelect
                name='deduct_frequency'
                label='扣款模式'
                initialValue='day'
                allowClear={false}
                valueEnum={{
                  day: '每天扣一次',
                  month: '每月扣一次',
                }}
                readonly
              />
            </span>

            <ProFormSelect
              name='deduct_standard'
              initialValue='fix_amount'
              allowClear={false}
              valueEnum={{
                fix_amount: '固定金额',
                cells_number: '格口数',
                deliver_number: '按投件量',
              }}
              readonly
            />
            <ProFormText
              name='single_deduct_money'
              readonly
            />
            {record.deduct_standard == 'cells_number' && (
              <>
                <span style={{ position: 'relative', top: 11 }}>*</span>
                <ProFormText
                  name='deduct_number'
                  placeholder='格口数量'
                  rules={[{ required: true }]}
                  readonly
                />
              </>
            )}
          </div>
          <ProFormText width='md' name='date' label='扣款时间' readonly />
          <ProFormRadio.Group
            name='cabinet_payee'
            label={<>&nbsp;&nbsp;&nbsp;&nbsp;收款方</>}
            initialValue='kb'
            valueEnum={platformValueEnum}
            readonly
          />

          {/* <Divider orientation='left'>超时费抽成</Divider>
          <div style={{ display: 'flex', marginLeft: 94}}>
          <span className='selectCls'>
            <ProFormSelect
              width={140}
              name='share_type'
              label='抽成模式'
              initialValue='share_ratio'
              allowClear={false}
              valueEnum={{
                no: '不抽成',
                share_ratio: '按比例',
                share_fix_amount: '固定金额',
              }}
              readonly
            />
            </span>
            {record.share_type == 'share_ratio' ? (
              <span style={{ display: 'flex' }}>
                <ProFormText readonly width={130} name='share_ratio' placeholder='每单抽成比例' />
                <span style={{ position: 'relative', top: 9 }}> %</span>
              </span>
            ) : record.share_type == 'no' ? null : (
              <ProFormText readonly width={130} name='share_money' placeholder='每单抽成金额' />
            )}
          </div>
          {record.share_type !== 'no' && <>
            <div style={{ display: 'flex', marginLeft: 94 }}>
              <ProFormSelect
                width={140}
                name='share_date_type'
                label='抽成期限'
                initialValue='set_date'
                allowClear={false}
                valueEnum={{
                  credit_date: '还款期内',
                  set_date: '指定时间段',
                }}
                readonly
              />
             <ProFormText width='md' name='share_date' readonly />
            </div>
            <ProFormRadio.Group
              name='share_payee'
              label={<>&nbsp;&nbsp;&nbsp;&nbsp;收款方</>}
              initialValue='kb'
              valueEnum={platformValueEnum}
              readonly
            />
          </>} */}
        </AddWrapper>

      </ModalForm>
    </>
  );
};

export default AddContract;
