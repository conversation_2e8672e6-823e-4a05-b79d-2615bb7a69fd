/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const useOptions = () => {
  const statusValueEnum = {
    '-1': '已取消',
    0: '正常',
    2: '异常',
    3: '已还清',
    4: '提前还款',
  };

  const recordStatusValueEnum = {
    0: '扣款中',
    1: '成功',
    '-1': '失败',
  };

  const recordPayMethodValueEnum = {
    balance: '快宝钱包',
    ali_withhold: '支付宝免密',
    aliWallet_withhold: '支付宝专用金',
  };
  const platformValueEnum = {
    kb: '快宝',
    city: '加盟商',
  };
  const payerRoleValueEnum = {
    yz: '驿站',
    city: '加盟商',
  };
  return {
    statusValueEnum,
    platformValueEnum,
    payerRoleValueEnum,
    recordStatusValueEnum,
    recordPayMethodValueEnum,
  };
};
