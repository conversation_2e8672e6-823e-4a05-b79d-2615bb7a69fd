/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { IListItem } from './types';
import React from 'react';
import { useOptions } from './_utils';
import AddContract from './add';
import FeeRecord from './record';
import { ProColumns } from '@/components/AdaptTable';
import OverCommission from './overCommission';

export const useColumns = () => {
  const { statusValueEnum, platformValueEnum, payerRoleValueEnum } = useOptions();
  const columns: ProColumns<IListItem>[] = [
    {
      title: <></>,
      dataIndex: 'phone',
      align: 'center',
      fieldProps: {
        placeholder: '请输入主驿站手机号',
        colon: false,
      },
      hideInTable: true,
      search: {
        transform: val => ({ inn_phone: val }),
      },
    },
    {
      title: '合同编号',
      dataIndex: 'contract_number',
      align: 'center',
      search: false,
    },
    {
      title: '付款方',
      dataIndex: 'payer_role',
      valueType: 'select',
      valueEnum: payerRoleValueEnum,
      align: 'center',
      search: false,
    },
    {
      title: '驿站id',
      dataIndex: 'cm_id',
      align: 'center',
      search: false,
    },
    {
      title: '驿站账号',
      dataIndex: 'inn_phone',
      align: 'center',
      search: false,
    },
    {
      title: '驿站名称',
      dataIndex: 'inn_name',
      align: 'center',
      search: false,
      render(_, { cabinet_name, inn_name }) {
        if (!cabinet_name) return inn_name;
        return `${inn_name}-${cabinet_name}`;
      },
    },
    {
      title: '加盟商',
      dataIndex: 'city_name',
      align: 'center',
      search: false,
    },
    {
      title: '购机款收款方',
      dataIndex: 'cabinet_payee',
      align: 'center',
      valueType: 'select',
      valueEnum: platformValueEnum,
      search: false,
    },
    {
      title: '贷款金额',
      dataIndex: 'credit_money',
      align: 'center',
      search: false,
    },
    {
      title: '扣款金额',
      dataIndex: 'single_deduct_money',
      align: 'center',
      search: false,
      render(dom, { single_deduct_money, deduct_frequency, deduct_standard, deduct_number }) {
        const label = () => {
          if (deduct_standard == 'deliver_number') {
            return deduct_frequency == 'day'
              ? `${single_deduct_money}*投件量/日`
              : deduct_frequency == 'month'
                ? `${single_deduct_money}*投件量/月`
                : '';
          } else if (deduct_standard == 'cells_number') {
            const val =
              deduct_frequency == 'day' ? '/每日' : deduct_frequency == 'month' ? '/每月' : '';
            return `${Number(deduct_number) * Number(single_deduct_money)}${val}`;
          } else {
            const val =
              deduct_frequency == 'day' ? '/每日' : deduct_frequency == 'month' ? '/每月' : '';
            return `${single_deduct_money}${val}`;
          }
        };

        return <>{label()}</>;
      },
    },

    {
      title: '扣款日期',
      dataIndex: 'start_date',
      search: false,
      align: 'center',
      render(dom, { start_date, end_date }) {
        if (!start_date) return '-';
        return `${start_date} ${end_date ? '至' : ''} ${end_date}`;
      },
    },
    {
      title: '扣款状态',
      dataIndex: 'status',
      align: 'center',
      valueEnum: statusValueEnum,
      search: false,
    },
    {
      title: '创建人',
      dataIndex: 'create_time',
      search: false,
      align: 'center',
      render(dom, { operator, create_time }) {
        return (
          <>
            <div>{operator}</div>
            {create_time}
          </>
        );
      },
    },
    {
      title: '合同详情',
      dataIndex: 'detail',
      search: false,
      align: 'center',
      render(_, record, __, action) {
        return <AddContract onSuccess={action?.reload} record={record} trigger={<a>查看</a>} />;
      },
    },
    {
      title: '扣款记录',
      dataIndex: 'record',
      search: false,
      align: 'center',
      render(_, record) {
        return <FeeRecord record={record} />;
      },
    },
    // {
    //   title: '超时费抽成',
    //   dataIndex: 'overCommission',
    //   search: false,
    //   align: 'center',
    //   render(_, record) {
    //     return <OverCommission record={record} />;
    //   },
    // },
  ];
  return {
    columns,
  };
};
