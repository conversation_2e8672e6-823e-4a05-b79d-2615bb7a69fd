/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export interface IListItem {
  id: string;
	contract_number: string;
	kb_id: string;
	kb_type: string;
	cm_id: string;
	cabinet_id: string;
	cabinet_t_kb_id: string;
	cabinet_t_kb_type: string;
	total_money: string;
	first_paid_money: string;
	credit_money: string;
	paid_money: string;
	surplus_credit_money: string;
	deduct_date: string;
	deduct_frequency: string;
	deduct_standard: string;
	deduct_number: string;
	single_deduct_money: string;
	start_date: string;
	end_date: string;
	operator: string;
	status: string;
	create_time: string;
	update_time: string;
	share_id: string;
	share_t_kb_id: string;
	share_t_kb_type: string;
	share_type: string;
	share_ratio: any;
	share_money: string;
	share_status: string;
	share_start_date: string;
	share_end_date: string;
	cabinet_payee: string;
	share_payee: string;
	city_name: string;
	cabinet_name: string;
	inn_phone: string;
	inn_name: string;
	deduct_time: string;
	once_pay_amount: string;
	share_date_type: string;

}

export interface IListRecordItem {
  kb_id: string;
  kb_type: string;
  inner_trade_no: string;
  outer_trade_no: string;
  money: string;
  pay_method: string;
  status: string;
  surplus_credit_money: string;
  date: string;
  create_time: string;
}
