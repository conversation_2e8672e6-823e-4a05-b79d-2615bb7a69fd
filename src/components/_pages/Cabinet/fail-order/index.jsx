/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import React from 'react';
import { Button, Card, Checkbox, Col, Form, Input, Radio, Row, Select } from 'antd';
import { connect } from 'dva';
import ProTableExtend from '@/components/ProTableExtend';
import { Brands } from '@/components/Select';
import PageHeaderLayout from '@/layouts/PageHeaderLayout';
import { useRetransmit } from '@/pages/Post/Retransmit/useRetransmit';
import AuthorizedExtend from '@/components/Authorized/AuthorizedExtend';

const Index = props => {
  const {
    columns,
    request,
    form,
    actionRef,
    pagination,
    rowSelection,
    options,
    handleSearch,
    getFieldDecorator,
    selectedAll,
    handleSelectAll,
    handleReupload,
    disabled,
    search,
  } = useRetransmit({ ...props, is_cabinet: true });

  const { selectedRowKeys } = rowSelection;

  return (
    <PageHeaderLayout title="重传失败单号">
      <Card>
        <Row type="flex" align="middle" gutter={12} style={{ marginBottom: 12 }}>
          <Col>
            <Form layout="inline">
              <Form.Item>
                {getFieldDecorator('dateType', {
                  initialValue: '0',
                })(
                  <Radio.Group>
                    {options.day.map(item => (
                      <Radio.Button key={item.value} value={item.value}>
                        {item.label}
                      </Radio.Button>
                    ))}
                  </Radio.Group>,
                )}
              </Form.Item>
              <Form.Item name="search" label="快递柜">
                {getFieldDecorator('search', {
                  initialValue: '',
                })(<Input placeholder="快递柜名称" />)}
              </Form.Item>
              <Form.Item name="brand" label="快递品牌">
                {getFieldDecorator('brand', {
                  initialValue: '',
                })(<Brands showSearch style={{ width: 200 }} />)}
              </Form.Item>
              <Form.Item name="stockStatus" label="库存状态">
                {getFieldDecorator('stockStatus', {
                  initialValue: '0',
                })(
                  <Select style={{ width: 100 }}>
                    {options.status.map(item => (
                      <Select.Option key={item.value} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
              <Form.Item name="complate" label="平台">
                {getFieldDecorator('complate', {
                  initialValue: 'kb',
                })(
                  <Select placeholder="请选择平台" style={{ width: 250 }}>
                    <Select.Option key="kb" value="kb">
                      kb
                    </Select.Option>
                    {options?.platform?.map(item => (
                      <Select.Option key={item.label} value={item.value}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>,
                )}
              </Form.Item>
            </Form>
          </Col>
          <Col>
            <Button type="primary" onClick={handleSearch}>
              查询
            </Button>
          </Col>
        </Row>
        <ProTableExtend
          rowKey="cm_id"
          columns={columns}
          request={request}
          formRef={form}
          actionRef={actionRef}
          pagination={pagination}
          rowSelection={rowSelection}
          manualRequest
          bordered={false}
        />
        <Row type="flex" align="middle" gutter={12} style={{ marginTop: 12 }}>
          <Col>
            <Checkbox checked={selectedAll} onChange={handleSelectAll}>
              全选
            </Checkbox>
          </Col>
          <AuthorizedExtend auth="2" patchId>
            <Col>
              <Button
                type="primary"
                disabled={!selectedRowKeys.length || disabled || search.complate == 'kb'}
                onClick={() => handleReupload(null, 0)}
              >
                重传快宝
              </Button>
            </Col>
          </AuthorizedExtend>
          <AuthorizedExtend auth="2" patchId>
            <Col>
              <Button
                type="primary"
                disabled={!selectedRowKeys.length || disabled}
                onClick={() => handleReupload(null, 1)}
              >
                重新上传
              </Button>
            </Col>
          </AuthorizedExtend>
        </Row>
      </Card>
    </PageHeaderLayout>
  );
};

const CabinetFailOrder = connect(({ user, setting }) => ({
  userInfo: user.currentUser.user_info,
  quickLoginType: user.currentUser.quickLoginType,
  ...setting,
}))(Form.create()(Index));

export default CabinetFailOrder;
