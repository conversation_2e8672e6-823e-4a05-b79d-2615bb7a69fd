/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import Dexie from 'dexie';

// 定义数据库
export class BatchPatchDB extends Dexie {
  constructor() {
    super('BatchPatchDB');

    // 定义数据库结构
    this.version(1).stores({
      // 扫描数据表，按 scanType 分类存储，使用 waybill + scanType 作为复合主键
      scanData:
        '[scanType+waybill], scanType, waybill, brand, scan_time, errMsg, init, courier_name, courier_phone, dispatched_at, new_dispatch_courier_name, new_dispatch_courier_phone, operatorCourier_name, operatorCourier, signType, badWayBillDesc, next_station_name, vehicle_no, line_name, voucher_no, desc, TE_SHU_JIAN_JIAN_CHA',
    });
  }
}

// 创建数据库实例
export const db = new BatchPatchDB();

// 数据库初始化和错误处理
db.open().catch(error => {
  console.error('IndexedDB 初始化失败:', error);
});
