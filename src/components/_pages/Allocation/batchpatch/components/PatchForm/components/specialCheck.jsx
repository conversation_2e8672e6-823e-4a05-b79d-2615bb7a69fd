/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { useEffect, useMemo, useState } from 'react';
import { Icon, Modal, Radio, Switch } from 'antd';
import { setSpecialConfig } from '@/services/batchPatch';
import { patchScanType } from '../../../_utils/useBatchPatchIndex';

const SpecialCheck = props => {
  const { scanValue, pageData, updatePageData, runSpecialCheckConfig } = props;

  const { specialConfig } = pageData;
  const [checked, setChecked] = useState(false);
  const [visible, setVisible] = useState(false);

  // 特殊件检查开关
  const onSpecialSwitch = async event => {
    if (!event) {
      setVisible(true);
    } else {
      if (showLock) {
        const type = patchScanType.find(item => item.value === scanValue)?.specialConfigType;
        await setSpecialConfig({
          type,
          status: 0,
        });
        await runSpecialCheckConfig();
      }
      updatePageData({
        TE_SHU_JIAN_JIAN_CHA: true,
      });
    }
  };

  const handleCloseModal = async () => {
    if (checked) {
      const type = patchScanType.find(item => item.value == scanValue)?.specialConfigType;
      await setSpecialConfig({
        type,
        status: 1,
      });
      await runSpecialCheckConfig();
    }
    updatePageData({
      TE_SHU_JIAN_JIAN_CHA: false,
    });
    setVisible(false);
  };

  const showLock = useMemo(
    () => {
      return (
        specialConfig.find(
          item => item.type == patchScanType.find(i => i.value == scanValue)?.specialConfigType,
        )?.status == 1
      );
    },
    [specialConfig, scanValue],
  );

  useEffect(
    () => {
      if (visible) {
        setChecked(false);
      }
    },
    [visible],
  );

  return (
    <>
      <Switch checked={pageData.TE_SHU_JIAN_JIAN_CHA} onChange={onSpecialSwitch} />
      {showLock && <Icon type="lock" style={{ fontSize: 20, marginLeft: 10 }} />}
      <Modal
        title="特殊件检查"
        visible={visible}
        onCancel={() => setVisible(false)}
        onOk={handleCloseModal}
        okText="确定关闭"
        cancelText="取消"
        centered
        destroyOnClose
      >
        <div>
          <div>
            确定关闭特殊件检查么?关闭后，本次提交的运单号，执行
            {patchScanType.find(item => item.value == scanValue)?.label}
            上传时不检查特殊件
          </div>
          <br />
          <Radio checked={checked} onClick={() => setChecked(!checked)}>
            每次提交运单号均不检查特殊件
          </Radio>
        </div>
      </Modal>
    </>
  );
};

export default SpecialCheck;
