/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isArray } from 'lodash';
import { filterPoi, mapConfig } from '.';
import { loadScript } from '@/utils/utils';

/**
 *
 * @descriptions 加载百度sdk
 */
function loadBMapGL(): Promise<any> {
  return new Promise(resolve => {
    if (window.BMapGL) {
      resolve(window.BMapGL);
      return;
    }
    const { key, version } = mapConfig.bmap;
    loadScript(`//api.map.baidu.com/getscript?type=webgl&v=${version}&ak=${key}`)
      .then(() => resolve(window.BMapGL || null))
      .catch(() => resolve(window.BMapGL || null));
  });
}

/**
 *
 * @descriptions 百度坐标转换高德
 * @returns
 */
export function convertFrom(
  opts: MAP_ADDRESS.AddressLocation,
  type: MAP_ADDRESS.ConvertFromType = 'baidu',
): Promise<MAP_ADDRESS.AddressLocation> {
  return new Promise(resolve => {
    const { lat, lng } = opts;
    window.AMap.convertFrom([lng, lat], type, (_: MAP_ADDRESS.MapResultStatus, result: any) => {
      const { locations, info } = result || {};
      resolve({
        ...opts,
        ...(info === 'ok' ? locations[0] : null),
      });
    });
  });
}

/**
 *
 * @descriptions 兼容百度定位
 * @returns
 */
export function getCurrentPositionBMap(
  opts: MAP_ADDRESS.CurrentPositionOptions,
): Promise<MAP_ADDRESS.MapAddressSearchValue> {
  return new Promise(resolve => {
    loadBMapGL().then(bMap => {
      if (!bMap) {
        resolve(null);
        return;
      }
      const bGeolocation = new bMap.Geolocation();
      bGeolocation.getCurrentPosition((r: any) => {
        const { point, address } = r || {};
        if (point) {
          const { street, street_number, ...rest } = address;
          convertFrom(point).then(location =>
            resolve({
              name: `${street}${street_number}`,
              location,
              ...rest,
            }),
          );
          return;
        }
        resolve(null);
      }, opts);
    });
  });
}

/**
 *
 * @descriptions 匹配关键词
 * @param keywords
 * @param opts
 * @returns
 */
export function searchTipsBMap(
  keywords: string,
  opts: MAP_ADDRESS.SearchPoiOptions,
): Promise<any[] | null> {
  return new Promise(resolve => {
    const { pageIndex = 1, cityInfo } = opts;
    if (pageIndex > 1) {
      resolve(null);
    } else {
      loadBMapGL().then(bMap => {
        if (!bMap) {
          resolve(null);
          return;
        }
        const onSearchComplete = (results: any) => {
          console.log('onSearchComplete-results', results);
        };
        const { city } = cityInfo || {};
        const autoComplete = new bMap.Autocomplete({
          location: city,
          onSearchComplete,
        });
        autoComplete.search(keywords);
        resolve(null);
      });
    }
  });
}

/**
 *
 * @descriptions 兼容百度搜索poi
 * @returns
 */
let localIns: any = null;
export function searchPoiBMap(keywords: string, opts: MAP_ADDRESS.SearchPoiOptions): Promise<any> {
  return new Promise(resolve => {
    const { cityInfo, pageIndex = 1, pageSize: pageCapacity = 10 } = opts || {};
    const { city = '全国' } = cityInfo || {};

    // 搜索结果处理
    const onSearchComplete = (results: any) => {
      const { _pois } = results || {};
      if (isArray(_pois)) {
        const poiList = {
          count: results.getNumPois(),
          pageIndex: 1 + results.getPageIndex(),
          pageSize: pageCapacity,
          pois: _pois
            .map(
              ({
                address,
                uid: id,
                title: name,
                point: location,
                city: cityname,
                province: pname,
              }) => ({
                address: address ? address.replace(cityname, '') : '',
                id,
                name,
                location,
                pname,
                cityname,
              }),
            )
            .filter(filterPoi),
        };
        resolve(poiList);
      } else {
        resolve(null);
      }
    };

    // 翻页
    if (localIns && pageIndex > 1) {
      localIns.setSearchCompleteCallback(onSearchComplete);
      localIns.gotoPage(pageIndex - 1);
      return;
    }

    loadBMapGL().then(bMap => {
      if (!bMap) {
        resolve(null);
        return;
      }

      const local = new bMap.LocalSearch(city, {
        pageCapacity,
        onSearchComplete,
      });
      local.search(`${keywords} `, { forceLocal: true });
      localIns = local;
    });
  });
}

export function geocoderAddressBMap(
  location: MAP_ADDRESS.AddressLocation,
): Promise<MAP_ADDRESS.AddressInfo | null> {
  return new Promise(resolve => {
    loadBMapGL().then(bMap => {
      if (!bMap) {
        resolve(null);
        return;
      }
      const geocoder = new bMap.Geocoder();
      geocoder.getLocation(location, (res: any) => {
        const { address: detail, addressComponents } = res;
        const { province, city, district } = addressComponents || {};
        resolve(district ? { province, city, district, detail } : null);
      });
    });
  });
}
