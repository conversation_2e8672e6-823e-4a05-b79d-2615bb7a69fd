/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

declare namespace MAP_ADDRESS {
  type ConvertFromType = 'baidu';
  type MapResultStatus = 'complete' | 'error';
  type SwitchMapType = 'amap' | 'bmap';

  interface AddressLocation {
    lat: number;
    lng: number;
  }
  interface AddressInfo {
    province?: string;
    city?: string;
    district?: string;
    detail?: string;
    address?: string;
  }
  interface MapAddressFormatted extends AddressInfo {
    name: string;
    location?: AddressLocation;
  }
  type MapAddressSearchValue = MapAddressFormatted | null;
  interface MapAddressChangeEvent extends React.ChangeEvent<HTMLInputElement> {}
  interface MapAddressCityInfo {
    province: string;
    city: string;
    longitude?: string | number;
    latitude?: string | number;
    location_poi?: string;
  }
  interface MapAddress {
    formattedAddress: string;
    addressComponent: any;
    position: AddressLocation;
  }
  interface MapAddressPoi {
    longitude: number;
    latitude: number;
  }
  interface MapAddressModalPoiProps {
    data?: any;
    type?: string;
  }

  interface MapAddressValue {
    original: string;
    detail?: string;
    province?: string;
    city?: string;
    district?: string;
  }
  type MapAddressRef = React.MutableRefObject<
    { search: (p: MapAddressInputValue | string) => void } | undefined
  >;
  interface MapAddressProps {
    placeholder?: string;
    onChange?: (v: MapAddressSearchValue) => void;
    value?: MapAddressSearchValue;
    actionRef?: MapAddressRef;
    cityInfo?: MapAddressCityInfo | null;
    iconColor?: string;
  }

  interface MapAddressSearchProps {
    open?: boolean;
    ready?: boolean;
    value?: any;
    onChange?: (v: MapAddressSearchValue) => void;
    onOpenMap?: () => void;
    type?: 'map' | 'input';
    className?: string;
    data: MapAddressInputValue;
  }
  interface PoiItem {
    id: string;
    address: string;
    pname: string;
    adname: string;
    cityname: string;
    location: AddressLocation;
    name: string;
  }
  interface PoiItemFormat {
    name: string;
    province: string;
    city: string;
    district: string;
    detail: string;
    location: AddressLocation;
  }

  interface MapAddressModalProps {
    onMapCreated?: (aMap: any) => void;
    onChange?: (v: MapAddressSearchValue) => void;
    data: MapAddressInputValue;
    cityInfo?: any;
  }

  type MapAddressInputValue = {
    province?: string;
    city?: string;
    keywords?: string;
  } | null;

  type MapAddressInputValueType = 'click' | 'change';

  interface MapAddressInputProps {
    ready?: boolean;
    suffix?: React.ReactNode | string;
    placeholder?: string;
    value: MapAddressInputValue;
    onChange: (v: MapAddressInputValue, t?: MAP_ADDRESS.MapAddressInputValueType) => void;
    onOpenDropdown?: (v: MapAddressInputValue, t?: MAP_ADDRESS.MapAddressInputValueType) => void;
    disableSearchWhenClick?: boolean;
  }
  interface CurrentPositionOptions {
    enableHighAccuracy: boolean; // 是否使用高精度定位，默认:true
    timeout: number; // 默认：无穷大
    maximumAge: number; // 默认：0
  }
  interface SearchPoiOptions {
    pageSize?: number;
    pageIndex?: number;
    cityInfo?: MapAddressCityInfo | null;
    mapType?: SwitchMapType;
  }
  interface SearchPoiResult {
    count: number;
    pageIndex: number;
    pageSize: number;
    pois: PoiItem[];
  }
}
