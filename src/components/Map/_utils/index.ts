/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { message } from 'antd';
import { isArray } from 'lodash';
import { convertFrom, searchPoiBMap } from './bmap';
import { loadScript } from '@/utils/utils';
import { getStorageSync, setStorage } from '@/utils/storage';

// 默认城市信息
const mapAddressCityInfoDefault = { province: '上海', city: '上海' };

// 过滤pois列表
export function filterPoi({ address }: { address: string }): any {
  return typeof address === 'string' && address;
}

// 地图配置
export const mapConfig = {
  // 高德
  amap: {
    key:
      process.env.NODE_ENV === 'production'
        ? 'f1e8b726c0bab42764b8d0626f8ff41a'
        : '26201e6960951bae78c3105e58aa118b',
    version: '1.4.15',
  },
  // 百度
  bmap: {
    key:
      process.env.NODE_ENV === 'production'
        ? 'Ns6yIjB5EDQbzKYKPuIVDDKt9p1HAG1x'
        : 'qxhRGkCd5TNU6RGhpTw45gDz2MRiflo7',
    version: '1.0',
  },
};

/**
 *
 * @descriptions 加载高德地图
 * @returns
 */
export function loadAMap(): Promise<any> {
  return new Promise(resolve => {
    if (window.AMap) {
      resolve(window.AMap);
      return;
    }
    const { key, version } = mapConfig.amap;
    if (!window.__amap_init_callback) {
      window.__amap_init_callback = () => {};
    }
    loadScript(`//webapi.amap.com/maps?v=${version}&key=${key}&callback=__amap_init_callback`)
      .then(() => resolve(window.AMap || null))
      .catch(() => resolve(window.AMap || null));
  });
}

function fixMapKey(k: string) {
  return `AMap.${k}`;
}
/**
 *
 * @descriptions 地图插件通用方法
 * @param key
 * @param callback
 * @returns
 */
function mapPluginUse(key: string | string[], callback: (a: any) => void) {
  loadAMap().then(aMap => {
    if (!aMap) {
      console.error('地图未加载');
      return;
    }
    const keyIsArray = isArray(key);
    if (keyIsArray ? key.every(ik => aMap[ik]) : aMap[key]) {
      callback(aMap);
      return;
    }
    aMap.plugin(keyIsArray ? key.map(fixMapKey) : fixMapKey(key), () => callback(aMap));
  });
}

/**
 * @descriptions 获取城市信息并缓存
 * @returns
 */

let mapAddressCityInfo: MAP_ADDRESS.MapAddressCityInfo | null = null;
export function getCityInfoAndRecord(): Promise<MAP_ADDRESS.MapAddressCityInfo> {
  return new Promise(resolve => {
    if (mapAddressCityInfo) {
      resolve(mapAddressCityInfo);
      return;
    }
    getCityInfo()
      .then(cityInfo => {
        mapAddressCityInfo = cityInfo || null;
        resolve({
          ...mapAddressCityInfoDefault,
          ...cityInfo,
        });
      })
      .catch(() => {
        resolve(mapAddressCityInfoDefault);
      });
  });
}

/**
 *
 * @descriptions 搜索提示
 * @param keywords
 * @param pageIndex
 * @param opts
 * @returns
 */
export function searchTips(
  keywords: string,
  opts: MAP_ADDRESS.SearchPoiOptions,
): Promise<any[] | null> {
  return new Promise(resolve => {
    const { pageIndex = 1, cityInfo } = opts;

    if (pageIndex > 1) {
      resolve(null);
    } else {
      const { province = '', city = '' } = cityInfo || {};
      mapPluginUse('Autocomplete', aMap => {
        const autoComplete = new aMap.Autocomplete({
          city,
          citylimit: true,
          datatype: 'poi',
        });
        autoComplete.search(keywords, (_: MAP_ADDRESS.MapResultStatus, result: any) => {
          const { tips } = result || {};
          resolve(
            isArray(tips)
              ? tips.map(({ district, ...restTip }) => {
                  const r = province === city ? province : province + city;
                  const adname = `${district}`.replace(r, '');
                  // 未替换掉省市，选择时补充，此处不做处理；
                  if (adname === district) return restTip;
                  return {
                    pname: province,
                    cityname: city,
                    adname,
                    ...restTip,
                  };
                })
              : null,
          );
        });
      });
    }
  });
}

/**
 *
 * @descriptions 搜索poi
 * @param keywords
 * @param opts
 * @returns
 */
export function searchPoi(
  keywords: string,
  opts: MAP_ADDRESS.SearchPoiOptions,
): Promise<MAP_ADDRESS.SearchPoiResult> {
  return new Promise(resolve => {
    const { cityInfo: cityInfoOpts, mapType, pageIndex = 1, ...restOpts } = opts || {};
    if (mapType === 'bmap') {
      // 兼容百度
      searchPoiBMap(keywords, opts).then(resolve);
      return;
    }
    mapPluginUse('PlaceSearch', aMap => {
      const trigger = (cityInfo: MAP_ADDRESS.MapAddressCityInfo) => {
        const { city } = cityInfo;

        const placeSearch = new aMap.PlaceSearch({
          city,
          citylimit: true,
          extensions: 'all',
          pageSize: 10, // 每页结果数,默认10
          pageIndex, // 请求页码，默认1
          ...restOpts,
        });

        placeSearch.setPageIndex(pageIndex);
        placeSearch.search(keywords, (_: MAP_ADDRESS.MapResultStatus, result: any) => {
          // 搜索成功时，result即是对应的匹配数据
          searchTips(keywords, opts).then(tips => {
            const { poiList } = result || {};
            let { pois = null } = poiList || {};
            console.log(tips, '-----', keywords);
            if (tips) {
              if (isArray(pois)) {
                // 过滤与tips重复的
                pois = pois.filter(poi => !tips.find(tip => tip.id === poi.id));
              }
              pois = tips.concat(pois || []);
            }
            resolve({
              ...poiList,
              pois,
            });
          });
        });
      };
      if (cityInfoOpts && cityInfoOpts.city) {
        // 传入城市，以传入为主
        trigger(cityInfoOpts);
        return;
      }
      getCityInfoAndRecord().then(trigger);
    });
  });
}

/**
 *
 * @descriptions 获取城市信息
 * @params callback 传入callback可作为精准定位
 * @returns
 */
export function getCityInfo(): Promise<MAP_ADDRESS.MapAddressCityInfo | null> {
  return new Promise(resolve => {
    mapPluginUse('Geolocation', aMap => {
      const geolocation = new aMap.Geolocation();
      geolocation.getCityInfo((status: MAP_ADDRESS.MapResultStatus, result: any) => {
        resolve(status === 'complete' ? result : null);
      });
    });
  });
}

export const getCityInfoByCity = (city: string) => {
  return new Promise(resolve => {
    mapPluginUse('Geocoder', aMap => {
      const geocoder = new aMap.Geocoder({
        city,
      });
      geocoder.getLocation(city, (status: MAP_ADDRESS.MapResultStatus, result: any) => {
        const geocodes = status === 'complete' ? result : null;
        console.log(result, status, 'getCityInfoByCity')
        if (geocodes?.geocodes?.length) {
          const { location } = geocodes?.geocodes?.[0] || {};
          resolve(location);
        } else {
          resolve(null);
        }
      });
    });
  });
};

/**
 *
 * @descriptions 获取精准定位信息
 * @returns
 */
class PositionRecord {
  value: MAP_ADDRESS.MapAddressSearchValue;

  key: string;

  constructor() {
    this.value = null;
    this.key = 'position';
  }

  set(v: MAP_ADDRESS.MapAddressSearchValue) {
    this.value = v;
    setStorage({
      key: this.key,
      data: v,
    });
  }

  get(): MAP_ADDRESS.MapAddressSearchValue {
    this.value =
      // @ts-ignore
      this.value || getStorageSync<MAP_ADDRESS.MapAddressSearchValue>(this.key).data || null;
    return this.value;
  }
}
const posIns = new PositionRecord();
export function getCurrentPosition(): Promise<MAP_ADDRESS.MapAddressSearchValue> {
  return new Promise(resolve => {
    mapPluginUse('Geolocation', aMap => {
      const { chrome, ua = '' } = aMap.Browser;
      const timeout = chrome && !ua.toLowerCase().includes('edg/') ? 300 : 5000;
      const opts = {
        GeoLocationFirst: true,
        enableHighAccuracy: true, // 是否使用高精度定位，默认:true
        timeout, // 默认：无穷大|百度：10秒
        maximumAge: 10 * 60000, // 默认：0|百度：10分钟
      };
      const geolocation = new aMap.Geolocation(opts);
      // 执行精确定位
      geolocation.getCurrentPosition((_: MAP_ADDRESS.MapResultStatus, pResult: any) => {
        const curPosition = formatMapAddress(pResult);
        if (curPosition) {
          posIns.set(curPosition);
          resolve(curPosition);
          return;
        }

        resolve(posIns.get());

        // 借助百度定位
        // getCurrentPositionBMap(opts).then((bMapPosition) => {
        //   const { location } = bMapPosition || {};
        //   if (location) {
        //     geocoderAddress(location).then(resolve);
        //     return;
        //   }
        //   // 粗定位补充
        //   geolocation.getCityInfo((status: MAP_ADDRESS.MapResultStatus, cResult: any) => {
        //     if (status === 'complete' && cResult) {
        //       const { center, province, city } = cResult;
        //       const [lng, lat] = center || [];
        //       resolve({
        //         name: '',
        //         province,
        //         city,
        //         location: {
        //           lng,
        //           lat,
        //         },
        //       });
        //     } else {
        //       resolve(null);
        //     }
        //   });
        // });
      });
    });
  });
}

/**
 *
 * @desc
 * @param descriptions 格式化地图地址
 * @returns
 */
function formatMapAddress(adr: MAP_ADDRESS.MapAddress): MAP_ADDRESS.MapAddressFormatted | null {
  const { formattedAddress = '', addressComponent, position } = adr || {};
  if (!formattedAddress && !addressComponent) return null;
  let { city } = addressComponent || {};
  const { neighborhood, building, province, district, township } = addressComponent || {};
  const area = [province, city, district, township].filter(item => !!item).join('');
  if (!city) {
    city = province;
  }
  const detail = formattedAddress !== area ? `${formattedAddress}`.replace(area, '') : township;
  const name = neighborhood || building || detail;
  return {
    name,
    province,
    city,
    district: district || city,
    detail: name === detail ? '' : detail,
    location: position,
  };
}

/**
 *
 * @descriptions 逆地理编码（坐标 -> 地址）
 * @params opts
 * @returns
 */
export function geocoderAddress(
  opts: MAP_ADDRESS.AddressLocation,
): Promise<MAP_ADDRESS.MapAddressSearchValue> {
  return new Promise(resolve => {
    mapPluginUse('Geocoder', aMap => {
      const geocoder = new aMap.Geocoder({
        // city: '全国', // 默认：“全国”
        // radius: 1000, // 范围，默认：500
        extensions: 'all',
      });
      const { lat, lng } = opts;
      geocoder.getAddress([lng, lat], (status: MAP_ADDRESS.MapResultStatus, result: any) => {
        const { regeocode } = result || {};
        if (status === 'complete' && regeocode) {
          const { pois } = regeocode;
          const poi = isArray(pois) ? pois[0] : null;
          const info = formatMapAddress({
            position: { lat, lng },
            ...regeocode,
          });
          if (poi && info) {
            // 使用poi
            const { address, location, name } = poi as any;
            info.name = name;
            info.location = location;
            info.detail = address;
          }
          resolve(info);
        } else {
          resolve(null);
        }
      });
    });
  });
}

export type SwitchMapType = 'amap' | 'bmap';
export interface MapListItem {
  key: SwitchMapType;
  label: string;
}
export const mapList: MapListItem[] = [
  {
    key: 'amap',
    label: '高德地图',
  },
  {
    key: 'bmap',
    label: '百度地图',
  },
];

/**
 *
 * @description 合并触发坐标转化
 * @param mapType
 * @param location
 * @returns
 */
export function triggerConvertFromBMap(
  mapType: MAP_ADDRESS.SwitchMapType | undefined,
  location: MAP_ADDRESS.AddressLocation,
): Promise<MAP_ADDRESS.AddressLocation> {
  return new Promise(resolve => {
    if (mapType !== 'bmap') {
      resolve(location);
      return;
    }
    convertFrom(location).then(resolve);
  });
}

/**
 *
 * @descriptions 格式化poiItem 翻转
 * @param item
 * @returns
 */
export function formatPoiItemReverse(
  item: MAP_ADDRESS.PoiItemFormat | null = null,
): MAP_ADDRESS.PoiItem | null {
  // @ts-ignore
  if (!item) return item;
  const { name, province, city, district, detail, location } = item;
  return {
    id: '0',
    adname: district,
    address: detail,
    cityname: city,
    pname: province,
    name,
    location,
  };
}

/**
 *
 * @description 格式化poiItem
 * @param mapType
 * @param item
 * @returns
 */
export function formatPoiItem(
  mapType: MAP_ADDRESS.SwitchMapType | undefined,
  item: MAP_ADDRESS.PoiItem,
): Promise<MAP_ADDRESS.PoiItemFormat> {
  return new Promise(resolve => {
    const {
      adname: district,
      address: detail,
      cityname: city,
      pname: province,
      name,
      location,
    } = item;
    // 百度坐标转换
    triggerConvertFromBMap(mapType, location).then(l => {
      const itemData = {
        name,
        province,
        city,
        district,
        detail,
        location: l,
      };
      if (district) {
        resolve(itemData);
      } else {
        // poi搜索缺少区，通过地址解析补充
        geocoderAddress(l).then(gaddress => {
          if (!gaddress) {
            message.error(`省市区解析失败，请重试！[${mapType || 'amap'}]`);
            return;
          }
          const { province: p = '', city: c = '', district: d = '' } = gaddress;
          resolve({
            ...itemData,
            province: p,
            city: c,
            district: d,
          });
        });
      }
    });
  });
}

/**
 *
 * @descriptions 合并radio值
 * @param v
 * @returns
 */
export function mergeRadioValue(v: MAP_ADDRESS.MapAddressSearchValue) {
  const { name: valueName = '', detail: valueDetail = '' } = v || {};
  return valueName + valueDetail;
}
