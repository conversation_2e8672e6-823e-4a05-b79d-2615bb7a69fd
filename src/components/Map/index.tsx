/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import {
  Map,
  Marker,
  Markers,
  Polygon,
  PolyEditor,
  Polyline,
  MapProps,
  MarkerProps,
  PolygonProps,
  PolyEditorProps,
  PolylineProps,
} from 'react-amap';
import { Spin } from 'antd';
import { mapConfig } from './_utils';

const { key: amapkey, version: amapversion } = mapConfig.amap;

/**
 * 地图
 * @param props
 * @returns
 */
const KBMap: React.FC<MapProps> = props => {
  return (
    // @ts-ignore
    <Map
      zoom={13}
      {...props}
      loading={
        <Spin
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            transform: 'translate(-50%,-50%)',
          }}
          spinning
        />
      }
      amapkey={amapkey}
      version={amapversion}
    >
      {props.children}
    </Map>
  );
};

/**
 * 地图marker
 * @param props
 * @returns
 */
export const KBMarker: React.FC<MarkerProps> = props => {
  return (
    // @ts-ignore-next
    <Marker {...props} />
  );
};

/**
 * 地图markers
 * @param props
 * @returns
 */
export const KBMarkers: React.FC<any> = props => {
  return (
    // @ts-ignore-next
    <Markers {...props} />
  );
};

/**
 * 地图Polygon
 * @param props
 * @returns
 */
export const KBPolygon: React.FC<PolygonProps> = props => {
  return (
    // @ts-ignore-next
    <Polygon {...props} />
  );
};

/**
 * 地图Polyline
 * @param props
 * @returns
 */
export const KBPolyline: React.FC<PolylineProps> = props => {
  return (
    // @ts-ignore-next
    <Polyline {...props} />
  );
};

/**
 * 地图PolyEditor
 * @param props
 * @returns
 */
export const KBPolyEditor: React.FC<PolyEditorProps> = props => {
  return (
    // @ts-ignore-next
    <PolyEditor {...props} />
  );
};

export default KBMap;
