/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React from 'react';
import styles from './index.less';

const MapAddressModalPoi: React.FC<MAP_ADDRESS.MapAddressModalPoiProps> = props => {
  const { data, type } = props;

  return data && data.name ? (
    <div className={type == 'stop' ? styles.poiStop : styles.poi}>
      <div className={styles.poiName}>{data.name}</div>
      {data.detail && <div className={styles.poiDetail}>{data.detail}</div>}
    </div>
  ) : null;
};

export default MapAddressModalPoi;
