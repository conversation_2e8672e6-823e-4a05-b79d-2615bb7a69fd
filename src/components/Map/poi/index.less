/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

.poi {
  position: absolute;
  top: 0;
  left: 0;
  padding: 8px 12px;
  color: #fff;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  box-shadow: 0 3px 3px 3px rgba(0, 0, 0, 0.05);
  transform: translate(-50%, -100%);
  &::after {
    position: absolute;
    bottom: -10px;
    left: 50%;
    display: inline-block;
    width: 0;
    height: 0;
    border: 10px solid rgba(0, 0, 0, 0.8);
    border-right-color: transparent;
    border-bottom: 0;
    border-left-color: transparent;
    transform: translateX(-50%);
    content: '';
  }
  &-name,
  &-detail {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  &-name {
    font-weight: bold;
  }
  &-detail {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
  }
}

.poiStop {
  position: relative;
  color: #fff;
  white-space: nowrap;
  background-color: #1785ff;
  border-radius: 20px;
  height: 28px;
  line-height: 28px;
  padding-left: 40px;
  padding-right: 20px;
  box-sizing: border-box;
  &::after {
    content: '停';
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #fff;
    text-align: center;
    line-height: 28px;
    position: absolute;
    color: #1785ff;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
