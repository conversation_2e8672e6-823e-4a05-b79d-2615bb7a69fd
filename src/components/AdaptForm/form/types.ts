/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { ButtonProps } from 'antd/lib/button';
import { string } from 'prop-types';
import React from 'react';

export type NamePath = string;
export interface KBProFormInstance<Values = any> {
  getFieldValue: (name: NamePath) => any;
  getFieldDecorator: any;
  getFieldsValue: (() => Values) &
    ((nameList: NamePath[] | true, filterFunc?: any) => any) &
    ((config: any) => any);
  getFieldError: (name: NamePath) => string[];
  getFieldsError: (nameList?: NamePath[]) => any[];
  getFieldWarning: (name: NamePath) => string[];
  isFieldsTouched: ((nameList?: NamePath[], allFieldsTouched?: boolean) => boolean) &
    ((allFieldsTouched?: boolean) => boolean);
  isFieldTouched: (name: NamePath) => boolean;
  isFieldValidating: (name: NamePath) => boolean;
  isFieldsValidating: (nameList?: NamePath[]) => boolean;
  resetFields: (fields?: NamePath[]) => void;
  setFields: (fields: any[]) => void;
  setFieldValue: (name: NamePath, value: any) => void;
  setFieldsValue: (values: any) => void;
  validateFields: any;
  submit: () => void;
  onValuesChange?: (changedValues?: any, values?: any) => void;
}

export type KBModalFormProps<T = Record<string, any>, U = Record<string, any>> = {
  className?: string;
  open?: boolean;
  submitter?: {
    submitButtonProps?: ButtonProps
    searchConfig?:{submitText?: string, resetText?:string}
    resetButtonProps?:ButtonProps
  };
  layout?: 'horizontal' | 'inline' | 'vertical';
  labelAlign?:'left' | 'right'
  /**
   * 接收任意值，返回 真值 会关掉这个抽屉
   *
   * @name 表单结束后调用
   *
   * @example 结束后关闭抽屉
   * onFinish: async ()=> {await save(); return true}
   *
   * @example 结束后不关闭抽屉
   * onFinish: async ()=> {await save(); return false}
   */
  onFinish?: (formData: T) => Promise<boolean>;

  /** @name 提交数据时，禁用取消按钮的超时时间（毫秒）。 */
  submitTimeout?: number;

  /** @name 用于触发抽屉打开的 dom */
  trigger?: JSX.Element;

  /** @name 受控的打开关闭 */

  /**
   * @deprecated use onOpenChange replace
   */
  onVisibleChange?: (visible: boolean) => void;
  /**
   * @deprecated use open replace
   */
  visible?: boolean;

  /** @name 打开关闭的事件 */
  onOpenChange?: (open: boolean) => void;
  /**
   * 不支持 'visible'，请使用全局的 visible
   *
   * @name 弹框的属性
   */
  modalProps?: Omit<KBModalProps, 'visible'>;

  /** @name 弹框的标题 */
  title?: KBModalProps['title'];

  /** @name 弹框的宽度 */
  width?: KBModalProps['width'];
};

export interface KBModalProps {
  /** Whether to apply loading visual effect for OK button or not */
  confirmLoading?: boolean;
  /** The modal dialog's title */
  title?: React.ReactNode;
  /** Whether a close (x) button is visible on top right of the modal dialog or not. Recommend to use closeIcon instead. */
  closable?: boolean;
  /** Specify a function that will be called when a user clicks the OK button */
  onOk?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  /** Specify a function that will be called when a user clicks mask, close button on top right or Cancel button */
  onCancel?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  afterClose?: () => void;
  /** Callback when the animation ends when Modal is turned on and off */
  afterOpenChange?: (open: boolean) => void;
  /** Centered Modal */
  centered?: boolean;
  /** Width of the modal dialog */
  width?: string | number;
  /** Footer content */
  footer?: React.ReactNode;
  /** Text of the OK button */
  okText?: React.ReactNode;
  /** Button `type` of the OK button */
  okType?: any;
  /** Text of the Cancel button */
  cancelText?: React.ReactNode;
  /** Whether to close the modal dialog when the mask (area outside the modal) is clicked */
  maskClosable?: boolean;
  /** Force render Modal */
  forceRender?: boolean;
  okButtonProps?: ButtonProps;
  cancelButtonProps?: ButtonProps;
  destroyOnClose?: boolean;
  style?: React.CSSProperties;
  wrapClassName?: string;
  maskTransitionName?: string;
  transitionName?: string;
  className?: string;
  rootClassName?: string;

  zIndex?: number;
  bodyStyle?: React.CSSProperties;
  /** @deprecated Please use `styles.mask` instead */
  maskStyle?: React.CSSProperties;
  mask?: boolean;
  keyboard?: boolean;
  wrapProps?: any;
  prefixCls?: string;
  closeIcon?: boolean | React.ReactNode;
  modalRender?: (node: React.ReactNode) => React.ReactNode;
  focusTriggerAfterClose?: boolean;
  children?: React.ReactNode;
  visible?: boolean;
}

export interface IProForm  {
  onValuesChange?:(changeValue?:Record<string,any>,values?:Record<string,any>)=> void;
  formRef?:React.Ref<KBProFormInstance | undefined>;
  children?: React.ReactNode;
  layout?: 'horizontal' | 'inline' | 'vertical';
  style?:React.CSSProperties;
  form?:any
  initialValues?:Record<string,any>
}
export interface ICom {

  _tableColumns?: boolean;
  name?: NamePath;
  value?:any;
  rules?: any[];
  initialValue?: any;
  label?: React.ReactNode;
 /**
  * @type xs=104px 适用于短数字、短文本或选项。
  * @type sm=216px 适用于较短字段录入、如姓名、电话、ID 等。
  * @type md=328px 标准宽度，适用于大部分字段长度。
  * @type lg=440px 适用于较长字段录入，如长网址、标签组、文件路径等。
  * @type xl=552px 适用于长文本录入，如长链接、描述、备注等，通常搭配自适应多行输入框或定高文本域使用。
  */
  width?: number | 'sm' | 'md' | 'xl' | 'xs' | 'lg';
  placeholder?: string;
  colon?:boolean;
  disabled?:boolean;
  onChange?:(v?:any,opt?:any)=> void
  allowClear?:boolean;
  fieldProps?:Record<string,any>
  readonly?:boolean;
  readOnly?:boolean;
  getValueFromEvent?:(e)=> any
}

export interface IOptions {
  request?: (v?: any) => Promise<IOptionsType>;
  params?: Record<string, any>;
  valueEnum?: any;
  options?: {label:React.ReactNode,value:string}[];
}

export type IOptionsType = {label:string,value:string}[]

export type IProFormSelect  = {
options?: IOptionsType
} & IOptions & ICom

export interface IKBProFormText extends ICom {
  fieldProps?:{
    prefix?: React.ReactNode;
    addonBefore?:React.ReactNode;
    addonAfter?:React.ReactNode;
    readOnly?:boolean;
  }

}
export type IKBProRadio = {} & IOptions & ICom
export interface IKBProFormSwitch extends ICom {

}
export interface IKBProFormSwitch extends ICom {

}
export interface IKBProFormCheckBox extends ICom {
  options?: IOptionsType
  checked?:boolean;
  indeterminate?:boolean;
}
export interface IKBProFormDateRangePicker extends ICom {
  dataFormat?:string
}
export interface IKBProFormDigit extends ICom {
  precision?: number;
  min?: number;
  max?: number;
}
