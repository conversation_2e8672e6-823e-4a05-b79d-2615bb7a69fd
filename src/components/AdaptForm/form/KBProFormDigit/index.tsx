/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { InputNumber } from 'antd';
import React,{ FC } from 'react';
import { IKBProFormDigit } from '../types';
import KBProFormWrapper from '../KBProFormWrapper';

const KBProFormDigit:FC<IKBProFormDigit> = props => {

  return (
    <>
      <KBProFormWrapper placeholder='请输入' {...props} comp={<InputNumber />}/>
    </>
  );
};

export default KBProFormDigit;
