/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Radio } from 'antd';
import { useOptRequest } from '../_utils/useOptRequest';
import { IProFormSelect } from '../types';
import React,{ FC } from 'react';
import KBProFormWrapper from '../KBProFormWrapper';
const Group:FC<IProFormSelect> = props => {

  const {options} = useOptRequest(props)
  return (
    <>
      <KBProFormWrapper  {...{...props,options}}  comp={<Radio.Group options={options} />}/>
    </>
  );
};

export default Group;
