/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Radio } from 'antd';
import React,{ FC } from 'react';
import { IKBProRadio } from '../types';
import KBProFormWrapper from '../KBProFormWrapper';
const KBProRadio:FC<IKBProRadio> = props => {

  return (
    <>
      <KBProFormWrapper {...props} comp={<Radio  />}  />
    </>
  );
};

export default KBProRadio as {
  (props: IKBProRadio): JSX.Element;
  Group: (props: IKBProRadio) => JSX.Element;
};

