/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Switch } from 'antd';
import React,{ FC } from 'react';
import { IKBProFormSwitch } from '../types';
import KBProFormWrapper from '../KBProFormWrapper';


const KBProFormSwitch:FC<IKBProFormSwitch> = props => {
  const {  width } = props;
  const Wrapper = (p:any) => {
    const {value,...rest} = p
    return  <Switch checked={value} {...rest} style={{ width }} onChange={(checked,e)=> {
      rest?.onChange?.(checked,e)
    }}/>
  }
  Object.defineProperty(Wrapper, 'name', { value: 'Switch' })
  return (
    <>
     <KBProFormWrapper  {...props} comp={<Wrapper />} />
    </>
  );
};

export default KBProFormSwitch;
