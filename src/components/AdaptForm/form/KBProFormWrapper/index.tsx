/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import KBProForm from '../KBProForm';
import React, { FC, cloneElement } from 'react';
import { ICom } from '../types';
import { useOnValuesChange } from '../_utils/useOnValuesChange';
import { getWidth } from '../_utils';
import { isFunction, isNumber } from 'lodash';
import { FormItemWrapper } from './styles';
import { tansformReadOnlyLabel } from '../_utils/tansformReadOnlyLabel';
import { patchGetValueFromEvent } from '../_utils/patchGetValueFromEvent';

// 兼容不给 name 报错
export const NAME = '__name__'
const Wrapper = (props) => {

  const { width, style, comp, ...rest } = props
  const { onChange } = useOnValuesChange(props)
  const readOnly = rest.readonly ||  rest.readOnly
  if(readOnly) {
     const val = tansformReadOnlyLabel(props)

    return  <span style={style}>{val || '-'}</span>
  }
  const value = patchGetValueFromEvent(props)
  return cloneElement(comp, {
    ...rest,
    value,
    onChange,
    style: { width: getWidth(width), ...style },
  })
}

const KBProFormWrapper: FC<ICom & { comp: React.ReactNode }> = props => {

  const { name = NAME, rules, initialValue: propsInitialValue, label, colon, fieldProps = {}, ...rest } = props;

  const [form] = KBProForm.useForm();
  const { initialValues, layout, labelAlign = 'left' } = KBProForm._useProps()
  const initialValue = propsInitialValue || initialValues?.[name]
  if(!isFunction(form.getFieldDecorator)) {
    console.error('请使用 AdaptWrapperFn 包裹组件')

  }
  const formFn = form.getFieldDecorator(name, { initialValue, rules,})
  const dom = formFn(<Wrapper name={name} {...fieldProps} {...rest}  />)
  if(props._tableColumns) {
    return <Wrapper name={name} {...fieldProps} {...rest}/>
  }
  return (
    <FormItemWrapper layout={layout} labelAlign={labelAlign} >
      <KBProForm.Item label={label} colon={colon} className="ant-pro">
        {dom}
      </KBProForm.Item>
    </FormItemWrapper>
  );
};

export default KBProFormWrapper;
