/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import styled from 'styled-components';

export const FormItemWrapper= styled.div<{layout?:"horizontal" | "inline" | "vertical",labelAlign?:'left'| 'right'}>`
    width:100%;
  .ant-pro .ant-form-item-label {
      text-align:  ${(props)=> props.layout == 'horizontal' ? 'right' : props.labelAlign};
      width:25%;
    }
  .ant-pro .ant-form-item-control-wrapper {
    }
`;

