/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { FC, createContext, useContext, useImperativeHandle } from 'react';
import { KBProFormWrapperContext } from '../AdaptWrapper';
import { IProForm, KBModalFormProps, KBProFormInstance, NamePath } from '../types';
import KBProFormItem from '../KBProFormItem';
import React from 'react';
import { FormItemProps } from 'antd/lib/form';
export const KBProFormContext = createContext({}) as any;

const KBProForm = (props: IProForm) => {
  const _form = useContext(KBProFormWrapperContext);
  useImperativeHandle(props.formRef, () => _form as any);

  return <KBProFormContext.Provider value={props}>
    <div style={props.style}>
      {props.children}
    </div>
  </KBProFormContext.Provider>;
};
KBProForm.useForm = (form?: KBProFormInstance) => {

  const _form = form ?? useContext(KBProFormWrapperContext);
  return [_form];
};
KBProForm.useWatch = (name: NamePath, form?: KBProFormInstance) => {
  const _form = form || useContext(KBProFormWrapperContext) as KBProFormInstance
  const val = _form.getFieldValue(name)
  return val
}
KBProForm._useProps = () => {
  const props = useContext(KBProFormContext);
  return props
}
KBProForm.Item = KBProFormItem

export default KBProForm as {
  (props: IProForm): JSX.Element;
  useForm: (form?: KBProFormInstance) => [KBProFormInstance];
  _useProps: () => Partial<KBModalFormProps & IProForm>;
  useWatch: (name: NamePath, form?: KBProFormInstance) => any;
  Item: (props: { children?: any } & FormItemProps) => JSX.Element
};
