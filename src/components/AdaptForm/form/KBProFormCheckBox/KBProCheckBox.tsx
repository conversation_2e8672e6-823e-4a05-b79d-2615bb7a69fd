/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Checkbox } from 'antd';
import React,{ FC } from 'react';
import { IKBProFormCheckBox } from '../types';
import KBProFormWrapper from '../KBProFormWrapper';

const KBProCheckBox:FC<IKBProFormCheckBox> = props => {

  return (
    <>
      <KBProFormWrapper {...props} comp={<Checkbox />}/>
    </>
  );
};

export default KBProCheckBox as {
  (props: IKBProFormCheckBox): JSX.Element;
  Group: (props: IKBProFormCheckBox) => JSX.Element;
};

