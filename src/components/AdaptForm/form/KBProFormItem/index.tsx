/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { FC } from "react"
import KBProForm from '../KBProForm';
import { Form } from 'antd';
import { FormItemProps } from "antd/lib/form";
const FormItem = Form.Item
const KBProFormItem:FC<FormItemProps> = (props) => {
  const { ...rest } = props;
  const { layout } = KBProForm._useProps()
  return <>
   <FormItem  style={{ display: layout === 'horizontal' ? 'flex' : undefined }} {...rest} >
        {props.children}
      </FormItem>
  </>
}

export default KBProFormItem
