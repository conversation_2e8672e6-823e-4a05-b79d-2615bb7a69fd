/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useEffect, useState } from "react"
import { IOptionsType } from "../types";

interface IRequest {
  request?:(v?:any)=> Promise<IOptionsType>;
  params?:Record<string,any>;
  valueEnum?:any;
  options?:IOptionsType
}
export const useOptRequest = (props:IRequest) => {
  const {request,params,valueEnum,options:propsOpt } = props
  const [options,setOptions] = useState<IOptionsType>([])

  useEffect(()=> {
  const _options = propsOpt ?? Object.keys(valueEnum ||  {}).map((key) => ({ label: valueEnum[key], value: key }))
    setOptions(_options)
  },[])
  useEffect(()=> {
    if(request) {
      request(params).then((opts)=> {
        setOptions(opts || [])
      })
    }
  },[request,params])
  return {
    options
  }
}

