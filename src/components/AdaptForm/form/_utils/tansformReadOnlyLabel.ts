/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

export const tansformReadOnlyLabel = (props)=> {

  const options = props?.options
  const value = props.value || props.tableColumnsValue
  if(!options ||  Array.isArray(options) && options.length == 0) return value
  const item = options.find(i=> i.value === value)
  if(item) return item.label
  return value

}
