/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { runFunction } from "@/utils/runFunction";
import KBProForm from "../KBProForm";
import { isArray, isFunction, isNumber } from "lodash";
import moment from "moment";
import { useRef } from "react";
const FORMAt = 'YYYY-MM-DD'
export const useOnValuesChange = (props: any) => {
  const { name } = props
  const { onValuesChange = props.onValuesChange } = KBProForm._useProps();
  const [form] = KBProForm.useForm();

  const onChange = (e:any,opt?:any,o?:any) => {
    let val = e?.target?.value || e?.target?.checked || e
    runFunction(props.onChange,e,opt,o)
    const values = form.getFieldsValue()
    val = handleformatDate(val)
    const _val = { [name]: val }
    const data = { }
    for (const key of Object.keys(values)) {
      data[key] = handleformatDate(values[key])
    }
    runFunction(onValuesChange,_val, {...data,..._val})
  }
  return {
    onChange,
  }
}

function handleformatDate(val) {
  if(!isArray(val)) return val
  if(val.some(i=> i._isAMomentObject)) {
    val = val.map(i=> moment(i).format(FORMAt))
  }
 return val
}

