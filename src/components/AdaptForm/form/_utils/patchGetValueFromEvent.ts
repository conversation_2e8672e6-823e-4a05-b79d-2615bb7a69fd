import { isFunction, isNumber } from 'lodash';

export const patchGetValueFromEvent = props => {
  const { value, getValueFromEvent, comp } = props;
  const e = {
    target: {
      value:
        ['Input', 'Select', 'InputNumber'].includes(comp.type.name) && isNumber(value)
          ? `${value}`
          : value || '',
      checked: ['Checkbox', 'Radio', 'Switch'].includes(comp.type.name) && value,
    },
  };
  const val = isFunction(getValueFromEvent) ? getValueFromEvent(e) : value;
  return val;
};
