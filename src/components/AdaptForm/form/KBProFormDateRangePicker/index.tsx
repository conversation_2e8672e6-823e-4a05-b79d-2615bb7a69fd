/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { DatePicker } from 'antd';
import React, { FC } from 'react';
import { IKBProFormDateRangePicker } from '../types';
import { isArray } from 'lodash';
import moment from 'moment';
import KBProFormWrapper from '../KBProFormWrapper';

const KBProFormDateRangePicker: FC<IKBProFormDateRangePicker> = props => {
  const { initialValue: propsInitialValue, ...rest } = props;

  const initialValue = isArray(propsInitialValue) ? [moment(propsInitialValue[0]), moment(propsInitialValue[1])] : propsInitialValue
  return (
    <>
      <KBProFormWrapper  {...rest} initialValue={initialValue} comp={<DatePicker.RangePicker />} />
    </>
  );
};

export default KBProFormDateRangePicker;
