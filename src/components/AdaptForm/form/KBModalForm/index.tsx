/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Modal } from 'antd';
import React, { FC, useContext, useImperativeHandle, useRef } from 'react';
import KBProForm from '../KBProForm';
import { IProForm, KBModalFormProps, KBModalProps, KBProFormInstance } from '../types';
import { useHandle } from './useHandle';
import classNames from 'classnames';
import { KBModalFormWrapper } from './styles';
import { KBProFormWrapperContext } from '../AdaptWrapper';
export type IKBModalFormProps = {
  modalProps?: KBModalProps;
  form?: KBProFormInstance;
  footer?: React.ReactNode;
  mountBody?:boolean
} & KBModalFormProps & IProForm;
let index = 1
const KBModalForm: FC<IKBModalFormProps> = props => {
  const { title, modalProps = {}, submitter, footer, width, className, formRef, mountBody = true,  ...rest } = props;
  const { loading, visible, onCancel, onOk, triggerDom } = useHandle(props);
  // 这里简单处理一下
  // pro中是将Form的footer渲染到modal的footer上
  const { submitButtonProps, searchConfig, resetButtonProps } = submitter || {}
  const { submitText, resetText } = searchConfig || {}
   const _form = useContext(KBProFormWrapperContext) as any
   useImperativeHandle(formRef,()=> _form)
   index++
   const id = 'kb-pro-modal' + index
  return (<>
    <KBModalFormWrapper>
      <div id={id}></div>
      <Modal
        confirmLoading={loading}
        width={width}
        title={title}
        visible={visible}
        onCancel={onCancel}
        onOk={onOk}
        okText={submitText}
        cancelText={resetText}
        okButtonProps={submitButtonProps}
        cancelButtonProps={resetButtonProps}
        footer={footer}
        maskClosable={false}
        destroyOnClose
        getContainer={()=> mountBody  ? document.documentElement :  document.getElementById(id)}
        {...modalProps}
        className={classNames('kb-pro-modal', className)}
      >
        <KBProForm {...rest}>{props.children}</KBProForm>
      </Modal>
      {triggerDom}
    </KBModalFormWrapper>
  </>

  );
};

export default KBModalForm;
