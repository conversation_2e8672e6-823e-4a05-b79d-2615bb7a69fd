/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { cloneElement, useMemo, useState } from "react"
import { IKBModalFormProps } from "."
import KBProForm from "../KBProForm"
import { runFunction } from "@/utils/runFunction"
import useMergedState from 'rc-util/lib/hooks/useMergedState';
export const useHandle = (props: IKBModalFormProps) => {
  const { trigger, onFinish, modalProps = {}, form: propForm, open,onOpenChange,onVisibleChange,visible:propVisible } = props
  const [loading, setLoading] = useState(false)
  const [form] = KBProForm.useForm(propForm)
  const [visible, setVisible] = useMergedState<boolean>(!!propVisible, {
    value: open || propVisible,
    onChange: onOpenChange || onVisibleChange,
  });
  const onOk = async () => {
    const values = await form?.validateFields?.()
    try {
      setLoading(true)
      const res = await onFinish?.(values)
      if (res) {
        setVisible(false)
      }

    } finally {
      setLoading(false)
    }

  }

  const onCancel = () => {
    setVisible(false)
    runFunction(modalProps.onCancel)

  }
  const triggerDom = useMemo(() => {
    if (!trigger) {
      return null;
    }
    return cloneElement(trigger, {
      key: 'trigger',
      ...trigger.props,
      onClick: async (e: React.MouseEvent<Element, MouseEvent>) => {
        setVisible(!visible);
        trigger.props?.onClick?.(e);
      },
    });
  }, [setVisible, trigger, visible]);

  return {
    loading,
    visible,
    triggerDom,
    onOk,
    onCancel,
  }
}
