/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { Select } from 'antd';
import React,{ FC } from 'react';
import { useOptRequest } from '../_utils/useOptRequest';
import { IProFormSelect } from '../types';
import KBProFormWrapper from '../KBProFormWrapper';

const SelectOption = Select.Option
const KBProFormSelect:FC<IProFormSelect> = props => {
  const {options} = useOptRequest(props)

  const Options = options?.map((item:any) => {
    if (item) {
      return (<SelectOption
        key={item.value}
        value={item.value}
      >{item.label}</SelectOption>);
    }
  });
  return (
    <>
    <KBProFormWrapper placeholder='请选择' {...{...props,options}} comp={<Select>{Options}</Select>} />
    </>
  );
};

export default KBProFormSelect;
