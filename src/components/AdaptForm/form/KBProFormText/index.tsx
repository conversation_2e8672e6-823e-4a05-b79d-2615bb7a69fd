/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import {  Input } from 'antd';
import React, { FC } from 'react';
import { IKBProFormText } from '../types';
import KBProFormWrapper from '../KBProFormWrapper';
import { useOptRequest } from '../_utils/useOptRequest';


const KBProFormText: FC<IKBProFormText> = props => {
  // 在table columns 使用 request valueEnums
 const {options} = useOptRequest(props as any)
  return (
    <>
      <KBProFormWrapper placeholder='请输入' {...{...props,options}} comp={<Input />} />
    </>
  );
};

export default KBProFormText;
