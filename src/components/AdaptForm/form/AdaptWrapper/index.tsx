/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */


import { nextTick } from '@/utils/nextTick';
import { Form } from 'antd';
import React, { createContext } from 'react';
import { NAME } from '../KBProFormWrapper';
export const KBProFormWrapperContext = createContext({}) as any
const _AdaptWrapper = props => {
  const setFieldsValue =(...params)=> {
    nextTick(()=> {
      props.form.setFieldsValue(...params)
    })
  }
  const setFieldValue = (name,val)=>{
    setFieldsValue({name,val})
  }
  const getFieldsValue = (...nameList) => {
    const valuses = props.form.getFieldsValue(...nameList)
    delete valuses[NAME]
    return valuses
  }
  const validateFields = async()=> {
    const valuses = await props.form.validateFields()
    delete valuses[NAME]
    return valuses
  }
  return <KBProFormWrapperContext.Provider value={{...props.form,setFieldsValue,setFieldValue,getFieldsValue,validateFields}}>{props.children}</KBProFormWrapperContext.Provider>;
};
const AdaptWrapper = Form.create()(_AdaptWrapper) as any;

const AdaptWrapperFn = <T extends Record<string,any>>(Comp)=> (props:T) => {
  return  <AdaptWrapper>
  <Comp {...props}/>
</AdaptWrapper>
}

export default AdaptWrapperFn

