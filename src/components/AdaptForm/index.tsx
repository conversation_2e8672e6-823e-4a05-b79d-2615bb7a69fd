/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
/**
 * @description 兼容 ProForm 部分常用属性 具体可参考 procomponents完善相应属性
 *   相关属性尽量和procomponents保持一致 方便后续升级
 *   用法示例  kdgFee
 */
import AdaptFormWrapperFn from './form/AdaptWrapper/index';
import ProForm from './form/KBProForm/index';
import ModalForm from './form/KBModalForm/index';
import ProFormText from './form/KBProFormText/index';
import ProFormSelect from './form/KBProFormSelect/index';
import ProFormCheckBox from './form/KBProFormCheckBox/index';
import ProFormRadio from './form/KBProFormRadio';
import ProFormSwitch from './form/KBProFormSwitch';
import ProFormDigit from './form/KBProFormDigit';
import ProFormDateRangePicker from './form/KBProFormDateRangePicker';
import { KBProFormInstance } from './form/types';
type ProFormInstance = KBProFormInstance
export {
  ModalForm,
  ProFormText,
  ProFormRadio,
  ProFormDigit,
  ProFormSelect,
  ProFormSwitch,
  ProFormCheckBox,
  ProFormDateRangePicker,
  AdaptFormWrapperFn,
  ProFormInstance
}
export default ProForm
