
import { useEffect, useRef } from 'react';
import { unstable_batchedUpdates } from 'react-dom';
import  {
  PageInfo,
  RequestData,
  UseFetchDataAction,
  UseFetchProps,
} from './types';
import { postDataPipeline } from '../_utils';
import useMergedState from 'rc-util/lib/hooks/useMergedState';
import { usePrevious } from 'ahooks';
import { useRefFunction } from '@/utils/hooks/useRefFunction';
import { runFunction } from '@/utils/runFunction';
import { useDebounceFn } from '@/utils/hooks/useDebounceFn';

const mergeOptionAndPageInfo = ({ pageInfo }: UseFetchProps) => {
  if (pageInfo) {
    const { current, defaultCurrent, pageSize, defaultPageSize } = pageInfo;
    return {
      current: current || defaultCurrent || 1,
      total: 0,
      pageSize: pageSize || defaultPageSize || 20,
    };
  }
  return { current: 1, total: 0, pageSize: 20 };
};


export const useFetchData = <DataSource extends RequestData<any>>(
  getData:
    | undefined
    | ((params?: { pageSize: number; current: number }) => Promise<DataSource>),
  defaultData: any[] | undefined,
  options: UseFetchProps,
): UseFetchDataAction => {

  const umountRef = useRef<boolean>(false);

  const abortRef = useRef<AbortController | null>(null);

  const {
    onLoad,
    manual,
    polling,
    onRequestError,
    debounceTime = 20,
    effects = [],
  } = options || {};

  const manualRequestRef = useRef<boolean>(manual);

  const pollingSetTimeRef = useRef<any>();


  const [tableDataList, setTableDataList] = useMergedState<
    DataSource[] | undefined
  >(defaultData, {
    value: options?.dataSource,
    onChange: options?.onDataSourceChange,
  });


  const [tableLoading, setTableLoading] = useMergedState<boolean>(false, {
    value:
      typeof options?.loading === 'object'
        ? options?.loading?.spinning
        : options?.loading,
    onChange: options?.onLoadingChange,
  });


  const [pageInfo, setPageInfoState] = useMergedState<PageInfo>(
    () => mergeOptionAndPageInfo(options),
    {
      onChange: options?.onPageInfoChange,
    },
  );


  const setPageInfo = useRefFunction((changePageInfo: PageInfo) => {
    if (
      changePageInfo.current !== pageInfo.current ||
      changePageInfo.pageSize !== pageInfo.pageSize ||
      changePageInfo.total !== pageInfo.total
    ) {
      setPageInfoState(changePageInfo);
    }
  });

  const [pollingLoading, setPollingLoading] = useMergedState(false);

  const setDataAndLoading = (newData: DataSource[], dataTotal: number) => {
    unstable_batchedUpdates(() => {
      setTableDataList(newData);
      if (pageInfo?.total !== dataTotal) {
        setPageInfo({
          ...pageInfo,
          total: dataTotal || newData.length,
        });
      }
    });
  };


  const prePage = usePrevious(pageInfo?.current);


  const prePageSize = usePrevious(pageInfo?.pageSize);


  const prePolling = usePrevious(polling);

  const requestFinally = useRefFunction(() => {
    unstable_batchedUpdates(() => {
      setTableLoading(false);
      setPollingLoading(false);
    });
  });
  /** 请求数据 */
  const fetchList = async (isPolling: boolean) => {
    if (manualRequestRef.current) {
      manualRequestRef.current = false;
      return;
    }
    if (!isPolling) {
      setTableLoading(true);
    } else {
      setPollingLoading(true);
    }

    const { pageSize, current } = pageInfo || {};
    try {
      const pageParams =
        options?.pageInfo !== false
          ? {
              current,
              pageSize,
            }
          : undefined;
      const {
        data = [],
        success,
        total = 0,
        ...rest
      } = (await getData?.(pageParams)) || {};
      // 如果失败了，直接返回，不走剩下的逻辑了
      if (success === false) return [];

      const responseData = postDataPipeline<DataSource[]>(
        data!,
        [options.postData].filter((item) => item) as any,
      );
      // 设置表格数据
      setDataAndLoading(responseData, total);
      onLoad?.(responseData, rest);
      return responseData;
    } catch (e) {
      // 如果没有传递这个方法的话，需要把错误抛出去，以免吞掉错误
      if (onRequestError === undefined) throw new Error(e as string);
      if (tableDataList === undefined) setTableDataList([]);
      onRequestError(e as Error);
    } finally {
      requestFinally();
    }

    return [];
  };


  const fetchListDebounce = useDebounceFn(async (isPolling: boolean) => {
    if (pollingSetTimeRef.current) {
      clearTimeout(pollingSetTimeRef.current);
    }
    if (!getData) {
      return;
    }

    const abort = new AbortController();
    abortRef.current = abort;
    try {

      const msg = (await Promise.race([
        fetchList(isPolling),
        new Promise((_, reject) => {
          abortRef.current?.signal?.addEventListener?.('abort', () => {
            reject('aborted');
            // 结束请求，并且清空loading控制
            fetchListDebounce.cancel();
            requestFinally();
          });
        }),
      ])) as DataSource[];

      if (abort.signal.aborted) return;
      const needPolling = runFunction(polling, msg);


      if (needPolling && !umountRef.current) {
        pollingSetTimeRef.current = setTimeout(() => {
          fetchListDebounce.run(needPolling);
          // 这里判断最小要2000ms，不然一直loading
        }, Math.max(needPolling, 2000));
      }

      return msg;
    } catch (error) {
      if (error === 'aborted') {
        return;
      }
      throw error;
    }
  }, debounceTime || 30);


  const abortFetch = () => {
    abortRef.current?.abort();
    fetchListDebounce.cancel();
    requestFinally();
  };

  useEffect(() => {
    if (!polling) {
      clearTimeout(pollingSetTimeRef.current);
    }
    if (!prePolling && polling) {
      fetchListDebounce.run(true);
    }
    return () => {
      clearTimeout(pollingSetTimeRef.current);
    };
  }, [polling]);

  useEffect(() => {
    umountRef.current = false;

    return () => {
      umountRef.current = true;
    };
  }, []);

  useEffect(() => {
    const { current, pageSize } = pageInfo || {};

    if (
      (!prePage || prePage === current) &&
      (!prePageSize || prePageSize === pageSize)
    ) {
      return;
    }

    if (
      (options.pageInfo && tableDataList && tableDataList?.length > pageSize) ||
      0
    ) {
      return;
    }


    if (
      current !== undefined &&
      tableDataList &&
      tableDataList.length <= pageSize
    ) {
      abortFetch();
      fetchListDebounce.run(false);
    }
  }, [pageInfo?.current]);

  useEffect(() => {
    if (!prePageSize) {
      return;
    }
    abortFetch();
    fetchListDebounce.run(false);
  }, [pageInfo?.pageSize]);


  useEffect(() => {
    abortFetch();
    fetchListDebounce.run(false);
    if (!manual) {

      manualRequestRef.current = false;
    }
    return () => {
      abortFetch();
    };
  }, [...effects, manual]);

  const reset = ()=> {
    const { pageInfo: optionPageInfo } = options || {};
      const { defaultCurrent = 1, defaultPageSize = 20 } = optionPageInfo || {};
      const initialPageInfo = {
        current: defaultCurrent,
        total: 0,
        pageSize: defaultPageSize,
      };
      setPageInfo(initialPageInfo);
  }
  return {
    /**
     * 表格的数据列表。
     * @type {DataSource[]}
     */
    dataSource: tableDataList! as DataSource[],

    setDataSource: setTableDataList,

    loading:
      typeof options?.loading === 'object'
        ? { ...options?.loading, spinning: tableLoading }
        : tableLoading,

    reload: async (bool) => {
      if(bool) {
        reset()
      }
      abortFetch();
      return fetchListDebounce.run(false);
    },

    pageInfo,

    pollingLoading,


    reset: async () => {
      reset()
    },

    setPageInfo: async (info) => {
      setPageInfo({
        ...pageInfo,
        ...info,
      });
    },
  };
};

export default useFetchData;
