/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { cloneElement } from 'react';
import { ColumnProps, SearchConfig, UseFetchDataAction } from '../types';
import { Col, Row } from 'antd';
import ProForm, { AdaptFormWrapperFn } from '@/components/AdaptForm';
import { KBProFormInstance } from '@/components/AdaptForm/form/types';
import { SearchFormWrapper } from './styles';
import { componentsMap } from './_utils';
import { useHandle } from './useHandle';
import { ITableProps } from '..';
import { isObject } from 'lodash';
import { useSubmitDom } from './useSubmitDom';
import { useOnValuesChange } from '@/components/AdaptForm/form/_utils/useOnValuesChange';

export interface ISearchForm<T = any> {
  columns: ColumnProps<T>[];
  search?: boolean | SearchConfig;
  action?: UseFetchDataAction;
  formRef?: React.Ref<KBProFormInstance | undefined>;
  formValuesRef?: React.Ref<any>;
  customProps?: ITableProps['customProps'];
  formProps?:ITableProps['form']
  formItemProps?:ColumnProps<T>['formItemProps']
}

const SearchForm = <T extends any>(props: ISearchForm<T>) => {
  const { action, formRef, customProps = {}, search, formProps = {} } = props;
  const searchConfig = isObject(search) ? search as SearchConfig : ({} as SearchConfig);

  const { controlWrapperWidth = '70%',searchSpan } = customProps;

  const { form, searchOpt } = useHandle<T>(props);

  const { submitDom } = useSubmitDom({ ...props, form });

  const genFormItem = (item: typeof searchOpt[number]) => {
    const Comp = componentsMap.get(item.valueType);
    if (item.renderFormItem) {
    const name = item.name
    const { onChange } = useOnValuesChange({name,onValuesChange:formProps.onValuesChange})
      return (
        <ProForm.Item {...item} {...item.fieldProps} {...item.formItemProps}>
          {form.getFieldDecorator(name, { ...item, ...item.fieldProps })(
            cloneElement(item.renderFormItem(item, null, form, action) as any,{
              onChange
            }) ,
          )}
        </ProForm.Item>
      );
    }
    if(!Comp) return null
    return <Comp {...item} {...item.fieldProps} {...item.formItemProps} />;
  };
  const handleColProps = (item?:any) =>{
    const _span = item?.fieldProps?.span || searchConfig.span || 6
    const s =  {
      xs:24,
      sm:24,
      md:12,
      lg:12,
      xl:_span,
      xxl:_span,
    }

    if(isObject(_span)) {
      return {..._span}
    }
    return  s
  }
  return (
    <SearchFormWrapper width={controlWrapperWidth}>
      <div className="form-container">
        <ProForm layout="horizontal" {...formProps} form={form} formRef={formRef} >
          <Row gutter={searchConfig.searchGutter}>
            {searchOpt.map(item => {
              const colProps = handleColProps(item)
              return (
                <Col  {...colProps} key={item.name}>
                  {genFormItem(item)}
                </Col>
              );
            })}
            {
              formProps.submitter !== false  &&
              <Col {...(searchSpan ? { xs:2* searchSpan,sm:2* searchSpan,md:2* searchSpan,lg:2* searchSpan,xl:searchSpan,xxl:searchSpan}:handleColProps())} >
              <div className="search-btn">{submitDom}</div>
            </Col>
            }

          </Row>
        </ProForm>
      </div>
    </SearchFormWrapper>
  );
};

export default AdaptFormWrapperFn(SearchForm) as <T extends any>(
  props: ISearchForm<T>,
) => JSX.Element;
