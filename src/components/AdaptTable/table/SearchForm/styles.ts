/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import styled from 'styled-components';
import { ISearchForm } from '.';
interface IProps{
  width:  ISearchForm['customProps']['controlWrapperWidth']
}
export const SearchFormWrapper= styled.div<IProps>`
  .form-container {
    padding:20px 0px 0px 0px;
    background-color:#fff;
    border-radius:4px;
  }
  .ant-form-item-control-wrapper{
    width:${props => props.width};
  }
  .ant-select {
    width:100%;
  }
  .ant-calendar-picker {
    width:100%;
  }
  .search-btn{
    display:flex;
    gap:14px;
    margin-top:4px;
  }

`;

