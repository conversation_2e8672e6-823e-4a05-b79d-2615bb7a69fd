/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { useImperativeHandle, useMemo, useRef } from "react";
import { SearchTransformKeyFn } from "../types";
import ProForm from "@/components/AdaptForm";
import { ISearchForm } from ".";
import { isArray, isObject } from "lodash";
import { runFunction } from "@/utils/runFunction";
import moment from "moment";
const FORMAt = 'YYYY-MM-DD'
export const useHandle = <T extends any >(props:ISearchForm<T>)=> {
  const { columns, search, action,formValuesRef } = props;

  const [form] = ProForm.useForm();
   /** 保存 transformKeyRef，用于对表单key transform */
   const transformKeyRef = useRef<Record<string, any | undefined>>({});

  // 给request 使用
  useImperativeHandle(formValuesRef, () => transformKeyRef.current);

  transformKeyRef.current = {...form.getFieldsValue()}
  const searchOpt = useMemo(()=>{
   return columns.filter(i => i.search !== false || i.hideInForm == false).map(item => {
      const name = item.dataIndex || 'name';
      const value =  form.getFieldValue(name)
      if(isObject(item.search) && item.search && item.search.transform) {
      delete transformKeyRef.current[name]
      let _value = value
      if(isArray(value) && value.some(i=> i._isAMomentObject)) {
        _value = value.map(i=> moment(i).format(FORMAt))
      }
      const valuses = runFunction(item.search.transform, _value, name, form.getFieldsValue())

      transformKeyRef.current = {...transformKeyRef.current,...valuses}

      }

      return {
        name,
        label: item.title,
        valueType: item.valueType || 'text',
        valueEnum: item.valueEnum,
        fieldProps: item.fieldProps || {},
        initialValue:item.initialValue,
        renderFormItem: item?.renderFormItem,
        request: item?.request,
        formItemProps:item?.formItemProps || {}
      };
    })
  },[columns,form]);

  return {
    form,
    searchOpt,

  }
}
