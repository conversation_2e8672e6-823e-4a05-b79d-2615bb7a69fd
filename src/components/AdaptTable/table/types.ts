/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */
import { ICom, IOptionsType, KBProFormInstance } from '@/components/AdaptForm/form/types';
import { ColProps } from 'antd/lib/col';
import { SpinProps } from 'antd/lib/spin';
import * as React from 'react';

export interface IKBTableProps<T> {
  prefixCls?: string;
  dropdownPrefixCls?: string;
  rowSelection?: TableRowSelection<T> | false;
  pagination?: PaginationProps | false;
  size?: TableSize;
  dataSource?: T[];
  components?: TableComponents;
  columns?: ColumnProps<T>[];
  rowKey?: string | ((record: T, index: number) => string);
  rowClassName?: (record: T, index: number) => string;
  expandedRowRender?: (record: T, index: number, indent: number, expanded: boolean) => React.ReactNode;
  defaultExpandAllRows?: boolean;
  defaultExpandedRowKeys?: string[] | number[];
  expandedRowKeys?: string[] | number[];
  expandIcon?: (props: ExpandIconProps<T>) => React.ReactNode;
  expandIconAsCell?: boolean;
  expandIconColumnIndex?: number;
  expandRowByClick?: boolean;
  onExpandedRowsChange?: (expandedRowKeys: string[] | number[]) => void;
  onExpand?: (expanded: boolean, record: T) => void;
  onChange?: (pagination: PaginationProps, filters: Partial<Record<keyof T, string[]>>, sorter: SorterResult<T>, extra: TableCurrentDataSource<T>) => void;
  loading?: boolean | SpinProps;
  locale?: TableLocale;
  indentSize?: number;
  onRowClick?: (record: T, index: number, event: Event) => void;
  onRow?: (record: T, index: number) => TableEventListeners;
  onHeaderRow?: (columns: ColumnProps<T>[]) => TableEventListeners;
  useFixedHeader?: boolean;
  bordered?: boolean;
  showHeader?: boolean;
  footer?: (currentPageData: T[]) => React.ReactNode;
  title?: (currentPageData: T[]) => React.ReactNode;
  scroll?: {
      x?: boolean | number | string;
      y?: boolean | number | string;
      scrollToFirstRowOnChange?: boolean;
  };
  childrenColumnName?: string;
  bodyStyle?: React.CSSProperties;
  className?: string;
  style?: React.CSSProperties;
  tableLayout?: React.CSSProperties['tableLayout'];
  children?: React.ReactNode;
  sortDirections?: SortOrder[];
  getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
}

export interface ColumnProps<T> {
  title?: React.ReactNode | ((options: {
      filters: TableStateFilters;
      sortOrder?: SortOrder;
      sortColumn?: ColumnProps<T> | null;
  }) => React.ReactNode);
  key?: React.Key;
  dataIndex?: string;
  render?: (text: any, record: T, index: number,action:ActionType) => React.ReactNode;
  align?: 'left' | 'right' | 'center';
  ellipsis?: boolean;
  filters?: ColumnFilterItem[];
  onFilter?: (value: any, record: T) => boolean;
  filterMultiple?: boolean;
  filterDropdown?: React.ReactNode | ((props: FilterDropdownProps) => React.ReactNode);
  filterDropdownVisible?: boolean;
  onFilterDropdownVisibleChange?: (visible: boolean) => void;
  sorter?: boolean | CompareFn<T>;
  defaultSortOrder?: SortOrder;
  colSpan?: number;
  width?: string | number;
  className?: string;
  fixed?: boolean | typeof ColumnFixedPlacements[number];
  filterIcon?: React.ReactNode | ((filtered: boolean) => React.ReactNode);
  filteredValue?: any[] | null;
  filtered?: boolean;
  defaultFilteredValue?: any[];
  sortOrder?: SortOrder | boolean;
  children?: ColumnProps<T>[];
  onCellClick?: (record: T, event: Event) => void;
  onCell?: (record: T, rowIndex: number) => TableEventListeners;
  onHeaderCell?: (props: ColumnProps<T>) => TableEventListeners;
  sortDirections?: SortOrder[];
  valueType?: 'text' | 'select' | 'dateRange';
  valueEnum?: Record<string,any>,
  initialValue?: any;
  /** @name 是否缩略 */
  /** @name 是否拷贝 */
  copyable?: boolean;
  /** @deprecated Use `search=false` instead 在查询表单中隐藏 */
  hideInSearch?: boolean;
  /** 在查询表单中隐藏 */
  search?: false | {
      /**
       * Transform: (value: any) => ({ startTime: value[0], endTime: value[1] }),
       *
       * @name 转化值的key, 一般用于事件区间的转化
       */
      transform:SearchTransformKeyFn;
  };
  /** @name 在 table 中隐藏 */
  hideInTable?: boolean;
  formItemProps?:{

  };
  /** @name 在新建表单中删除 */
  hideInForm?: boolean;
  /** @name 不在配置工具中显示 */
  hideInSetting?: boolean;
  fieldProps?:{
    options?: any[];
    span?: number | ColProps;

  } & ICom
  renderFormItem?: (schema: ProSchema<Entity, ExtraProps, ComponentsType, ValueType, ExtraFormItemProps> & {
    isEditable?: boolean;
    index?: number;
    type: ComponentsType;
    originProps?: any;
}, config: {
    onSelect?: (value: any) => void;
    onChange?: <T = any>(value: T) => void;
    value?: any;
    type: ComponentsType;
    recordKey?: React.Key | React.Key[];
    record?: Entity;
    isEditable?: boolean;
    defaultRender: (newItem: ProSchema<Entity, ExtraProps, ComponentsType, ValueType>) => JSX.Element | null;
}, form: KBProFormInstance, action?: Omit<UseEditableUtilType, 'newLineRecord' | 'editableKeys' | 'actionRender' | 'setEditableRowKeys'>) => React.ReactNode;
request?:(params?:any,props?:any) => Promise<IOptionsType>
}
export type SearchTransformKeyFn =  (value: any, namePath: string, allValues: any) => Record<string,any>;
export declare type CompareFn<T> = (a: T, b: T, sortOrder?: SortOrder) => number;
export declare type ColumnFilterItem = {
    text: React.ReactNode;
    value: string;
    children?: ColumnFilterItem[];
};
export interface FilterDropdownProps {
    prefixCls?: string;
    setSelectedKeys?: (selectedKeys: string[]) => void;
    selectedKeys?: React.Key[];
    confirm?: () => void;
    clearFilters?: () => void;
    filters?: ColumnFilterItem[];
    getPopupContainer?: (triggerNode: HTMLElement) => HTMLElement;
    visible?: boolean;
}

export interface AdditionalCellProps {
    onClick?: React.MouseEventHandler<HTMLElement>;
    [name: string]: any;
}
export interface TableComponents {
    table?: React.ReactType;
    header?: {
        wrapper?: React.ReactType;
        row?: React.ReactType;
        cell?: React.ReactType;
    };
    body?: {
        wrapper?: React.ReactType;
        row?: React.ReactType;
        cell?: React.ReactType;
    };
}
export interface TableLocale {
    filterTitle?: string;
    filterConfirm?: React.ReactNode;
    filterReset?: React.ReactNode;
    emptyText?: React.ReactNode | (() => React.ReactNode);
    selectAll?: React.ReactNode;
    selectInvert?: React.ReactNode;
    sortTitle?: string;
    expand?: string;
    collapse?: string;
}
export declare type RowSelectionType = 'checkbox' | 'radio';
export declare type SelectionSelectFn<T> = (record: T, selected: boolean, selectedRows: Object[], nativeEvent: Event) => void;
export declare type TableSelectWay = 'onSelect' | 'onSelectMultiple' | 'onSelectAll' | 'onSelectInvert';
export interface TableRowSelection<T> {
    type?: RowSelectionType;
    selectedRowKeys?: string[] | number[];
    onChange?: (selectedRowKeys: string[] | number[], selectedRows: T[]) => void;
    getCheckboxProps?: (record: T) => Object;
    onSelect?: SelectionSelectFn<T>;
    onSelectMultiple?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    onSelectAll?: (selected: boolean, selectedRows: T[], changeRows: T[]) => void;
    onSelectInvert?: (selectedRowKeys: string[] | number[]) => void;
    selections?: SelectionItem[] | boolean;
    hideDefaultSelections?: boolean;
    fixed?: boolean;
    columnWidth?: string | number;
    selectWay?: TableSelectWay;
    columnTitle?: string | React.ReactNode;
}
export declare type SortOrder = 'descend' | 'ascend';
export interface SorterResult<T> {
    column: ColumnProps<T>;
    order: SortOrder;
    field: string;
    columnKey: string;
}
export declare type TableSize = 'default' | 'middle' | 'small';
export interface ExpandIconProps<T> {
    prefixCls: string;
    expanded: boolean;
    record: T;
    needIndentSpaced: boolean;
    expandable: boolean;
    onExpand: (record: T, event?: React.MouseEvent) => void;
}
export interface TableCurrentDataSource<T> {
    currentDataSource: T[];
}
export interface TableEventListeners {
    onClick?: (arg: React.MouseEvent) => void;
    onDoubleClick?: (arg: React.MouseEvent) => void;
    onContextMenu?: (arg: React.MouseEvent) => void;
    onMouseEnter?: (arg: React.MouseEvent) => void;
    onMouseLeave?: (arg: React.MouseEvent) => void;
    [name: string]: any;
}
export interface CheckboxPropsCache {
    [key: string]: any;
}
export interface WithStore {
    store: Store;
    checkboxPropsCache: CheckboxPropsCache;
    setCheckboxPropsCache: (cache: CheckboxPropsCache) => void;
}

export declare type InternalTableProps<T> = TableProps<T> & WithStore;
export interface TableStateFilters {
    [key: string]: string[];
}
export interface TableState<T> {
    pagination: PaginationConfig;
    filters: TableStateFilters;
    sortColumn: ColumnProps<T> | null;
    sortOrder?: SortOrder;
    pivot?: number;
    prevProps: TableProps<T>;
    components: TableComponents;
    columns: ColumnProps<T>[];
}
export declare type SelectionItemSelectFn = (key: string[]) => void;
declare type GetPopupContainer = (triggerNode?: HTMLElement) => HTMLElement;
export interface SelectionItem {
    key: string;
    text: React.ReactNode;
    onSelect?: SelectionItemSelectFn;
}
export interface SelectionCheckboxAllProps<T> {
    store: Store;
    locale: TableLocale;
    disabled: boolean;
    getCheckboxPropsByItem: (item: T, index: number) => {
        defaultChecked: boolean;
    };
    getRecordKey: (record: T, index?: number) => string;
    data: T[];
    prefixCls: string | undefined;
    onSelect: (key: string, index: number, selectFunc: any) => void;
    hideDefaultSelections?: boolean;
    selections?: SelectionItem[] | boolean;
    getPopupContainer?: GetPopupContainer;
}
export interface SelectionCheckboxAllState {
    checked?: boolean;
    indeterminate?: boolean;
}
export interface SelectionBoxProps {
    store: Store;
    type?: RowSelectionType;
    defaultSelection: string[];
    rowIndex: string;
    name?: string;
    disabled?: boolean;
    onChange: (e: RadioChangeEvent | CheckboxChangeEvent) => void;
}
export interface SelectionBoxState {
    checked?: boolean;
}
export interface SelectionInfo<T> {
    selectWay: TableSelectWay;
    record?: T;
    checked?: boolean;
    changeRowKeys?: React.Key[];
    nativeEvent?: Event;
}
export interface FilterMenuProps<T> {
    locale: TableLocale;
    selectedKeys: string[];
    column: ColumnProps<T>;
    confirmFilter: (column: ColumnProps<T>, selectedKeys: React.Key[]) => any;
    prefixCls: string;
    dropdownPrefixCls: string;
    getPopupContainer?: GetPopupContainer;
}
export interface FilterMenuState<T> {
    selectedKeys: React.Key[];
    valueKeys: {
        [name: string]: string;
    };
    keyPathOfSelectedItem: {
        [key: string]: React.Key[];
    };
    visible?: boolean;
    prevProps: FilterMenuProps<T>;
}
export declare type PrepareParamsArgumentsReturn<T> = [any, string[], Object, {
    currentDataSource: T[];
}];


export type PageInfo = {
  pageSize: number;
  total: number;
  current: number;
};

export type RequestData<T> = {
  data: T[] | undefined;
  success?: boolean;
  total?: number;
} & Record<string, any>;

export type ActionType = {} & UseFetchDataAction

export type UseFetchDataAction<T = any> = {
  dataSource: T[];
  setDataSource: (dataSource: T[]) => void;
  loading: boolean | SpinProps | undefined;
  pageInfo: PageInfo;
  reload: (bool?:boolean) => Promise<void>;
  fullScreen?: () => void;
  reset: () => void;
  pollingLoading: boolean;
  setPageInfo: (pageInfo: Partial<PageInfo>) => void;
};


export type UseFetchProps = {
  /**
   * 数据源
   * @type {any}
   */
  dataSource?: any;

  /**
   * 是否处于加载状态
   * @type {UseFetchDataAction['loading']}
   */
  loading?: UseFetchDataAction['loading'];

  /**
   * 加载状态改变时的回调函数
   * @type {(loading: UseFetchDataAction['loading']) => void}
   */
  onLoadingChange?: (loading: UseFetchDataAction['loading']) => void;

  /**
   * 数据加载完成后的回调函数
   * @type {(dataSource: any[], extra: any) => void}
   */
  onLoad?: (dataSource: any[], extra: any) => void;

  /**
   * 数据源变化时的回调函数
   * @type {(dataSource?: any) => void}
   */
  onDataSourceChange?: (dataSource?: any) => void;

  /**
   * 请求时附带的数据
   * @type {any}
   */
  postData?: (dataSource: any[]) => any[];

  /**
   * 分页信息
   * @type {{
   *   current?: number;
   *   pageSize?: number;
   *   defaultCurrent?: number;
   *   defaultPageSize?: number;
   * } | false}
   */
  pageInfo?:
    | {
        current?: number;
        pageSize?: number;
        defaultCurrent?: number;
        defaultPageSize?: number;
      }
    | false;

  /**
   * 分页信息变化时的回调函数
   * @type {(pageInfo: PageInfo) => void}
   */
  onPageInfoChange?: (pageInfo: PageInfo) => void;

  /**
   * 请求相关的副作用
   * @type {any[]}
   */
  effects?: any[];

  /**
   * 请求出错时的回调函数
   * @type {(e: Error) => void}
   */
  onRequestError?: (e: Error) => void;

  /**
   * 是否手动触发请求
   * @type {boolean}
   */
  manual?: boolean;

  /**
   * 请求防抖时间
   * @type {number}
   */
  debounceTime?: number;

  /**
   * 数据源轮询间隔时间或轮询触发条件
   * @type {number | ((dataSource: any[]) => number)}
   */
  polling?: number | ((dataSource: any[]) => number);

  /**
   * 是否在页面获得焦点时重新验证数据
   * @type {Boolean}
   */
  revalidateOnFocus?: boolean;
};


export interface TablePaginationConfig extends PaginationProps {

}

export interface PaginationProps   {
  total?: number;
  defaultCurrent?: number;
  disabled?: boolean;
  current?: number;
  defaultPageSize?: number;
  pageSize?: number;
  onChange?: (page: number, pageSize?: number) => void;
  hideOnSinglePage?: boolean;
  showSizeChanger?: boolean;
  pageSizeOptions?: string[];
  onShowSizeChange?: (current: number, size: number) => void;
  showQuickJumper?: boolean | {
      goButton?: React.ReactNode;
  };
  showTotal?: (total: number, range: [number, number]) => React.ReactNode;
  size?: string;
  simple?: boolean;
  style?: React.CSSProperties;
  locale?: Object;
  className?: string;
  prefixCls?: string;
  selectPrefixCls?: string;
  itemRender?: (page: number, type: 'page' | 'prev' | 'next' | 'jump-prev' | 'jump-next', originalElement: React.ReactElement<HTMLElement>) => React.ReactNode;
  role?: string;
  showLessItems?: boolean;
}



  export type SearchConfig = Omit<ActionsProps, 'submitter' | 'setCollapsed' | 'isForm'> & {
    className?: string;
    defaultCollapsed?: boolean;
    /**
     * @name layout 的布局设置
     * @type 'horizontal' | 'inline' | 'vertical';
     */
    layout?: FormProps['layout'];
    /**
     * @name 默认一行显示几个表单项
     */
    defaultColsNumber?: number;
    /**
     * @name 文字标签的宽度
     *
     * @example 文字标签宽 80 ，一般用于只有两个字
     * labelWidth={80}
     * @example 文字标签宽 140 ，一般用于有四个字
     * labelWidth={140}
     * @example 自动计算，会导致不整齐
     * labelWidth="auto"
     */
    labelWidth?: number | 'auto';
    /**
     * @name 每一行之前要不要有分割线
     * @description 只有在 `layout` 为 `vertical` 时生效
     */
    split?: boolean;
    /**
     * @name 配置列数，一般而言是 8 的倍数
     *
     * @example 配置一行4个
     * span={6}
     *
     * @example 配置一行3个
     * span={6}
     *
     * @example 根据屏幕宽度配置
     * span={xs: 24, sm: 12, md: 8, lg: 6, xl: 6, xxl: 6}
     * */
    span?: number | ColProps;
    /**
     * @name 查询按钮的文本
     *  */
    searchText?: string;
    /**
     * @name 重置按钮的文本
     */
    resetText?: string;
    /**
     * @name 查询表单栅格间隔
     *
     * @example searchGutter={24}
     * */
    searchGutter?: RowProps['gutter'];
    form?: FormProps['form'];
    /**
     * @param searchConfig 基础的配置
     * @param props 更加详细的配置 {
     *     type?: 'form' | 'list' | 'table' | 'cardList' | undefined;
     *     form: FormInstance;
     *     submit: () => void;
     *     collapse: boolean;
     *     setCollapse: (collapse: boolean) => void;
     *     showCollapseButton: boolean; }
     * @name 底部操作栏的 render
     *
     *
     * @example 增加一个清空按钮
     * optionRender={(searchConfig, props, dom) =>[ <a key="clear">清空</a>,...dom]}
     *
     * @example 增自定义提交
     *
     * optionRender={(searchConfig) => [<a key="submit" onClick={()=> searchConfig?.form?.submit()}>提交</a>]}
     */
    optionRender?: ((searchConfig: {form?: KBProFormInstance }, props: {form?: KBProFormInstance }, dom: React.ReactNode[]) => React.ReactNode[]) | false;
    /**
     * @name 忽略 Form.Item rule规则配置
     */
    ignoreRules?: boolean;
    /**
     * @name 是否显示 collapse 隐藏个数
     */
    showHiddenNum?: boolean;
    submitterColSpanProps?: Omit<ColProps, 'span'> & {
        span: number;
    };
};

