/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { Table } from 'antd';
import React, { Key, useCallback, useImperativeHandle, useMemo, useRef, useState } from 'react';
import { ActionType, IKBTableProps, RequestData, SearchConfig, TablePaginationConfig } from './types';
import { useFetchData } from './useFetchData';
import SearchForm from './SearchForm';
import { KBProFormInstance } from '@/components/AdaptForm/form/types';
import { TableWrapper } from './styles';
import { runFunction } from '@/utils/runFunction';
import { TableRowSelection } from 'antd/lib/table';
import useMountMergeState from 'rc-util/lib/hooks/useMergedState';
import { ProFormText } from '@/components/AdaptForm';
const emptyObj = {} as Record<string, any>;
const emptyColumns = [] as any[];
export type ITableProps<T = any> = {
  request?: (params: any) => Promise<{ data: T[]; total: number }>;
  params?: Record<string, any>;
  defaultData?: T[];
  onDataSourceChange?: ((dataSource: T[]) => void);
  onLoadingChange?: (bool: boolean) => void;
  manual?: boolean;
  actionRef?: React.Ref<ActionType | undefined>;
  formRef?: React.Ref<KBProFormInstance | undefined>;
  search?: boolean | SearchConfig;
  form?: {
    onValuesChange?: (changedValues?: any, values?: any) => void;
    submitter?: SubmitterProps<{
      form?: KBProFormInstance<any>;
    }> | false;
  }
  customProps?: {
    controlWrapperWidth?: string | number;
    searchSpan?: number;
  }
  headerTitle?: React.ReactNode
  toolBarRender?: (action: ActionType | undefined) => React.ReactNode[] | false
} & IKBTableProps<T>;
const stringify = JSON.stringify

const KBTable = <T extends any>(props: ITableProps<T>) => {
  const {
    request,
    pagination: propsPagination,
    params = emptyObj,
    defaultData,
    onLoadingChange,
    manual = false,
    columns: propsColumns = emptyColumns,
    actionRef,
    formRef,
    search,
    customProps,
    headerTitle,
    toolBarRender,
    form: formProps = {},
    rowSelection: propsRowSelection = false,
    ...rest
  } = props;

  const formValuesRef = useRef({})

    /** 单选多选的相关逻辑 */
    const [selectedRowKeys, setSelectedRowKeys] = useMountMergeState<
    (string | number)[] | Key[] | undefined
  >(
    propsRowSelection
      ? propsRowSelection?.defaultSelectedRowKeys || []
      : undefined,
    {
      value: propsRowSelection ? propsRowSelection.selectedRowKeys : undefined,
    },
  );

    /** 清空所有的选中项 */
    const onCleanSelected = useCallback(() => {
      if(propsRowSelection) {
        runFunction(propsRowSelection.onChange,[],[])
      }
      setSelectedRowKeys([]);
    }, [propsRowSelection, setSelectedRowKeys]);


  const fetchPagination =
    typeof propsPagination === 'object'
      ? (propsPagination as TablePaginationConfig)
      : { defaultCurrent: 1, defaultPageSize: 20, pageSize: 20, current: 1 }

  const fetchData = useMemo(() => {
    if (!request) return undefined;
    return async (pageParams?: Record<string, any>) => {
      const actionParams = {
        ...(pageParams || {}),
        ...formValuesRef.current,
        ...params,
      };
      delete (actionParams as any)._timestamp;
      const response = await request(
        actionParams,

      );
      onCleanSelected()
      return response as RequestData<T>;
    };
  }, [params, request, formValuesRef.current]);

  const action = useFetchData(fetchData, defaultData, {
    pageInfo: propsPagination === false ? false : fetchPagination,
    loading: props.loading,
    dataSource: props.dataSource,
    onDataSourceChange: props.onDataSourceChange,
    onLoadingChange,
    manual,
    effects: [
      stringify(params),

    ],
    onPageInfoChange: (pageInfo) => {
      if (!propsPagination || !fetchData) return;
      propsPagination?.onChange?.(pageInfo.current, pageInfo.pageSize);
      propsPagination?.onShowSizeChange?.(pageInfo.current, pageInfo.pageSize);
    },
  });

  const { dataSource, loading, pageInfo } = action
  /**
    * 暴露action给父组建使用
  */
  useImperativeHandle(actionRef, () => action, [action])


  /**
   * columns 给render扩展 action
   * TODO: 这里action会导致多次生成columns和useFetchData 返回值有关
   *      procomponents 是通过context和ref并重新包装action的属性
   */
  const columns = useMemo(() => {
    return propsColumns.map((item) => {
      if (item.hideInTable) return void 0
      const _item = { ...item }
      if (item.render) {
        return { ..._item, render: (text, record, index) => item.render(text, record, index, action) }
      }
      return {..._item,render:(text)=> <ProFormText _tableColumns {..._item} tableColumnsValue={text} readOnly/>}
    }).filter(Boolean)
  }, [propsColumns, action])
  /**
    * pagination
  */
  const setPageInfo = (current: number, pageSize: number) => {
    action.setPageInfo({ pageSize, current })
  }
  const pagination = useMemo(() => {
    return propsPagination == false ? false :
      {
        ...pageInfo,
        onChange: setPageInfo,
        onShowSizeChange: setPageInfo,
        showSizeChanger: true,
        showTotal: (total, range) => `第 ${range[0]}-${range[1]}条/总共${total}条 `,
        showQuickJumper: false,
        ...propsPagination
      }
  }, [propsPagination, pageInfo])


   /** 行选择相关的问题 */
   const rowSelection: TableRowSelection<any> = {
    selectedRowKeys,
    ...propsRowSelection,
    onChange: (keys, rows) => {
      if (propsRowSelection && propsRowSelection.onChange) {
        propsRowSelection.onChange(keys, rows);
      }
      setSelectedRowKeys(keys);
    },
  } as any
  return (
    <TableWrapper>
      {
        search !== false && <SearchForm<T>
          columns={propsColumns}
          search={search}
          action={action}
          formRef={formRef}
          formValuesRef={formValuesRef}
          customProps={customProps}
          formProps={formProps}
        />
      }
      <Table
        title={() => [
          <div className='titleWrapper' key='title'>
            <div className='left'>{headerTitle}</div>
            <div className='rigth'>{runFunction(toolBarRender,action)}</div>
          </div>]}
        rowKey='id'
        pagination={pagination as any}
        dataSource={dataSource}
        loading={loading}
        columns={columns as any}
        {...rest}
        rowSelection={propsRowSelection !== false ? rowSelection : undefined}
        style={{ background: '#fff', ...rest?.style }}
      />

    </TableWrapper>
  );
};

export default KBTable;
