/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { isArray } from 'lodash';

export type TabItemType = 'province' | 'city' | 'district';
export interface TabItem {
  key: TabItemType;
  title: string;
}
export type AreaDataType = 'all' | 'province' | 'city';
export interface AreaDataItem {
  value: string;
  label: string;
  children?: AreaDataItem[];
}
interface AreaDataListItem extends AreaDataItem {
  children?: AreaDataItem[];
}

interface AreaItem {
  id: string;
  pid: number;
  name: string;
}

// 格式化区域列表
let formattedAddressList: AreaDataListItem[] | null = null;

export const defaultTabItemTitle = '请选择';
export const valueKeys: TabItemType[] = ['province', 'city', 'district'];

export function formatAreaList(area: AreaItem[][], type: AreaDataType = 'all'): AreaDataListItem[] {
  if (formattedAddressList) {
    return formattedAddressList;
  }
  if (!isArray(area)) return [];

  const list: AreaDataListItem[] = [];
  const areaLen = area.length;
  const pIdIndex = {};
  const cIdIndex = {};
  const isProvince = type === 'province';
  const isCity = type === 'city';
  for (let p = 0; p < areaLen; p++) {
    const arr = (area[p] || []).filter((item: any) => !!item);
    const arrLen = arr.length;
    if (arrLen > 0) {
      for (let i = 0; i < arrLen; i++) {
        const { id, pid, name } = arr[i] || {};
        if (!name) continue;
        const data: AreaDataItem = {
          value: name,
          label: name,
        };
        if (p === 1 && 1 * pid === 0) {
          list.push(data);
          pIdIndex[id] = list.length - 1;
        } else if (p === 2) {
          // 市
          if (isProvince) {
            continue;
          }
          const parent: any = list[pIdIndex[pid]];
          parent.children = parent.children || [];
          parent.children.push(data);
          cIdIndex[id] = parent.children.length - 1;
        } else {
          // 区
          if (isProvince || isCity) {
            continue;
          }
          const currentCity = area[2][pid];
          if (!currentCity) continue;
          const cIndex = cIdIndex[pid];
          const pIndex = pIdIndex[currentCity.pid];
          const parent = list[pIndex].children![cIndex];
          parent.children = parent.children || [];
          parent.children.push(data);
        }
      }
    }
  }
  formattedAddressList = list;
  return formattedAddressList;
}

/**
 *
 * @description 根据父级格式化列表
 * @param list
 * @param parent
 * @returns
 */
export function findListByParent(list: AreaDataItem[], parent: string): AreaDataItem[] {
  const cur = list.find((item) => item.value === parent);
  if (cur) {
    return cur.children || [];
  }
  return [];
}

/**
 *
 * @description 设置前格式化
 * @param list
 * @returns
 */
export function formatListBeforeSet(list: AreaDataItem[]): AreaDataItem[] {
  return list.map(({ children, ...item }) => item);
}

/**
 *
 * @description 创建tab列表
 * @param tabItems
 * @param key
 * @param value
 * @param isOver
 * @returns
 */
export function createTabList(
  tabItems: TabItem[],
  key: TabItemType,
  value: string,
  isOver: boolean,
): TabItem[] {
  const list: TabItem[] = [...tabItems];
  const index = list.findIndex((cur) => cur.key === key);
  list.splice(index, 1, {
    ...list[index],
    title: value,
  });
  list.splice(index + 1);
  if (!isOver) {
    // 结束状态不再添加新tab
    switch (key) {
      case 'province':
        list.push({ key: 'city', title: defaultTabItemTitle });
        break;
      case 'city':
        list.push({ key: 'district', title: defaultTabItemTitle });
        break;
      default:
        break;
    }
  }
  return list;
}
