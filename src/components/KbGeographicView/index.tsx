/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import React, { CSSProperties, useEffect, useState } from 'react';
import { Cascader } from 'antd';
import { formatAreaList } from './utils';

type GeographicViewType = 'all' | 'province' | 'city';
interface KbGeographicViewProps {
  size?: 'small' | 'middle' | 'large' | undefined;
  onChange: (v: string[]) => void;
  placeholder?: string;
  type?: GeographicViewType;
  value: string[];
  allowClear?: boolean;
  style?: CSSProperties;
}

interface AreaDataItem {
  value: string;
  label: string;
  children?: AreaDataItem[];
}
interface AreaDataListItem extends AreaDataItem {
  children?: AreaDataItem[];
}

interface AreaItem {
  id: string;
  pid: number;
  name: string;
}


const KbGeographicView: React.FC<KbGeographicViewProps> = props => {
  const { onChange, value, size, type, allowClear, style, placeholder } = props;
  const [options, setOptions] = useState<AreaDataListItem[]>([]);

  const hasValue = value && !!value.filter(item => !!item).length;

  // 格式化区域列表
  const triggerFormatAreaList = (area: AreaItem[][]) => setOptions(formatAreaList(area, type));

  useEffect(() => {
    triggerFormatAreaList(window.address);
  }, []);

  const handleChange = (v: any[]) => onChange(v);

  const displayRender = (label: string[]) => {
    let index = -1;
    switch (type) {
      case 'province':
        index = 0;
        break;
      case 'city':
        index = 1;
        break;
      default:
        break;
    }
    if (hasValue) {
      const values = value || label;
      return values[index] || values.join('/');
    }
    return '';
  };

  return (
    <Cascader
      style={{ width: '100%', ...style }}
      displayRender={displayRender}
      value={hasValue ? value : []}
      size={size}
      notFoundContent="未匹配到对应区域"
      showSearch
      expandTrigger="hover"
      options={options}
      onChange={handleChange}
      placeholder={placeholder}
      allowClear={allowClear}
    />
  );
};

KbGeographicView.defaultProps = {
  size: 'middle',
  onChange: () => {},
  placeholder: '选择省市区',
  type: 'all',
};

export default KbGeographicView;
