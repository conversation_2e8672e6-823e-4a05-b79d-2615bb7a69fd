/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { runFunction } from '@/utils/runFunction';
import { Button } from 'antd';
import { ButtonProps } from 'antd/lib/button';
import React, { FC, useState } from 'react';

type IProps = {
  onFinish?: () => Promise<boolean | undefined | void>;
} & ButtonProps;
const ButtonLoading: FC<IProps> = (props) => {
  const { onFinish, ...rest } = props;
  const [loading, setLoading] = useState(false);
  const handleOnClick = async (e: React.MouseEvent<HTMLElement, MouseEvent>) => {
    try {
      setLoading(true);
      await onFinish?.();

    } finally{
      setLoading(false);
      runFunction(rest.onClick,e)
    }
  };
  return (
    <>
      <Button loading={loading} {...rest} onClick={(e)=> handleOnClick(e)} >
        {props.children}
      </Button>
    </>
  );
};

export default ButtonLoading;
