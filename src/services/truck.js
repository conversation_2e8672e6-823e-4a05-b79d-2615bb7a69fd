/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@/utils/request';
import { operateBool, operateError, operateSuccess } from './_utils';

export const getTruckList = params => {
  return request('/Api/ac/AcCar/getPageData', {
    method: 'POST',
    body: params,
  }).then(operateError);
};

export const getTruckType = () => {
  return request('/Api/ac/AcChannel/getAllList', {
    method: 'POST',
  }).then(res => {
    const { data: { list = [] } = {} } = res;
    return [
      ...(Array.isArray(list) ? list : []).map(item => ({
        ...item,
        label: item.channel_name,
        value: item.channel,
      })),
    ];
  });
};

export const getAuthList = () => {
  return request('/Api/ac/AcAuth/getAllList', {
    method: 'POST',
  }).then(res => {
    const { data: { list = [] } = {} } = res;
    return [
      ...(Array.isArray(list) ? list : []).map(item => ({
        ...item,
        auth_id: item.id,
      })),
    ];
  });
};

export const addTruck = params => {
  return request('/Api/ac/AcCar/add', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const deleteTruck = params => {
  return request('/Api/ac/AcCar/remove', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const getTruckStationList = params => {
  return request('/Api/ac/AcStops/getPageData', {
    method: 'POST',
    body: params,
  }).then(operateError);
};

export const bindTruckStation = params => {
  return request('/Api/ac/AcStops/bind', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const unbindTruckStation = params => {
  return request('/Api/ac/AcStops/unbind', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const deleteTruckStation = params => {
  return request('/Api/ac/AcStops/unRelation', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const addTruckStation = params => {
  return request('/Api/ac/AcStops/relation', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const getAllStation = params => {
  return request('/Api/ac/AcStops/getAllList', {
    method: 'POST',
    body: params,
  }).then(res => res?.data || {});
};

export const addAuth = params => {
  return request('/Api/ac/AcAuth/add', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const editAuth = params => {
  return request('/Api/ac/AcAuth/modify', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};

export const deleteAuth = params => {
  return request('/Api/ac/AcAuth/remove', {
    method: 'POST',
    body: params,
  })
    .then(operateError)
    .then(operateSuccess)
    .then(operateBool);
};
