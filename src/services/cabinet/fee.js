/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import { message } from 'antd';
import request from '@/utils/request';
import { formatTableRequest } from '../_utils';

export async function getCabinetTakeList(params) {
  const { current, ...rest } = params;

  return formatTableRequest('/Api/YZ/CourierStation/subList', {
    ...rest,
    page: current,
    is_district: '0',
  });
}

export async function setCabinetSizePrice(params) {
  return request('/Api/YZ/Cabinet/revisePrice', {
    method: 'POST',
    data: params,
  }).then(({ code, msg }) => {
    const status = `${code}` === '0';
    if (status) {
      message.info('设置成功');
    } else {
      message.error(msg);
    }
    return status;
  });
}

export async function getCabinetSizePrice(params) {
  return request('/Api/YZ/Cabinet/cabinetPrice', {
    method: 'POST',
    data: params,
  }).then(({ data }) => {
    return Array.isArray(data) ? data : [];
  });
}

// 保管费 -- 品牌折扣 -- 获取
export async function getCabinetBrandDiscount(params) {
  return request('/Api/YZ/Cabinet/brandDiscount', {
    method: 'POST',
    data: params,
  }).then(({ data }) => data);
}
// 保管费 -- 品牌折扣 -- 设置
export async function setCabinetBrandDiscount(params) {
  return request('/Api/YZ/Cabinet/setBrandDiscount', {
    method: 'POST',
    data: params,
  }).then(({ code, msg }) => {
    const status = `${code}` === '0';
    if (status) {
      message.info('设置成功');
    } else {
      message.error(msg);
    }
    return status;
  });
}

// 超时费 -- 品牌折扣 -- 获取
export async function getCabinetBrandOutTimeFee(params) {
  return request('/Api/YZ/Cabinet/getBrandOutTimeFee', {
    method: 'POST',
    data: params,
  }).then(({ data }) => data);
}
// 超时费 -- 品牌折扣 -- 设置
export async function setCabinetBrandOutTimeFee(params) {
  return request('/Api/YZ/Cabinet/setBrandOutTimeFee', {
    method: 'POST',
    data: params,
  }).then(({ code, msg }) => {
    const status = `${code}` === '0';
    if (status) {
      message.info('设置成功');
    } else {
      message.error(msg);
    }
    return status;
  });
}

// 获取快递柜品牌列表
export async function getCabinetBrandList(params) {
  return request('/Api/YZ/Cabinet/getBrandList', {
    method: 'POST',
    data: params,
  }).then(({ data }) => (Array.isArray(data) ? data : []));
}

export async function setCabinetOutTimeFee(params) {
  return request('/Api/YZ/Cabinet/setOutTimeFee', {
    method: 'POST',
    data: params,
  }).then(({ code, msg }) => {
    const status = `${code}` === '0';
    if (status) {
      message.info('设置成功');
    } else {
      message.error(msg);
    }
    return status;
  });
}

export async function getCabinetOutTimeFee(params) {
  return request('/Api/YZ/Cabinet/getOutTimeFee', {
    method: 'POST',
    data: params,
  }).then(({ data }) => data || {});
}

export async function setCabinetOutTimeRake(params) {
  return request('/Api/YZ/Cabinet/setOutTimeRake', {
    method: 'POST',
    data: params,
  }).then(({ code, msg }) => {
    const status = `${code}` === '0';
    if (status) {
      message.info('设置成功');
    } else {
      message.error(msg);
    }
    return status;
  });
}

export async function getCabinetOutTimeRake(params) {
  return request('/Api/YZ/Cabinet/getOutTimeRake', {
    method: 'POST',
    data: params,
  }).then(({ data }) => data || {});
}

export async function prohibitSetFee(params) {
  return request('/Api/YZ/Cabinet/prohibitSetFee', {
    method: 'POST',
    data: params,
  }).then(({ code, msg }) => {
    const status = `${code}` === '0';
    if (status) {
      message.info('设置成功');
    } else {
      message.error(msg);
    }
    return status;
  });
}
