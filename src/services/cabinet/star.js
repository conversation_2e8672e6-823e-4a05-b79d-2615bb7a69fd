/**
 * Copyright (c) 快宝网络 kuaidihelp.com Co., Ltd. All Rights Reserved 禁止外泄以及用于其它的商业用途
 */

import request from '@/utils/request';

export async function getAreaEcCurrentDayStat(params) {
  return request('/Api/Statistics/getAreaEcCurrentDayStat', {
    method: 'POST',
    data: params,
  }).then(({ code, data }) => {
    const status = `${code}` === '0';
    return status ? data || {} : {};
  });
}

export async function getAreaEcInOutStat(params) {
  return request('/Api/Statistics/getAreaEcInOutStat', {
    method: 'POST',
    data: params,
  }).then(({ data = {}, ...rest }) => {
    const list = Object.keys(data).map(i => {
      const item = data[i] || {};
      return {
        date: i,
        in_num: item.storage_num || 0,
        out_num: item.out_num || 0,
        back_num: item.back_num || 0,
      };
    });
    return { ...rest, data: list };
  });
}

export async function getAreaEcBrandStat(params) {
  return request('/Api/Statistics/getAreaEcBrandStat', {
    method: 'POST',
    data: params,
  }).then(({ data = {} }) => {
    const { storage_list, out_list, send_list } = data;
    return {
      inBrandPieList: Array.isArray(storage_list) ? storage_list : [],
      outBrandPieList: Array.isArray(out_list) ? out_list : [],
      orderBrandPieList: Array.isArray(send_list) ? send_list : [],
    };
  });
}

export async function getAreaEcIncomeStat(params) {
  return request('/Api/Statistics/getAreaEcIncomeStat', {
    method: 'POST',
    data: params,
  }).then(({ data = {}, ...rest }) => {
    const list = Object.keys(data).map(i => {
      return {
        day: i,
        cnt: data[i],
      };
    });
    return { ...rest, data: list };
  });
}

export async function getAreaEcSendStat(params) {
  return request('/Api/Statistics/getAreaEcSendStat', {
    method: 'POST',
    data: params,
  }).then(({ data = {}, ...rest }) => {
    const list = Object.keys(data).map(i => {
      return {
        date: i,
        num: (data[i] || 0) * 1,
      };
    });
    return { ...rest, data: list };
  });
}
